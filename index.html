

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nexus | HR, Payroll & IT Automation Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        navy: '#0A2342',
                        teal: '#2DD4BF',
                        purple: '#8B5CF6',
                        lightpurple: '#A78BFA',
                        darkpurple: '#7C3AED'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #8B5CF6 0%, #2DD4BF 100%);
        }
        .gradient-text {
            background: linear-gradient(135deg, #8B5CF6 0%, #2DD4BF 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .nav-link {
            position: relative;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background: linear-gradient(135deg, #8B5CF6 0%, #2DD4BF 100%);
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }
        .active-nav::after {
            width: 100%;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .pricing-card:hover {
            transform: translateY(-8px);
        }
        .integration-logo {
            filter: grayscale(100%);
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        .integration-logo:hover {
            filter: grayscale(0%);
            opacity: 1;
        }
        .animated-line {
            stroke-dasharray: 1000;
            stroke-dashoffset: 1000;
            animation: dash 3s linear forwards;
        }
        @keyframes dash {
            to {
                stroke-dashoffset: 0;
            }
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center">
                <svg class="h-10 w-10 text-purple" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 5L30 10V30L20 35L10 30V10L20 5Z" fill="currentColor" fill-opacity="0.8"/>
                    <path d="M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z" fill="white"/>
                </svg>
                <span class="ml-2 text-2xl font-bold text-navy font-poppins">Nexus</span>
            </div>
            
            <div class="hidden md:flex space-x-8">
                <a href="#home" class="nav-link active-nav text-navy font-medium hover:text-purple transition-colors">Home</a>
                <a href="#features" class="nav-link text-navy font-medium hover:text-purple transition-colors">Features</a>
                <a href="#solutions" class="nav-link text-navy font-medium hover:text-purple transition-colors">Solutions</a>
                <a href="#integrations" class="nav-link text-navy font-medium hover:text-purple transition-colors">Integrations</a>
                <a href="#pricing" class="nav-link text-navy font-medium hover:text-purple transition-colors">Pricing</a>
                <a href="#about" class="nav-link text-navy font-medium hover:text-purple transition-colors">About</a>
                <a href="#contact" class="nav-link text-navy font-medium hover:text-purple transition-colors">Contact</a>
            </div>
            
            <div class="hidden md:flex items-center space-x-4">
                <a href="#" class="text-navy font-medium hover:text-purple transition-colors">Login</a>
                <a href="#" class="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg">Get Started</a>
            </div>
            
            <button id="mobile-menu-button" class="md:hidden text-navy">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
            </button>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white shadow-lg absolute w-full">
            <div class="container mx-auto px-4 py-3 flex flex-col space-y-3">
                <a href="#home" class="text-navy font-medium py-2 hover:text-purple transition-colors">Home</a>
                <a href="#features" class="text-navy font-medium py-2 hover:text-purple transition-colors">Features</a>
                <a href="#solutions" class="text-navy font-medium py-2 hover:text-purple transition-colors">Solutions</a>
                <a href="#integrations" class="text-navy font-medium py-2 hover:text-purple transition-colors">Integrations</a>
                <a href="#pricing" class="text-navy font-medium py-2 hover:text-purple transition-colors">Pricing</a>
                <a href="#about" class="text-navy font-medium py-2 hover:text-purple transition-colors">About</a>
                <a href="#contact" class="text-navy font-medium py-2 hover:text-purple transition-colors">Contact</a>
                <div class="flex flex-col space-y-3 pt-3 border-t border-gray-200">
                    <a href="#" class="text-navy font-medium hover:text-purple transition-colors">Login</a>
                    <a href="#" class="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md">Get Started</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Home Section -->
    <section id="home" class="pt-28 pb-20 md:pt-32 md:pb-24">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0">
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins">
                        The Future of Work <span class="gradient-text">Starts Here</span>
                    </h1>
                    <p class="text-lg text-gray-600 mb-8 max-w-lg">
                        Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="#" class="gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                            Request Demo
                        </a>
                        <a href="#" class="border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors">
                            Learn More
                        </a>
                    </div>
                    <div class="mt-10 flex items-center">
                        <div class="flex -space-x-2">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-10 h-10 rounded-full border-2 border-white">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-10 h-10 rounded-full border-2 border-white">
                            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-10 h-10 rounded-full border-2 border-white">
                            <div class="w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium">+5</div>
                        </div>
                        <p class="ml-4 text-gray-600">Trusted by 1000+ companies worldwide</p>
                    </div>
                </div>
                <div class="md:w-1/2">
                    <div class="relative">
                        <div class="absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full"></div>
                        <div class="absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full"></div>
                        <div class="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100">
                            <div class="bg-navy p-4 flex justify-between items-center">
                                <div class="flex space-x-2">
                                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                </div>
                                <div class="text-white text-sm">Nexus Dashboard</div>
                                <div></div>
                            </div>
                            <div class="p-6">
                                <div class="flex justify-between items-center mb-6">
                                    <div>
                                        <h3 class="text-navy font-semibold">Welcome back, Sarah</h3>
                                        <p class="text-gray-500 text-sm">Monday, June 12</p>
                                    </div>
                                    <div class="bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium">
                                        Admin
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-4 mb-6">
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="text-purple text-xl font-bold">24</div>
                                        <div class="text-gray-500 text-sm">New Hires</div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="text-teal text-xl font-bold">98%</div>
                                        <div class="text-gray-500 text-sm">Onboarding</div>
                                    </div>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <div class="text-navy text-xl font-bold">12</div>
                                        <div class="text-gray-500 text-sm">Approvals</div>
                                    </div>
                                </div>
                                <div class="mb-6">
                                    <div class="flex justify-between items-center mb-2">
                                        <h4 class="font-medium text-navy">Onboarding Progress</h4>
                                        <span class="text-sm text-purple">View All</span>
                                    </div>
                                    <div class="space-y-3">
                                        <div>
                                            <div class="flex justify-between text-sm mb-1">
                                                <span>Emily Johnson</span>
                                                <span>75%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-purple h-2 rounded-full" style="width: 75%"></div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="flex justify-between text-sm mb-1">
                                                <span>Michael Chen</span>
                                                <span>90%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2">
                                                <div class="bg-teal h-2 rounded-full" style="width: 90%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <button class="text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        New Task
                                    </button>
                                    <button class="text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                        Run Payroll
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Powerful Features</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Our comprehensive platform streamlines your HR, payroll, and IT operations with powerful automation and intuitive workflows.</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
                    <div class="w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-navy mb-3">Seamless Onboarding</h3>
                    <p class="text-gray-600 mb-4">Automate employee onboarding with customizable workflows, digital paperwork, and integrated background checks.</p>
                    <ul class="space-y-2 mb-5">
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Digital document signing
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Automated task assignment
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Progress tracking dashboard
                        </li>
                    </ul>
                    <a href="#" class="text-purple font-medium flex items-center hover:text-darkpurple transition-colors">
                        Learn more
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
                
                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
                    <div class="w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-navy mb-3">Smart Payroll</h3>
                    <p class="text-gray-600 mb-4">Process payroll with precision and compliance across multiple countries, currencies, and tax jurisdictions.</p>
                    <ul class="space-y-2 mb-5">
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Automatic tax calculations
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Multi-currency support
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Compliance monitoring
                        </li>
                    </ul>
                    <a href="#" class="text-teal font-medium flex items-center hover:text-darkpurple transition-colors">
                        Learn more
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
                
                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
                    <div class="w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-navy mb-3">IT Provisioning</h3>
                    <p class="text-gray-600 mb-4">Automate device setup, software licensing, and access management for new and existing employees.</p>
                    <ul class="space-y-2 mb-5">
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            One-click app provisioning
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Device management
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Security compliance
                        </li>
                    </ul>
                    <a href="#" class="text-purple font-medium flex items-center hover:text-darkpurple transition-colors">
                        Learn more
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
                
                <!-- Feature 4 -->
                <div class="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
                    <div class="w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-navy mb-3">Finance Management</h3>
                    <p class="text-gray-600 mb-4">Streamline expense tracking, budgeting, and financial reporting with powerful automation tools.</p>
                    <ul class="space-y-2 mb-5">
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Expense approval workflows
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Budget tracking
                        </li>
                        <li class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            Financial reporting
                        </li>
                    </ul>
                    <a href="#" class="text-teal font-medium flex items-center hover:text-darkpurple transition-colors">
                        Learn more
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Solutions Section -->
    <section id="solutions" class="py-20">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Tailored Solutions</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Our platform adapts to your organization's unique needs, whether you're a startup or a global enterprise.</p>
            </div>
            
            <div class="flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
                <div class="w-full md:w-1/4">
                    <div class="bg-navy text-white p-6 rounded-xl h-full">
                        <h3 class="text-xl font-semibold mb-4">Choose Your Solution</h3>
                        <p class="text-gray-300 mb-6">Select the solution that best fits your organization's size and needs.</p>
                        
                        <div class="space-y-3">
                            <button class="solution-tab w-full text-left py-3 px-4 rounded-lg bg-purple/20 border-l-4 border-purple font-medium" data-tab="startups">
                                Startups
                            </button>
                            <button class="solution-tab w-full text-left py-3 px-4 rounded-lg hover:bg-white/10 transition-colors" data-tab="enterprises">
                                Enterprises
                            </button>
                            <button class="solution-tab w-full text-left py-3 px-4 rounded-lg hover:bg-white/10 transition-colors" data-tab="global">
                                Global Teams
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="w-full md:w-3/4">
                    <!-- Startups Tab -->
                    <div id="startups-tab" class="tab-content active bg-white p-8 rounded-xl shadow-lg h-full">
                        <div class="flex flex-col md:flex-row">
                            <div class="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                                <span class="text-sm font-medium text-purple bg-purple/10 px-3 py-1 rounded-full">For Startups</span>
                                <h3 class="text-2xl font-bold text-navy mt-4 mb-4">Scale your team without scaling overhead</h3>
                                <p class="text-gray-600 mb-6">Perfect for growing teams that need to establish efficient HR and IT processes without dedicated departments.</p>
                                
                                <ul class="space-y-4 mb-8">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Quick Implementation</h4>
                                            <p class="text-gray-600 text-sm">Get up and running in days, not months</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Scalable Pricing</h4>
                                            <p class="text-gray-600 text-sm">Pay only for what you need as you grow</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Essential Integrations</h4>
                                            <p class="text-gray-600 text-sm">Connect with the tools startups love</p>
                                        </div>
                                    </li>
                                </ul>
                                
                                <a href="#" class="inline-block bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Learn More
                                </a>
                            </div>
                            <div class="md:w-1/2">
                                <div class="bg-gray-50 rounded-xl p-6 h-full">
                                    <div class="flex justify-between items-center mb-6">
                                        <h4 class="font-semibold text-navy">Startup Success Story</h4>
                                        <div class="text-purple">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-teal/20 rounded-full flex items-center justify-center mr-4">
                                            <span class="text-teal font-bold">TL</span>
                                        </div>
                                        <div>
                                            <h5 class="font-medium">TechLaunch</h5>
                                            <p class="text-sm text-gray-500">SaaS Startup, 45 employees</p>
                                        </div>
                                    </div>
                                    
                                    <blockquote class="text-gray-600 italic mb-6">
                                        "Nexus helped us scale from 10 to 45 employees in just 8 months without hiring a dedicated HR team. The automated onboarding and IT provisioning saved us countless hours."
                                    </blockquote>
                                    
                                    <div class="flex items-center">
                                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-8 h-8 rounded-full">
                                        <div class="ml-3">
                                            <h6 class="font-medium text-sm">Sarah Chen</h6>
                                            <p class="text-xs text-gray-500">Co-founder & COO</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Enterprises Tab -->
                    <div id="enterprises-tab" class="tab-content bg-white p-8 rounded-xl shadow-lg h-full">
                        <div class="flex flex-col md:flex-row">
                            <div class="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                                <span class="text-sm font-medium text-teal bg-teal/10 px-3 py-1 rounded-full">For Enterprises</span>
                                <h3 class="text-2xl font-bold text-navy mt-4 mb-4">Enterprise-grade solutions with unmatched flexibility</h3>
                                <p class="text-gray-600 mb-6">Designed for large organizations that need robust, customizable HR and IT systems with advanced security and compliance features.</p>
                                
                                <ul class="space-y-4 mb-8">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Advanced Customization</h4>
                                            <p class="text-gray-600 text-sm">Tailor workflows to your organization's structure</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Enterprise Security</h4>
                                            <p class="text-gray-600 text-sm">SOC 2, GDPR, and HIPAA compliant</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Dedicated Support</h4>
                                            <p class="text-gray-600 text-sm">24/7 priority support and implementation team</p>
                                        </div>
                                    </li>
                                </ul>
                                
                                <a href="#" class="inline-block bg-teal hover:bg-teal/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Learn More
                                </a>
                            </div>
                            <div class="md:w-1/2">
                                <div class="bg-gray-50 rounded-xl p-6 h-full">
                                    <div class="flex justify-between items-center mb-6">
                                        <h4 class="font-semibold text-navy">Enterprise Success Story</h4>
                                        <div class="text-teal">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-navy/20 rounded-full flex items-center justify-center mr-4">
                                            <span class="text-navy font-bold">GI</span>
                                        </div>
                                        <div>
                                            <h5 class="font-medium">Global Innovations</h5>
                                            <p class="text-sm text-gray-500">Manufacturing, 2,500+ employees</p>
                                        </div>
                                    </div>
                                    
                                    <blockquote class="text-gray-600 italic mb-6">
                                        "Implementing Nexus across our 12 global offices streamlined our HR operations and reduced onboarding time by 65%. The customizable workflows and robust security features were exactly what we needed."
                                    </blockquote>
                                    
                                    <div class="flex items-center">
                                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-8 h-8 rounded-full">
                                        <div class="ml-3">
                                            <h6 class="font-medium text-sm">Michael Rodriguez</h6>
                                            <p class="text-xs text-gray-500">Chief HR Officer</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Global Teams Tab -->
                    <div id="global-tab" class="tab-content bg-white p-8 rounded-xl shadow-lg h-full">
                        <div class="flex flex-col md:flex-row">
                            <div class="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                                <span class="text-sm font-medium text-purple bg-purple/10 px-3 py-1 rounded-full">For Global Teams</span>
                                <h3 class="text-2xl font-bold text-navy mt-4 mb-4">Manage international teams with ease</h3>
                                <p class="text-gray-600 mb-6">Purpose-built for organizations with employees across multiple countries, handling complex compliance and payroll requirements.</p>
                                
                                <ul class="space-y-4 mb-8">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Global Compliance</h4>
                                            <p class="text-gray-600 text-sm">Stay compliant in 50+ countries</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Multi-Currency Payroll</h4>
                                            <p class="text-gray-600 text-sm">Pay employees in their local currency</p>
                                        </div>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <h4 class="font-medium text-navy">Localized Experience</h4>
                                            <p class="text-gray-600 text-sm">Platform available in 20+ languages</p>
                                        </div>
                                    </li>
                                </ul>
                                
                                <a href="#" class="inline-block bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors">
                                    Learn More
                                </a>
                            </div>
                            <div class="md:w-1/2">
                                <div class="bg-gray-50 rounded-xl p-6 h-full">
                                    <div class="flex justify-between items-center mb-6">
                                        <h4 class="font-semibold text-navy">Global Team Success Story</h4>
                                        <div class="text-purple">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                            </svg>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center mb-4">
                                        <div class="w-12 h-12 bg-purple/20 rounded-full flex items-center justify-center mr-4">
                                            <span class="text-purple font-bold">WN</span>
                                        </div>
                                        <div>
                                            <h5 class="font-medium">WorldNet</h5>
                                            <p class="text-sm text-gray-500">Tech Company, 800+ employees in 15 countries</p>
                                        </div>
                                    </div>
                                    
                                    <blockquote class="text-gray-600 italic mb-6">
                                        "Managing teams across 15 countries used to be a compliance nightmare. Nexus simplified everything from local tax calculations to time-off policies while giving our employees a consistent experience."
                                    </blockquote>
                                    
                                    <div class="flex items-center">
                                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%238B5CF6'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" class="w-8 h-8 rounded-full">
                                        <div class="ml-3">
                                            <h6 class="font-medium text-sm">Emma Patel</h6>
                                            <p class="text-xs text-gray-500">Global HR Director</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Integrations Section -->
    <section id="integrations" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Seamless Integrations</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Connect with your favorite tools and services for a unified workflow experience.</p>
            </div>
            
            <div class="bg-white rounded-2xl shadow-lg p-8 md:p-12">
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8 mb-12">
                    <!-- Integration Logos -->
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M25 5H5C3.89543 5 3 5.89543 3 7V23C3 24.1046 3.89543 25 5 25H25C26.1046 25 27 24.1046 27 23V7C27 5.89543 26.1046 5 25 5Z" fill="#4285F4"/>
                            <path d="M55 5H35C33.8954 5 33 5.89543 33 7V23C33 24.1046 33.8954 25 35 25H55C56.1046 25 57 24.1046 57 23V7C57 5.89543 56.1046 5 55 5Z" fill="#34A853"/>
                            <path d="M85 5H65C63.8954 5 63 5.89543 63 7V23C63 24.1046 63.8954 25 65 25H85C86.1046 25 87 24.1046 87 23V7C87 5.89543 86.1046 5 85 5Z" fill="#FBBC05"/>
                            <path d="M115 5H95C93.8954 5 93 5.89543 93 7V23C93 24.1046 93.8954 25 95 25H115C116.105 25 117 24.1046 117 23V7C117 5.89543 116.105 5 115 5Z" fill="#EA4335"/>
                            <path d="M15 15C15 12.7909 16.7909 11 19 11C21.2091 11 23 12.7909 23 15C23 17.2091 21.2091 19 19 19C16.7909 19 15 17.2091 15 15Z" fill="white"/>
                            <path d="M45 15C45 12.7909 46.7909 11 49 11C51.2091 11 53 12.7909 53 15C53 17.2091 51.2091 19 49 19C46.7909 19 45 17.2091 45 15Z" fill="white"/>
                            <path d="M75 15C75 12.7909 76.7909 11 79 11C81.2091 11 83 12.7909 83 15C83 17.2091 81.2091 19 79 19C76.7909 19 75 17.2091 75 15Z" fill="white"/>
                            <path d="M105 15C105 12.7909 106.791 11 109 11C111.209 11 113 12.7909 113 15C113 17.2091 111.209 19 109 19C106.791 19 105 17.2091 105 15Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Google Workspace</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="3" y="3" width="114" height="24" rx="4" fill="#4A154B"/>
                            <path d="M35 10C33.3431 10 32 11.3431 32 13C32 14.6569 33.3431 16 35 16V20C35 20.5523 35.4477 21 36 21H40C40 22.6569 41.3431 24 43 24C44.6569 24 46 22.6569 46 21C47.6569 21 49 19.6569 49 18C49 16.3431 47.6569 15 46 15H42V13C42 11.3431 40.6569 10 39 10H35Z" fill="white"/>
                            <path d="M43 10C44.6569 10 46 8.65685 46 7C46 5.34315 44.6569 4 43 4C41.3431 4 40 5.34315 40 7H36C35.4477 7 35 7.44772 35 8V12H39C40.6569 12 42 10.6569 42 9V9H43Z" fill="#2EB67D"/>
                            <path d="M85 20C86.6569 20 88 18.6569 88 17C88 15.3431 86.6569 14 85 14H81V10C81 9.44772 80.5523 9 80 9H76C76 7.34315 74.6569 6 73 6C71.3431 6 70 7.34315 70 9C68.3431 9 67 10.3431 67 12C67 13.6569 68.3431 15 70 15H74V17C74 18.6569 75.3431 20 77 20H85Z" fill="white"/>
                            <path d="M77 24C75.3431 24 74 25.3431 74 27C74 28.6569 75.3431 30 77 30C78.6569 30 80 28.6569 80 27H84C84.5523 27 85 26.5523 85 26V22H81C79.3431 22 78 23.3431 78 25V25H77Z" fill="#E01E5A"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Slack</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5C46.1929 5 35 16.1929 35 30H85C85 16.1929 73.8071 5 60 5Z" fill="#2684FF"/>
                            <path d="M82.5 12.5L65 30H85C85 23.0964 84.2393 16.5393 82.5 12.5Z" fill="#0052CC"/>
                            <path d="M65 30L47.5 12.5C45.7607 16.5393 45 23.0964 45 30H65Z" fill="#2684FF"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Jira</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5L85 30H35L60 5Z" fill="#0061D5"/>
                            <rect x="47.5" y="12.5" width="25" height="10" fill="#00A2F5"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Box</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5C46.1929 5 35 16.1929 35 30H85C85 16.1929 73.8071 5 60 5Z" fill="#0066FF"/>
                            <circle cx="60" cy="17.5" r="7.5" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Zoom</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M35 7.5C35 6.11929 36.1193 5 37.5 5H82.5C83.8807 5 85 6.11929 85 7.5V22.5C85 23.8807 83.8807 25 82.5 25H37.5C36.1193 25 35 23.8807 35 22.5V7.5Z" fill="#2EB67D"/>
                            <path d="M45 12.5L50 17.5L45 22.5V12.5Z" fill="white"/>
                            <path d="M55 12.5H75V17.5H55V12.5Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">QuickBooks</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5L85 30H35L60 5Z" fill="#FF5722"/>
                            <path d="M60 15L70 30H50L60 15Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">GitLab</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="35" y="5" width="50" height="20" rx="2" fill="#1F76C2"/>
                            <path d="M60 10L65 15L60 20L55 15L60 10Z" fill="white"/>
                            <path d="M50 10L55 15L50 20L45 15L50 10Z" fill="white"/>
                            <path d="M70 10L75 15L70 20L65 15L70 10Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Dropbox</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5C46.1929 5 35 16.1929 35 30H85C85 16.1929 73.8071 5 60 5Z" fill="#6264A7"/>
                            <path d="M55 15L65 15L65 25L55 25L55 15Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Microsoft Teams</span>
                    </div>
                    
                    <div class="flex items-center justify-center p-4 border border-gray-100 rounded-lg hover:shadow-md transition-shadow integration-logo">
                        <svg class="h-10 w-auto" viewBox="0 0 120 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M60 5C46.1929 5 35 16.1929 35 30H85C85 16.1929 73.8071 5 60 5Z" fill="#FF6B00"/>
                            <path d="M50 15H70V25H50V15Z" fill="white"/>
                        </svg>
                        <span class="ml-2 font-medium text-gray-700">Zendesk</span>
                    </div>
                </div>
                
                <div class="flex flex-col md:flex-row items-center justify-between bg-gray-50 p-6 rounded-xl">
                    <div class="mb-4 md:mb-0 md:mr-8">
                        <h3 class="text-xl font-semibold text-navy mb-2">Need a custom integration?</h3>
                        <p class="text-gray-600">Our platform connects with 200+ apps and services. Can't find what you need? Let us know.</p>
                    </div>
                    <a href="#" class="whitespace-nowrap bg-navy hover:bg-navy/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Request Integration
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-20">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Simple, Transparent Pricing</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Choose the plan that works best for your business needs and scale as you grow.</p>
                
                <div class="flex justify-center mt-8 mb-12">
                    <div class="bg-gray-100 p-1 rounded-full inline-flex">
                        <button class="pricing-toggle px-6 py-2 rounded-full bg-purple text-white font-medium">Monthly</button>
                        <button class="pricing-toggle px-6 py-2 rounded-full text-gray-700 font-medium">Annual (Save 20%)</button>
                    </div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Basic Plan -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden pricing-card transition-all duration-300">
                    <div class="p-8 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-navy mb-4">Basic</h3>
                        <div class="flex items-baseline mb-4">
                            <span class="text-4xl font-bold text-navy">$12</span>
                            <span class="text-gray-500 ml-2">per employee / month</span>
                        </div>
                        <p class="text-gray-600">Perfect for small teams just getting started with HR automation.</p>
                    </div>
                    <div class="p-8">
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Core HR management</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Basic onboarding workflows</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Time-off management</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">5 integrations</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Email support</span>
                            </li>
                            <li class="flex items-start text-gray-400">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Advanced IT provisioning</span>
                            </li>
                            <li class="flex items-start text-gray-400">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Custom workflows</span>
                            </li>
                        </ul>
                        <a href="#" class="block text-center bg-white border-2 border-purple text-purple hover:bg-purple hover:text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Get Started
                        </a>
                    </div>
                </div>
                
                <!-- Pro Plan -->
                <div class="bg-white rounded-2xl shadow-xl overflow-hidden pricing-card transition-all duration-300 transform md:-translate-y-4 relative">
                    <div class="absolute top-0 right-0">
                        <div class="bg-purple text-white text-xs font-bold px-3 py-1 rounded-bl-lg">
                            MOST POPULAR
                        </div>
                    </div>
                    <div class="p-8 border-b border-gray-100 bg-purple/5">
                        <h3 class="text-xl font-bold text-navy mb-4">Pro</h3>
                        <div class="flex items-baseline mb-4">
                            <span class="text-4xl font-bold text-navy">$24</span>
                            <span class="text-gray-500 ml-2">per employee / month</span>
                        </div>
                        <p class="text-gray-600">Ideal for growing businesses that need advanced HR and IT automation.</p>
                    </div>
                    <div class="p-8">
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Everything in Basic</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Advanced onboarding workflows</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Full payroll automation</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Basic IT provisioning</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">20 integrations</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Priority support</span>
                            </li>
                            <li class="flex items-start text-gray-400">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                                <span>Dedicated account manager</span>
                            </li>
                        </ul>
                        <a href="#" class="block text-center bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Get Started
                        </a>
                    </div>
                </div>
                
                <!-- Enterprise Plan -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden pricing-card transition-all duration-300">
                    <div class="p-8 border-b border-gray-100">
                        <h3 class="text-xl font-bold text-navy mb-4">Enterprise</h3>
                        <div class="flex items-baseline mb-4">
                            <span class="text-4xl font-bold text-navy">Custom</span>
                        </div>
                        <p class="text-gray-600">For large organizations with complex HR and IT requirements.</p>
                    </div>
                    <div class="p-8">
                        <ul class="space-y-4 mb-8">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Everything in Pro</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Advanced IT provisioning</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Custom workflows</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Unlimited integrations</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Dedicated account manager</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">24/7 priority support</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <span class="text-gray-600">Custom contract terms</span>
                            </li>
                        </ul>
                        <a href="#" class="block text-center bg-white border-2 border-teal text-teal hover:bg-teal hover:text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="mt-16 bg-gray-50 rounded-xl p-8 text-center">
                <h3 class="text-xl font-semibold text-navy mb-4">Need a custom plan?</h3>
                <p class="text-gray-600 mb-6 max-w-2xl mx-auto">Our team can create a tailored solution to meet your organization's specific needs.</p>
                <a href="#" class="inline-block bg-navy hover:bg-navy/80 text-white px-8 py-3 rounded-lg font-medium transition-colors">
                    Contact Our Sales Team
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">About Nexus</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">We're on a mission to transform how organizations manage their workforce and operations.</p>
            </div>
            
            <div class="flex flex-col md:flex-row mb-16">
                <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                    <h3 class="text-2xl font-bold text-navy mb-4">Our Vision</h3>
                    <p class="text-gray-600 mb-6">We envision a world where HR, payroll, and IT operations are seamlessly integrated, allowing organizations to focus on what matters most: their people and their mission.</p>
                    
                    <h3 class="text-2xl font-bold text-navy mb-4">Our Mission</h3>
                    <p class="text-gray-600 mb-6">To build technology that simplifies complex workforce management processes, empowering organizations of all sizes to operate more efficiently and create better employee experiences.</p>
                    
                    <div class="grid grid-cols-2 gap-6 mt-8">
                        <div class="text-center">
                            <div class="text-purple text-4xl font-bold mb-2">500+</div>
                            <p class="text-gray-600">Clients Worldwide</p>
                        </div>
                        <div class="text-center">
                            <div class="text-purple text-4xl font-bold mb-2">50+</div>
                            <p class="text-gray-600">Countries Served</p>
                        </div>
                        <div class="text-center">
                            <div class="text-purple text-4xl font-bold mb-2">98%</div>
                            <p class="text-gray-600">Client Retention</p>
                        </div>
                        <div class="text-center">
                            <div class="text-purple text-4xl font-bold mb-2">200+</div>
                            <p class="text-gray-600">Team Members</p>
                        </div>
                    </div>
                </div>
                
                <div class="md:w-1/2">
                    <div class="bg-white p-8 rounded-xl shadow-lg">
                        <h3 class="text-2xl font-bold text-navy mb-6">Our Values</h3>
                        
                        <div class="space-y-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-lg font-semibold text-navy">Innovation</h4>
                                    <p class="text-gray-600">We constantly push boundaries to create solutions that anticipate tomorrow's challenges.</p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-teal/10 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-lg font-semibold text-navy">People First</h4>
                                    <p class="text-gray-600">We believe that technology should serve people, not the other way around.</p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-lg font-semibold text-navy">Security & Trust</h4>
                                    <p class="text-gray-600">We handle sensitive data with the utmost care and transparency.</p>
                                </div>
                            </div>
                            
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-teal/10 rounded-lg flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-lg font-semibold text-navy">Global Perspective</h4>
                                    <p class="text-gray-600">We build solutions that work across cultures, countries, and regulatory environments.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div>
                <h3 class="text-2xl font-bold text-navy mb-8 text-center">Leadership Team</h3>
                
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
                    <!-- Team Member 1 -->
                    <div class="bg-white rounded-xl overflow-hidden shadow-lg">
                        <div class="h-48 bg-purple/10 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-purple/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-navy">Sarah Johnson</h4>
                            <p class="text-purple mb-4">CEO & Co-Founder</p>
                            <p class="text-gray-600 text-sm mb-4">Former VP of Product at Workday with 15+ years of experience in HR tech.</p>
                            <div class="flex space-x-3">
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                    </svg>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Team Member 2 -->
                    <div class="bg-white rounded-xl overflow-hidden shadow-lg">
                        <div class="h-48 bg-teal/10 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-teal/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-navy">Michael Chen</h4>
                            <p class="text-teal mb-4">CTO & Co-Founder</p>
                            <p class="text-gray-600 text-sm mb-4">Previously led engineering at Rippling and held senior roles at Google Cloud.</p>
                            <div class="flex space-x-3">
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                    </svg>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Team Member 3 -->
                    <div class="bg-white rounded-xl overflow-hidden shadow-lg">
                        <div class="h-48 bg-purple/10 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-purple/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold text-navy">Priya Sharma</h4>
                            <p class="text-purple mb-4">Chief Product Officer</p>
                            <p class="text-gray-600 text-sm mb-4">Product leader with experience at Zenefits and LinkedIn, focused on user experience.</p>
                            <div class="flex space-x-3">
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                                    </svg>
                                </a>
                                <a href="#" class="text-gray-400 hover:text-navy transition-colors">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Team Member 4 -->
                    <div class="bg-white rounded-xl overflow-hidden shadow-lg">
                        <div class="h-48 bg-teal/10 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 text-teal<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'961c2ff411f0a052',t:'MTc1Mjk0ODc0OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script>