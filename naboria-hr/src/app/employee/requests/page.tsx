'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function EmployeeRequests() {
  const [activeTab, setActiveTab] = useState('overview');
  const [showNewRequestModal, setShowNewRequestModal] = useState(false);
  const [requestType, setRequestType] = useState('');

  const myRequests = [
    {
      id: 1,
      type: 'Time Off',
      title: 'Vacation Request - Hawaii Trip',
      startDate: '2024-02-15',
      endDate: '2024-02-22',
      days: 6,
      status: 'Pending',
      submittedDate: '2024-01-10',
      approver: '<PERSON>',
      reason: 'Family vacation to Hawaii'
    },
    {
      id: 2,
      type: 'Expense',
      title: 'Conference Travel Expenses',
      amount: '$1,250.00',
      status: 'Approved',
      submittedDate: '2024-01-08',
      approver: '<PERSON>',
      description: 'Travel and accommodation for tech conference'
    },
    {
      id: 3,
      type: 'Equipment',
      title: 'New Monitor Request',
      item: '27" 4K Monitor',
      status: 'In Review',
      submittedDate: '2024-01-05',
      approver: 'IT Department',
      justification: 'Current monitor is too small for development work'
    },
    {
      id: 4,
      type: 'Training',
      title: 'AWS Certification Course',
      course: 'AWS Solutions Architect',
      cost: '$300',
      status: 'Approved',
      submittedDate: '2024-01-03',
      approver: 'Sarah Johnson'
    }
  ];

  const approvalRequests = [
    {
      id: 1,
      employee: 'John Smith',
      type: 'Time Off',
      title: 'Sick Leave',
      startDate: '2024-01-16',
      endDate: '2024-01-17',
      days: 2,
      status: 'Pending',
      submittedDate: '2024-01-15',
      reason: 'Medical appointment and recovery'
    },
    {
      id: 2,
      employee: 'Emily Davis',
      type: 'Expense',
      title: 'Client Dinner',
      amount: '$180.00',
      status: 'Pending',
      submittedDate: '2024-01-14',
      description: 'Business dinner with potential client'
    }
  ];

  const requestTypes = [
    { id: 'time-off', name: 'Time Off Request', icon: '🏖️', description: 'Request vacation, sick leave, or personal time' },
    { id: 'expense', name: 'Expense Reimbursement', icon: '💰', description: 'Submit business expenses for reimbursement' },
    { id: 'equipment', name: 'Equipment Request', icon: '💻', description: 'Request new equipment or hardware' },
    { id: 'training', name: 'Training Request', icon: '📚', description: 'Request training courses or certifications' },
    { id: 'schedule', name: 'Schedule Change', icon: '📅', description: 'Request changes to work schedule' },
    { id: 'remote', name: 'Remote Work', icon: '🏠', description: 'Request to work from home' }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'in review': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleApproval = (requestId: number, action: 'approve' | 'reject') => {
    console.log(`${action} request ${requestId}`);
    // This would typically make an API call
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/employee" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Requests & Approvals</h1>
                <p className="text-gray-600 text-sm">Manage your requests and approve team submissions</p>
              </div>
            </div>
            <button
              onClick={() => setShowNewRequestModal(true)}
              className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto"
            >
              New Request
            </button>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              
              <button
                onClick={() => setActiveTab('my-requests')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'my-requests' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                My Requests
                <span className="ml-auto bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {myRequests.length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('approvals')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'approvals' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Pending Approvals
                <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {approvalRequests.length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('history')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'history' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                History
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Total Requests</p>
                      <p className="text-2xl font-bold text-navy">{myRequests.length}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Pending</p>
                      <p className="text-2xl font-bold text-navy">
                        {myRequests.filter(r => r.status === 'Pending').length}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Approved</p>
                      <p className="text-2xl font-bold text-navy">
                        {myRequests.filter(r => r.status === 'Approved').length}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Need Approval</p>
                      <p className="text-2xl font-bold text-navy">{approvalRequests.length}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {requestTypes.map((type) => (
                    <button
                      key={type.id}
                      onClick={() => {
                        setRequestType(type.id);
                        setShowNewRequestModal(true);
                      }}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <div className="text-2xl mb-2">{type.icon}</div>
                      <h4 className="font-medium text-gray-900 mb-1">{type.name}</h4>
                      <p className="text-sm text-gray-500">{type.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {myRequests.slice(0, 3).map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-gray-900">{request.title}</p>
                        <p className="text-sm text-gray-500">
                          {request.type} • Submitted {request.submittedDate}
                        </p>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(request.status)}`}>
                        {request.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'my-requests' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">My Requests</h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {myRequests.map((request) => (
                    <div key={request.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{request.title}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(request.status)}`}>
                          {request.status}
                        </span>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Type:</span>
                          <span className="font-medium">{request.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Submitted:</span>
                          <span className="font-medium">{request.submittedDate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Approver:</span>
                          <span className="font-medium">{request.approver}</span>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          View Details
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          Edit
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Request
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Submitted
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Approver
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {myRequests.map((request) => (
                        <tr key={request.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{request.title}</div>
                              {request.type === 'Time Off' && (
                                <div className="text-sm text-gray-500">
                                  {request.startDate} to {request.endDate} ({request.days} days)
                                </div>
                              )}
                              {request.type === 'Expense' && (
                                <div className="text-sm text-gray-500">{request.amount}</div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(request.status)}`}>
                              {request.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.submittedDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {request.approver}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">View</button>
                            <button className="text-gray-400 hover:text-gray-600">Edit</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'approvals' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Pending Approvals</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {approvalRequests.map((request) => (
                    <div key={request.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-4 lg:space-y-0">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{request.title}</h4>
                          <p className="text-sm text-gray-500 mb-2">
                            {request.employee} • {request.type} • Submitted {request.submittedDate}
                          </p>
                          {request.type === 'Time Off' && (
                            <p className="text-sm text-gray-600">
                              {request.startDate} to {request.endDate} ({request.days} days)
                              <br />
                              Reason: {request.reason}
                            </p>
                          )}
                          {request.type === 'Expense' && (
                            <p className="text-sm text-gray-600">
                              Amount: {request.amount}
                              <br />
                              Description: {request.description}
                            </p>
                          )}
                        </div>
                        <div className="flex space-x-3">
                          <button
                            onClick={() => handleApproval(request.id, 'approve')}
                            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleApproval(request.id, 'reject')}
                            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                          >
                            Reject
                          </button>
                          <button className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors">
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* New Request Modal */}
      {showNewRequestModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-navy">New Request</h3>
              <button
                onClick={() => setShowNewRequestModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="space-y-3">
              {requestTypes.map((type) => (
                <button
                  key={type.id}
                  onClick={() => {
                    setRequestType(type.id);
                    setShowNewRequestModal(false);
                    // Navigate to specific request form
                  }}
                  className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                >
                  <div className="flex items-center">
                    <span className="text-xl mr-3">{type.icon}</span>
                    <div>
                      <p className="font-medium text-gray-900">{type.name}</p>
                      <p className="text-sm text-gray-500">{type.description}</p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
