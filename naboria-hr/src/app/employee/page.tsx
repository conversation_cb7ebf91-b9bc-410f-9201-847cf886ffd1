'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function EmployeeDashboard() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isClockingIn, setIsClockingIn] = useState(false);
  const [isClockingOut, setIsClockingOut] = useState(false);
  const [clockedIn, setClockedIn] = useState(false);
  const [clockInTime, setClockInTime] = useState<Date | null>(null);
  const [totalHoursToday, setTotalHoursToday] = useState(0);
  const [activeTab, setActiveTab] = useState('dashboard');

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Calculate hours worked today
  useEffect(() => {
    if (clockedIn && clockInTime) {
      const timer = setInterval(() => {
        const now = new Date();
        const diff = now.getTime() - clockInTime.getTime();
        const hours = diff / (1000 * 60 * 60);
        setTotalHoursToday(hours);
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [clockedIn, clockInTime]);

  const handleClockIn = async () => {
    setIsClockingIn(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setClockedIn(true);
    setClockInTime(new Date());
    setIsClockingIn(false);
  };

  const handleClockOut = async () => {
    setIsClockingOut(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setClockedIn(false);
    setClockInTime(null);
    setIsClockingOut(false);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatHours = (hours: number) => {
    const h = Math.floor(hours);
    const m = Math.floor((hours - h) * 60);
    return `${h}h ${m}m`;
  };

  const timeEntries = [
    { date: '2024-01-15', clockIn: '09:00 AM', clockOut: '05:30 PM', hours: 8.5, status: 'Complete' },
    { date: '2024-01-14', clockIn: '08:45 AM', clockOut: '05:15 PM', hours: 8.5, status: 'Complete' },
    { date: '2024-01-13', clockIn: '09:15 AM', clockOut: '05:45 PM', hours: 8.5, status: 'Complete' },
    { date: '2024-01-12', clockIn: '08:30 AM', clockOut: '05:00 PM', hours: 8.5, status: 'Complete' },
    { date: '2024-01-11', clockIn: '09:00 AM', clockOut: '05:30 PM', hours: 8.5, status: 'Complete' }
  ];

  const pendingTasks = [
    { id: 1, title: 'Complete Security Training', type: 'Training', dueDate: '2024-01-20', priority: 'High' },
    { id: 2, title: 'Submit Expense Report', type: 'Finance', dueDate: '2024-01-18', priority: 'Medium' },
    { id: 3, title: 'Update Emergency Contact', type: 'HR', dueDate: '2024-01-25', priority: 'Low' },
    { id: 4, title: 'Annual Performance Review', type: 'HR', dueDate: '2024-01-30', priority: 'High' }
  ];

  const upcomingEvents = [
    { id: 1, title: 'Team Meeting', date: '2024-01-16', time: '10:00 AM', type: 'Meeting' },
    { id: 2, title: 'Project Deadline', date: '2024-01-18', time: '05:00 PM', type: 'Deadline' },
    { id: 3, title: 'Company All-Hands', date: '2024-01-20', time: '02:00 PM', type: 'Event' },
    { id: 4, title: 'Training Session', date: '2024-01-22', time: '09:00 AM', type: 'Training' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/" className="flex items-center mr-4">
                <svg className="h-8 w-8 text-purple mr-3" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 5L30 10V30L20 35L10 30V10L20 5Z" fill="currentColor" fillOpacity="0.8"/>
                  <path d="M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z" fill="white"/>
                </svg>
                <span className="text-xl font-bold text-navy">Naboria Employee Portal</span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-600">{formatDate(currentTime)}</p>
                <p className="text-lg font-semibold text-navy">{formatTime(currentTime)}</p>
              </div>
              <div className="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center">
                <span className="text-purple font-medium">SC</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'dashboard' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0" />
                </svg>
                Dashboard
              </button>
              
              <button
                onClick={() => setActiveTab('timesheet')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'timesheet' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Time & Attendance
              </button>
              
              <Link
                href="/employee/onboarding"
                className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                Onboarding
              </Link>
              
              <Link
                href="/employee/requests"
                className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Requests
              </Link>
              
              <Link
                href="/employee/compliance"
                className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Compliance
              </Link>
              
              <Link
                href="/employee/profile"
                className="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                My Profile
              </Link>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Clock In/Out Section */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Time Clock</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="mb-4">
                      <div className="text-4xl font-bold text-navy mb-2">{formatTime(currentTime)}</div>
                      <div className="text-gray-600">{formatDate(currentTime)}</div>
                    </div>
                    
                    {!clockedIn ? (
                      <button
                        onClick={handleClockIn}
                        disabled={isClockingIn}
                        className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors flex items-center justify-center"
                      >
                        {isClockingIn ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Clocking In...
                          </>
                        ) : (
                          <>
                            <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Clock In
                          </>
                        )}
                      </button>
                    ) : (
                      <button
                        onClick={handleClockOut}
                        disabled={isClockingOut}
                        className="w-full bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-8 py-4 rounded-lg font-medium text-lg transition-colors flex items-center justify-center"
                      >
                        {isClockingOut ? (
                          <>
                            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Clocking Out...
                          </>
                        ) : (
                          <>
                            <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Clock Out
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  
                  <div className="space-y-4">
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Status:</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          clockedIn ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {clockedIn ? 'Clocked In' : 'Clocked Out'}
                        </span>
                      </div>
                    </div>
                    
                    {clockedIn && clockInTime && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Clock In Time:</span>
                          <span className="font-medium">{formatTime(clockInTime)}</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Hours Today:</span>
                        <span className="font-medium text-purple">{formatHours(totalHoursToday)}</span>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Hours This Week:</span>
                        <span className="font-medium">32h 15m</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">PTO Balance</p>
                      <p className="text-xl font-bold text-navy">15.5 days</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Tasks Complete</p>
                      <p className="text-xl font-bold text-navy">8/12</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Pending Actions</p>
                      <p className="text-xl font-bold text-navy">4</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Performance</p>
                      <p className="text-xl font-bold text-navy">94%</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pending Tasks & Upcoming Events */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-navy mb-4">Pending Tasks</h3>
                  <div className="space-y-3">
                    {pendingTasks.map((task) => (
                      <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{task.title}</p>
                          <p className="text-sm text-gray-500">{task.type} • Due: {task.dueDate}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          task.priority === 'High' ? 'bg-red-100 text-red-800' :
                          task.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {task.priority}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-navy mb-4">Upcoming Events</h3>
                  <div className="space-y-3">
                    {upcomingEvents.map((event) => (
                      <div key={event.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{event.title}</p>
                          <p className="text-sm text-gray-500">{event.date} at {event.time}</p>
                        </div>
                        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                          {event.type}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'timesheet' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
                  <h2 className="text-xl font-semibold text-navy">Time & Attendance</h2>
                  <div className="flex space-x-2">
                    <button className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors">
                      Export Timesheet
                    </button>
                    <button className="border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors">
                      Request Correction
                    </button>
                  </div>
                </div>

                {/* Weekly Summary */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-600">This Week</p>
                    <p className="text-2xl font-bold text-blue-800">32h 15m</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-green-600">Regular Hours</p>
                    <p className="text-2xl font-bold text-green-800">32h 15m</p>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <p className="text-sm text-orange-600">Overtime</p>
                    <p className="text-2xl font-bold text-orange-800">0h 0m</p>
                  </div>
                </div>

                {/* Time Entries Table */}
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock In</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clock Out</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {timeEntries.map((entry, index) => (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{entry.date}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{entry.clockIn}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{entry.clockOut}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{entry.hours}h</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                              {entry.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">Edit</button>
                            <button className="text-gray-400 hover:text-gray-600">View</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
