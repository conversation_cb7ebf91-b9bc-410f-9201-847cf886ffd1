'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function EmployeeCompliance() {
  const [activeTab, setActiveTab] = useState('overview');

  const complianceItems = [
    {
      id: 1,
      title: 'Security Awareness Training',
      category: 'Security',
      status: 'completed',
      dueDate: '2024-01-15',
      completedDate: '2024-01-10',
      description: 'Annual security awareness and phishing prevention training',
      priority: 'High',
      certificateUrl: '/certificates/security-2024.pdf'
    },
    {
      id: 2,
      title: 'Anti-Harassment Training',
      category: 'HR',
      status: 'completed',
      dueDate: '2024-02-01',
      completedDate: '2024-01-12',
      description: 'Workplace harassment prevention and reporting procedures',
      priority: 'High',
      certificateUrl: '/certificates/harassment-2024.pdf'
    },
    {
      id: 3,
      title: 'Data Privacy (GDPR) Training',
      category: 'Legal',
      status: 'overdue',
      dueDate: '2024-01-10',
      description: 'GDPR compliance and data protection regulations',
      priority: 'High'
    },
    {
      id: 4,
      title: 'Code of Conduct Acknowledgment',
      category: 'HR',
      status: 'pending',
      dueDate: '2024-01-25',
      description: 'Annual acknowledgment of company code of conduct',
      priority: 'Medium'
    },
    {
      id: 5,
      title: 'Safety Training',
      category: 'Safety',
      status: 'pending',
      dueDate: '2024-02-15',
      description: 'Workplace safety procedures and emergency protocols',
      priority: 'Medium'
    },
    {
      id: 6,
      title: 'Financial Compliance Training',
      category: 'Finance',
      status: 'not-started',
      dueDate: '2024-03-01',
      description: 'SOX compliance and financial reporting requirements',
      priority: 'Low'
    }
  ];

  const certifications = [
    {
      id: 1,
      name: 'Security Awareness 2024',
      issueDate: '2024-01-10',
      expiryDate: '2025-01-10',
      status: 'active',
      downloadUrl: '/certificates/security-2024.pdf'
    },
    {
      id: 2,
      name: 'Anti-Harassment Training 2024',
      issueDate: '2024-01-12',
      expiryDate: '2025-01-12',
      status: 'active',
      downloadUrl: '/certificates/harassment-2024.pdf'
    },
    {
      id: 3,
      name: 'Data Privacy Training 2023',
      issueDate: '2023-01-15',
      expiryDate: '2024-01-15',
      status: 'expired',
      downloadUrl: '/certificates/privacy-2023.pdf'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'not-started': return 'bg-gray-100 text-gray-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'expired': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-orange-100 text-orange-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplianceScore = () => {
    const completed = complianceItems.filter(item => item.status === 'completed').length;
    return Math.round((completed / complianceItems.length) * 100);
  };

  const startTraining = (itemId: number) => {
    console.log(`Starting training for item ${itemId}`);
    // This would typically navigate to the training module
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/employee" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Compliance Center</h1>
                <p className="text-gray-600 text-sm">Track your training and compliance requirements</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Compliance Score</p>
              <p className="text-2xl font-bold text-purple">{getComplianceScore()}%</p>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              
              <button
                onClick={() => setActiveTab('training')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'training' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Training
                <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {complianceItems.filter(item => item.status === 'pending' || item.status === 'overdue').length}
                </span>
              </button>
              
              <button
                onClick={() => setActiveTab('certificates')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'certificates' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Certificates
              </button>
              
              <button
                onClick={() => setActiveTab('policies')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'policies' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Policies
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Compliance Score */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Compliance Dashboard</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="relative w-32 h-32 mx-auto mb-4">
                      <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#8b5cf6"
                          strokeWidth="3"
                          strokeDasharray={`${getComplianceScore()}, 100`}
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-2xl font-bold text-purple">{getComplianceScore()}%</span>
                      </div>
                    </div>
                    <p className="text-gray-600">Overall Compliance Score</p>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                      <span className="text-green-700">Completed</span>
                      <span className="font-bold text-green-800">
                        {complianceItems.filter(item => item.status === 'completed').length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
                      <span className="text-orange-700">Pending</span>
                      <span className="font-bold text-orange-800">
                        {complianceItems.filter(item => item.status === 'pending').length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                      <span className="text-red-700">Overdue</span>
                      <span className="font-bold text-red-800">
                        {complianceItems.filter(item => item.status === 'overdue').length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-700">Not Started</span>
                      <span className="font-bold text-gray-800">
                        {complianceItems.filter(item => item.status === 'not-started').length}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Urgent Items */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Urgent Actions Required</h3>
                <div className="space-y-3">
                  {complianceItems
                    .filter(item => item.status === 'overdue' || (item.status === 'pending' && item.priority === 'High'))
                    .map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div>
                          <h4 className="font-medium text-red-900">{item.title}</h4>
                          <p className="text-sm text-red-700">Due: {item.dueDate}</p>
                        </div>
                        <button
                          onClick={() => startTraining(item.id)}
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                        >
                          Start Now
                        </button>
                      </div>
                    ))}
                </div>
              </div>

              {/* Recent Completions */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Recent Completions</h3>
                <div className="space-y-3">
                  {complianceItems
                    .filter(item => item.status === 'completed')
                    .slice(0, 3)
                    .map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div>
                          <p className="font-medium text-green-900">{item.title}</p>
                          <p className="text-sm text-green-700">Completed: {item.completedDate}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <svg className="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          {item.certificateUrl && (
                            <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                              Download Certificate
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'training' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Training Requirements</h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {complianceItems.map((item) => (
                    <div key={item.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{item.title}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.status)}`}>
                          {item.status.replace('-', ' ')}
                        </span>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Category:</span>
                          <span className="font-medium">{item.category}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Due Date:</span>
                          <span className="font-medium">{item.dueDate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Priority:</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(item.priority)}`}>
                            {item.priority}
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">{item.description}</p>
                      <div className="mt-3 flex space-x-2">
                        {item.status !== 'completed' && (
                          <button
                            onClick={() => startTraining(item.id)}
                            className="flex-1 bg-purple hover:bg-darkpurple text-white px-3 py-1 rounded text-sm font-medium"
                          >
                            {item.status === 'not-started' ? 'Start Training' : 'Continue'}
                          </button>
                        )}
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Training
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Priority
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Due Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {complianceItems.map((item) => (
                        <tr key={item.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{item.title}</div>
                              <div className="text-sm text-gray-500">{item.description}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.status)}`}>
                              {item.status.replace('-', ' ')}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(item.priority)}`}>
                              {item.priority}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.dueDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            {item.status !== 'completed' && (
                              <button
                                onClick={() => startTraining(item.id)}
                                className="text-purple hover:text-darkpurple mr-3"
                              >
                                {item.status === 'not-started' ? 'Start' : 'Continue'}
                              </button>
                            )}
                            <button className="text-gray-400 hover:text-gray-600">View</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'certificates' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">My Certificates</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {certifications.map((cert) => (
                    <div key={cert.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{cert.name}</h4>
                          <p className="text-sm text-gray-500">
                            Issued: {cert.issueDate} • Expires: {cert.expiryDate}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(cert.status)}`}>
                            {cert.status}
                          </span>
                          <button className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            Download
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'policies' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Company Policies</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Employee Handbook</h4>
                    <p className="text-sm text-gray-500 mb-3">Complete guide to company policies and procedures</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Code of Conduct</h4>
                    <p className="text-sm text-gray-500 mb-3">Ethical guidelines and behavioral expectations</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Security Policy</h4>
                    <p className="text-sm text-gray-500 mb-3">Information security and data protection guidelines</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Remote Work Policy</h4>
                    <p className="text-sm text-gray-500 mb-3">Guidelines for remote and hybrid work arrangements</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Expense Policy</h4>
                    <p className="text-sm text-gray-500 mb-3">Business expense guidelines and reimbursement procedures</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                  
                  <div className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <h4 className="font-medium text-gray-900 mb-2">Time Off Policy</h4>
                    <p className="text-sm text-gray-500 mb-3">Vacation, sick leave, and time off procedures</p>
                    <button className="text-purple hover:text-darkpurple text-sm font-medium">
                      View Policy
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
