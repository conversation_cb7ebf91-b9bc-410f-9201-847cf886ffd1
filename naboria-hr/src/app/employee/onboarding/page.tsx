'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function EmployeeOnboarding() {
  const [activeStep, setActiveStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState([1, 2]);

  const onboardingSteps = [
    {
      id: 1,
      title: 'Welcome & Introduction',
      description: 'Complete your welcome orientation',
      status: 'completed',
      tasks: [
        { id: 1, title: 'Watch Welcome Video', completed: true },
        { id: 2, title: 'Read Employee Handbook', completed: true },
        { id: 3, title: 'Company Culture Overview', completed: true },
        { id: 4, title: 'Meet Your Team', completed: true }
      ]
    },
    {
      id: 2,
      title: 'Personal Information',
      description: 'Complete your personal and emergency contact details',
      status: 'completed',
      tasks: [
        { id: 1, title: 'Personal Details Form', completed: true },
        { id: 2, title: 'Emergency Contacts', completed: true },
        { id: 3, title: 'Banking Information', completed: true },
        { id: 4, title: 'Tax Forms (W-4)', completed: true }
      ]
    },
    {
      id: 3,
      title: 'IT Setup & Security',
      description: 'Set up your accounts and security credentials',
      status: 'in-progress',
      tasks: [
        { id: 1, title: 'Create Company Email Account', completed: true },
        { id: 2, title: 'Set Up Two-Factor Authentication', completed: true },
        { id: 3, title: 'Install Required Software', completed: false },
        { id: 4, title: 'Security Training Module', completed: false }
      ]
    },
    {
      id: 4,
      title: 'Benefits Enrollment',
      description: 'Choose your health, dental, and retirement benefits',
      status: 'pending',
      tasks: [
        { id: 1, title: 'Health Insurance Selection', completed: false },
        { id: 2, title: 'Dental Insurance Selection', completed: false },
        { id: 3, title: '401(k) Enrollment', completed: false },
        { id: 4, title: 'Life Insurance Options', completed: false }
      ]
    },
    {
      id: 5,
      title: 'Training & Compliance',
      description: 'Complete mandatory training and compliance modules',
      status: 'pending',
      tasks: [
        { id: 1, title: 'Workplace Safety Training', completed: false },
        { id: 2, title: 'Anti-Harassment Training', completed: false },
        { id: 3, title: 'Data Privacy Training', completed: false },
        { id: 4, title: 'Role-Specific Training', completed: false }
      ]
    },
    {
      id: 6,
      title: 'Final Steps',
      description: 'Complete your onboarding and get ready to start',
      status: 'pending',
      tasks: [
        { id: 1, title: 'Manager Check-in Meeting', completed: false },
        { id: 2, title: 'Workspace Setup', completed: false },
        { id: 3, title: 'First Week Goals', completed: false },
        { id: 4, title: 'Onboarding Feedback', completed: false }
      ]
    }
  ];

  const getStepStatus = (stepId: number) => {
    const step = onboardingSteps.find(s => s.id === stepId);
    return step?.status || 'pending';
  };

  const getCompletionPercentage = () => {
    const totalTasks = onboardingSteps.reduce((acc, step) => acc + step.tasks.length, 0);
    const completedTasks = onboardingSteps.reduce((acc, step) => 
      acc + step.tasks.filter(task => task.completed).length, 0
    );
    return Math.round((completedTasks / totalTasks) * 100);
  };

  const markTaskComplete = (stepId: number, taskId: number) => {
    // This would typically make an API call
    console.log(`Marking task ${taskId} in step ${stepId} as complete`);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/employee" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Employee Onboarding</h1>
                <p className="text-gray-600 text-sm">Complete your onboarding process</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Overall Progress</p>
              <p className="text-2xl font-bold text-purple">{getCompletionPercentage()}%</p>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Progress Overview */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-navy mb-4">Onboarding Progress</h2>
          <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
            <div 
              className="bg-purple h-3 rounded-full transition-all duration-300" 
              style={{ width: `${getCompletionPercentage()}%` }}
            ></div>
          </div>
          <p className="text-sm text-gray-600">
            {getCompletionPercentage()}% complete • {onboardingSteps.filter(s => s.status === 'completed').length} of {onboardingSteps.length} steps finished
          </p>
        </div>

        {/* Step Navigation */}
        <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
          <div className="flex flex-wrap gap-2">
            {onboardingSteps.map((step) => (
              <button
                key={step.id}
                onClick={() => setActiveStep(step.id)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeStep === step.id
                    ? 'bg-purple text-white'
                    : step.status === 'completed'
                    ? 'bg-green-100 text-green-800'
                    : step.status === 'in-progress'
                    ? 'bg-orange-100 text-orange-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                <div className="flex items-center">
                  {step.status === 'completed' && (
                    <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                  Step {step.id}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Active Step Content */}
        {onboardingSteps.map((step) => (
          activeStep === step.id && (
            <div key={step.id} className="bg-white rounded-xl shadow-sm p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-navy">{step.title}</h2>
                  <p className="text-gray-600">{step.description}</p>
                </div>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                  step.status === 'completed' ? 'bg-green-100 text-green-800' :
                  step.status === 'in-progress' ? 'bg-orange-100 text-orange-800' :
                  'bg-gray-100 text-gray-600'
                }`}>
                  {step.status === 'completed' ? 'Completed' :
                   step.status === 'in-progress' ? 'In Progress' : 'Pending'}
                </div>
              </div>

              <div className="space-y-4">
                {step.tasks.map((task) => (
                  <div key={task.id} className={`p-4 rounded-lg border-2 transition-colors ${
                    task.completed 
                      ? 'border-green-200 bg-green-50' 
                      : 'border-gray-200 bg-white hover:border-purple'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center mr-3 ${
                          task.completed 
                            ? 'border-green-500 bg-green-500' 
                            : 'border-gray-300'
                        }`}>
                          {task.completed && (
                            <svg className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </div>
                        <span className={`font-medium ${
                          task.completed ? 'text-green-800' : 'text-gray-900'
                        }`}>
                          {task.title}
                        </span>
                      </div>
                      {!task.completed && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => markTaskComplete(step.id, task.id)}
                            className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors"
                          >
                            Start Task
                          </button>
                          <button className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-3 py-2 rounded-lg font-medium transition-colors">
                            View Details
                          </button>
                        </div>
                      )}
                      {task.completed && (
                        <span className="text-green-600 text-sm font-medium">Completed</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Step Navigation */}
              <div className="flex justify-between mt-8">
                <button
                  onClick={() => setActiveStep(Math.max(1, activeStep - 1))}
                  disabled={activeStep === 1}
                  className="flex items-center px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                  Previous Step
                </button>
                
                <button
                  onClick={() => setActiveStep(Math.min(onboardingSteps.length, activeStep + 1))}
                  disabled={activeStep === onboardingSteps.length}
                  className="flex items-center px-4 py-2 bg-purple hover:bg-darkpurple text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next Step
                  <svg className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          )
        ))}

        {/* Help Section */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mt-8">
          <div className="flex items-start">
            <svg className="h-6 w-6 text-blue-500 mr-3 mt-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h3 className="text-lg font-medium text-blue-800 mb-2">Need Help?</h3>
              <p className="text-blue-700 mb-4">
                If you have any questions or need assistance with your onboarding, don't hesitate to reach out to our HR team.
              </p>
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                  Contact HR
                </button>
                <button className="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-4 py-2 rounded-lg font-medium transition-colors">
                  Schedule Meeting
                </button>
                <button className="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-4 py-2 rounded-lg font-medium transition-colors">
                  View FAQ
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
