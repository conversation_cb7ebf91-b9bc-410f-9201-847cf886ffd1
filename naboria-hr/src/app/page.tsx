'use client';

import { useState } from 'react';

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('startups');

  return (
    <div className="bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <svg className="h-10 w-10 text-purple" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 5L30 10V30L20 35L10 30V10L20 5Z" fill="currentColor" fillOpacity="0.8"/>
              <path d="M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z" fill="white"/>
            </svg>
            <span className="ml-2 text-2xl font-bold text-navy font-poppins">Naboria</span>
          </div>

          <div className="hidden md:flex space-x-8">
            <a href="#home" className="nav-link active-nav text-navy font-medium hover:text-purple transition-colors">Home</a>
            <a href="#features" className="nav-link text-navy font-medium hover:text-purple transition-colors">Features</a>
            <a href="#solutions" className="nav-link text-navy font-medium hover:text-purple transition-colors">Solutions</a>
            <a href="#integrations" className="nav-link text-navy font-medium hover:text-purple transition-colors">Integrations</a>
            <a href="#pricing" className="nav-link text-navy font-medium hover:text-purple transition-colors">Pricing</a>
            <a href="#about" className="nav-link text-navy font-medium hover:text-purple transition-colors">About</a>
            <a href="#contact" className="nav-link text-navy font-medium hover:text-purple transition-colors">Contact</a>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <a href="#" className="text-navy font-medium hover:text-purple transition-colors">Login</a>
            <a href="#" className="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg">Get Started</a>
          </div>

          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden text-navy"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white shadow-lg absolute w-full">
            <div className="container mx-auto px-4 py-3 flex flex-col space-y-3">
              <a href="#home" className="text-navy font-medium py-2 hover:text-purple transition-colors">Home</a>
              <a href="#features" className="text-navy font-medium py-2 hover:text-purple transition-colors">Features</a>
              <a href="#solutions" className="text-navy font-medium py-2 hover:text-purple transition-colors">Solutions</a>
              <a href="#integrations" className="text-navy font-medium py-2 hover:text-purple transition-colors">Integrations</a>
              <a href="#pricing" className="text-navy font-medium py-2 hover:text-purple transition-colors">Pricing</a>
              <a href="#about" className="text-navy font-medium py-2 hover:text-purple transition-colors">About</a>
              <a href="#contact" className="text-navy font-medium py-2 hover:text-purple transition-colors">Contact</a>
              <div className="flex flex-col space-y-3 pt-3 border-t border-gray-200">
                <a href="#" className="text-navy font-medium hover:text-purple transition-colors">Login</a>
                <a href="#" className="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md">Get Started</a>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Home Section */}
      <section id="home" className="pt-28 pb-20 md:pt-32 md:pb-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins">
                The Future of Work <span className="gradient-text">Starts Here</span>
              </h1>
              <p className="text-lg text-gray-600 mb-8 max-w-lg">
                Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <a href="#" className="gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                  Request Demo
                </a>
                <a href="#" className="border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors">
                  Learn More
                </a>
              </div>
              <div className="mt-10 flex items-center">
                <div className="flex -space-x-2">
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <div className="w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium">+5</div>
                </div>
                <p className="ml-4 text-gray-600">Trusted by 1000+ companies worldwide</p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="relative">
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full"></div>
                <div className="absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100">
                  <div className="bg-navy p-4 flex justify-between items-center">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-white text-sm">Naboria Dashboard</div>
                    <div></div>
                  </div>
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                      <div>
                        <h3 className="text-navy font-semibold">Welcome back, Sarah</h3>
                        <p className="text-gray-500 text-sm">Monday, June 12</p>
                      </div>
                      <div className="bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium">
                        Admin
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-purple text-xl font-bold">24</div>
                        <div className="text-gray-500 text-sm">New Hires</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-teal text-xl font-bold">98%</div>
                        <div className="text-gray-500 text-sm">Onboarding</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-navy text-xl font-bold">12</div>
                        <div className="text-gray-500 text-sm">Approvals</div>
                      </div>
                    </div>
                    <div className="mb-6">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-navy">Onboarding Progress</h4>
                        <span className="text-sm text-purple">View All</span>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Emily Johnson</span>
                            <span>75%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-purple h-2 rounded-full" style={{width: '75%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Michael Chen</span>
                            <span>90%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-teal h-2 rounded-full" style={{width: '90%'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <button className="text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        New Task
                      </button>
                      <button className="text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Run Payroll
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}