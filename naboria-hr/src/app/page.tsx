'use client';

import { useState } from 'react';
import Link from 'next/link';
import IntegrationsSection from '@/components/IntegrationsSection';
import PricingSection from '@/components/PricingSection';
import AboutSection from '@/components/AboutSection';
import ContactSection from '@/components/ContactSection';
import Footer from '@/components/Footer';

export default function Home() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('startups');

  return (
    <div className="bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center">
            <svg className="h-10 w-10 text-purple" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 5L30 10V30L20 35L10 30V10L20 5Z" fill="currentColor" fillOpacity="0.8"/>
              <path d="M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z" fill="white"/>
            </svg>
            <span className="ml-2 text-2xl font-bold text-navy font-poppins">Naboria</span>
          </div>

          <div className="hidden md:flex space-x-8">
            <a href="#home" className="nav-link active-nav text-navy font-medium hover:text-purple transition-colors">Home</a>
            <a href="#features" className="nav-link text-navy font-medium hover:text-purple transition-colors">Features</a>
            <a href="#solutions" className="nav-link text-navy font-medium hover:text-purple transition-colors">Solutions</a>
            <a href="#integrations" className="nav-link text-navy font-medium hover:text-purple transition-colors">Integrations</a>
            <a href="#pricing" className="nav-link text-navy font-medium hover:text-purple transition-colors">Pricing</a>
            <a href="#about" className="nav-link text-navy font-medium hover:text-purple transition-colors">About</a>
            <a href="#contact" className="nav-link text-navy font-medium hover:text-purple transition-colors">Contact</a>
          </div>

          <div className="hidden md:flex items-center space-x-4">
            <Link href="/login" className="text-navy font-medium hover:text-purple transition-colors">Login</Link>
            <Link href="/signup" className="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg">Get Started</Link>
          </div>

          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="md:hidden text-navy"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white shadow-lg absolute w-full">
            <div className="container mx-auto px-4 py-3 flex flex-col space-y-3">
              <a href="#home" className="text-navy font-medium py-2 hover:text-purple transition-colors">Home</a>
              <a href="#features" className="text-navy font-medium py-2 hover:text-purple transition-colors">Features</a>
              <a href="#solutions" className="text-navy font-medium py-2 hover:text-purple transition-colors">Solutions</a>
              <a href="#integrations" className="text-navy font-medium py-2 hover:text-purple transition-colors">Integrations</a>
              <a href="#pricing" className="text-navy font-medium py-2 hover:text-purple transition-colors">Pricing</a>
              <a href="#about" className="text-navy font-medium py-2 hover:text-purple transition-colors">About</a>
              <a href="#contact" className="text-navy font-medium py-2 hover:text-purple transition-colors">Contact</a>
              <div className="flex flex-col space-y-3 pt-3 border-t border-gray-200">
                <Link href="/login" className="text-navy font-medium hover:text-purple transition-colors">Login</Link>
                <Link href="/signup" className="bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md">Get Started</Link>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Home Section */}
      <section id="home" className="pt-28 pb-20 md:pt-32 md:pb-24">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins">
                The Future of Work <span className="gradient-text">Starts Here</span>
              </h1>
              <p className="text-lg text-gray-600 mb-8 max-w-lg">
                Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <Link href="/signup" className="gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1">
                  Start Free Trial
                </Link>
                <Link href="/dashboard" className="border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors">
                  View Demo
                </Link>
              </div>
              <div className="mt-10 flex items-center">
                <div className="flex -space-x-2">
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                  <div className="w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium">+5</div>
                </div>
                <p className="ml-4 text-gray-600">Trusted by 1000+ companies worldwide</p>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="relative">
                <div className="absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full"></div>
                <div className="absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100">
                  <div className="bg-navy p-4 flex justify-between items-center">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    </div>
                    <div className="text-white text-sm">Naboria Dashboard</div>
                    <div></div>
                  </div>
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                      <div>
                        <h3 className="text-navy font-semibold">Welcome back, Sarah</h3>
                        <p className="text-gray-500 text-sm">Monday, June 12</p>
                      </div>
                      <div className="bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium">
                        Admin
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-purple text-xl font-bold">24</div>
                        <div className="text-gray-500 text-sm">New Hires</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-teal text-xl font-bold">98%</div>
                        <div className="text-gray-500 text-sm">Onboarding</div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <div className="text-navy text-xl font-bold">12</div>
                        <div className="text-gray-500 text-sm">Approvals</div>
                      </div>
                    </div>
                    <div className="mb-6">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium text-navy">Onboarding Progress</h4>
                        <span className="text-sm text-purple">View All</span>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Emily Johnson</span>
                            <span>75%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-purple h-2 rounded-full" style={{width: '75%'}}></div>
                          </div>
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Michael Chen</span>
                            <span>90%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-teal h-2 rounded-full" style={{width: '90%'}}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <button className="text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        New Task
                      </button>
                      <button className="text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                        </svg>
                        Run Payroll
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Powerful Features</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">Our comprehensive platform streamlines your HR, payroll, and IT operations with powerful automation and intuitive workflows.</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Feature 1 */}
            <div className="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
              <div className="w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-navy mb-3">Seamless Onboarding</h3>
              <p className="text-gray-600 mb-4">Automate employee onboarding with customizable workflows, digital paperwork, and integrated background checks.</p>
              <ul className="space-y-2 mb-5">
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Digital document signing
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Automated task assignment
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Progress tracking dashboard
                </li>
              </ul>
              <a href="#" className="text-purple font-medium flex items-center hover:text-darkpurple transition-colors">
                Learn more
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {/* Feature 2 */}
            <div className="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
              <div className="w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-navy mb-3">Smart Payroll</h3>
              <p className="text-gray-600 mb-4">Process payroll with precision and compliance across multiple countries, currencies, and tax jurisdictions.</p>
              <ul className="space-y-2 mb-5">
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Automatic tax calculations
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Multi-currency support
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Compliance monitoring
                </li>
              </ul>
              <a href="#" className="text-teal font-medium flex items-center hover:text-darkpurple transition-colors">
                Learn more
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {/* Feature 3 */}
            <div className="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
              <div className="w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-navy mb-3">IT Provisioning</h3>
              <p className="text-gray-600 mb-4">Automate device setup, software licensing, and access management for new and existing employees.</p>
              <ul className="space-y-2 mb-5">
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  One-click app provisioning
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Device management
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-teal mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Security compliance
                </li>
              </ul>
              <a href="#" className="text-purple font-medium flex items-center hover:text-darkpurple transition-colors">
                Learn more
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>

            {/* Feature 4 */}
            <div className="bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300">
              <div className="w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-teal" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-navy mb-3">Finance Management</h3>
              <p className="text-gray-600 mb-4">Streamline expense tracking, budgeting, and financial reporting with powerful automation tools.</p>
              <ul className="space-y-2 mb-5">
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Expense approval workflows
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Budget tracking
                </li>
                <li className="flex items-center text-sm text-gray-600">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                  Financial reporting
                </li>
              </ul>
              <a href="#" className="text-teal font-medium flex items-center hover:text-darkpurple transition-colors">
                Learn more
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section id="solutions" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins">Tailored Solutions</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">Our platform adapts to your organization's unique needs, whether you're a startup or a global enterprise.</p>
          </div>

          <div className="flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6">
            <div className="w-full md:w-1/4">
              <div className="bg-navy text-white p-6 rounded-xl h-full">
                <h3 className="text-xl font-semibold mb-4">Choose Your Solution</h3>
                <p className="text-gray-300 mb-6">Select the solution that best fits your organization's size and needs.</p>

                <div className="space-y-3">
                  <button
                    onClick={() => setActiveTab('startups')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${
                      activeTab === 'startups'
                        ? 'bg-purple/20 border-l-4 border-purple'
                        : 'hover:bg-white/10'
                    }`}
                  >
                    Startups
                  </button>
                  <button
                    onClick={() => setActiveTab('enterprises')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${
                      activeTab === 'enterprises'
                        ? 'bg-purple/20 border-l-4 border-purple'
                        : 'hover:bg-white/10'
                    }`}
                  >
                    Enterprises
                  </button>
                  <button
                    onClick={() => setActiveTab('global')}
                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${
                      activeTab === 'global'
                        ? 'bg-purple/20 border-l-4 border-purple'
                        : 'hover:bg-white/10'
                    }`}
                  >
                    Global Teams
                  </button>
                </div>
              </div>
            </div>

            <div className="w-full md:w-3/4">
              {/* Startups Tab */}
              {activeTab === 'startups' && (
                <div className="tab-content active bg-white p-8 rounded-xl shadow-lg h-full">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                      <span className="text-sm font-medium text-purple bg-purple/10 px-3 py-1 rounded-full">For Startups</span>
                      <h3 className="text-2xl font-bold text-navy mt-4 mb-4">Scale your team without scaling overhead</h3>
                      <p className="text-gray-600 mb-6">Perfect for growing teams that need to establish efficient HR and IT processes without dedicated departments.</p>

                      <ul className="space-y-4 mb-8">
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Quick Implementation</h4>
                            <p className="text-gray-600 text-sm">Get up and running in days, not months</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Scalable Pricing</h4>
                            <p className="text-gray-600 text-sm">Pay only for what you need as you grow</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-purple mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Essential Integrations</h4>
                            <p className="text-gray-600 text-sm">Connect with the tools startups love</p>
                          </div>
                        </li>
                      </ul>

                      <a href="#" className="inline-block bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Learn More
                      </a>
                    </div>
                    <div className="md:w-1/2">
                      <div className="bg-gray-50 rounded-xl p-6 h-full">
                        <div className="flex justify-between items-center mb-6">
                          <h4 className="font-semibold text-navy">Startup Success Story</h4>
                          <div className="text-purple">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          </div>
                        </div>

                        <div className="flex items-center mb-4">
                          <div className="w-12 h-12 bg-teal/20 rounded-full flex items-center justify-center mr-4">
                            <span className="text-teal font-bold">TL</span>
                          </div>
                          <div>
                            <h5 className="font-medium">TechLaunch</h5>
                            <p className="text-sm text-gray-500">SaaS Startup, 45 employees</p>
                          </div>
                        </div>

                        <blockquote className="text-gray-600 italic mb-6">
                          "Naboria helped us scale from 10 to 45 employees in just 8 months without hiring a dedicated HR team. The automated onboarding and IT provisioning saved us countless hours."
                        </blockquote>

                        <div className="flex items-center">
                          <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E" alt="User" className="w-8 h-8 rounded-full" />
                          <div className="ml-3">
                            <h6 className="font-medium text-sm">Sarah Chen</h6>
                            <p className="text-xs text-gray-500">Co-founder & COO</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Enterprises Tab */}
              {activeTab === 'enterprises' && (
                <div className="tab-content active bg-white p-8 rounded-xl shadow-lg h-full">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                      <span className="text-sm font-medium text-teal bg-teal/10 px-3 py-1 rounded-full">For Enterprises</span>
                      <h3 className="text-2xl font-bold text-navy mt-4 mb-4">Enterprise-grade security and compliance</h3>
                      <p className="text-gray-600 mb-6">Comprehensive solutions for large organizations with complex requirements and strict compliance needs.</p>

                      <ul className="space-y-4 mb-8">
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">SOC 2 Type II Compliance</h4>
                            <p className="text-gray-600 text-sm">Enterprise-grade security and audit trails</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Advanced Analytics</h4>
                            <p className="text-gray-600 text-sm">Deep insights and custom reporting</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-teal mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Dedicated Support</h4>
                            <p className="text-gray-600 text-sm">24/7 support with dedicated CSM</p>
                          </div>
                        </li>
                      </ul>

                      <a href="#" className="inline-block bg-teal hover:bg-teal/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Learn More
                      </a>
                    </div>
                    <div className="md:w-1/2">
                      <div className="bg-gray-50 rounded-xl p-6 h-full">
                        <div className="flex justify-between items-center mb-6">
                          <h4 className="font-semibold text-navy">Enterprise Metrics</h4>
                          <div className="text-teal">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4 mb-6">
                          <div className="bg-white p-4 rounded-lg">
                            <div className="text-2xl font-bold text-teal">99.9%</div>
                            <div className="text-sm text-gray-500">Uptime SLA</div>
                          </div>
                          <div className="bg-white p-4 rounded-lg">
                            <div className="text-2xl font-bold text-purple">50K+</div>
                            <div className="text-sm text-gray-500">Employees</div>
                          </div>
                          <div className="bg-white p-4 rounded-lg">
                            <div className="text-2xl font-bold text-navy">85%</div>
                            <div className="text-sm text-gray-500">Time Saved</div>
                          </div>
                          <div className="bg-white p-4 rounded-lg">
                            <div className="text-2xl font-bold text-teal">24/7</div>
                            <div className="text-sm text-gray-500">Support</div>
                          </div>
                        </div>

                        <div className="bg-white p-4 rounded-lg">
                          <h5 className="font-medium mb-2">Fortune 500 Client</h5>
                          <p className="text-sm text-gray-600 mb-3">"Naboria transformed our global HR operations, reducing onboarding time by 75% across 40+ countries."</p>
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-navy/20 rounded-full flex items-center justify-center mr-3">
                              <span className="text-navy font-bold text-sm">GC</span>
                            </div>
                            <div>
                              <h6 className="font-medium text-sm">Global Corp</h6>
                              <p className="text-xs text-gray-500">50,000+ employees</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Global Teams Tab */}
              {activeTab === 'global' && (
                <div className="tab-content active bg-white p-8 rounded-xl shadow-lg h-full">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/2 mb-6 md:mb-0 md:pr-8">
                      <span className="text-sm font-medium text-navy bg-navy/10 px-3 py-1 rounded-full">For Global Teams</span>
                      <h3 className="text-2xl font-bold text-navy mt-4 mb-4">Seamless global operations</h3>
                      <p className="text-gray-600 mb-6">Manage distributed teams across multiple countries with localized compliance and unified workflows.</p>

                      <ul className="space-y-4 mb-8">
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-navy mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Multi-Country Payroll</h4>
                            <p className="text-gray-600 text-sm">Local compliance in 40+ countries</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-navy mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Currency Management</h4>
                            <p className="text-gray-600 text-sm">Real-time exchange rates and hedging</p>
                          </div>
                        </li>
                        <li className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-navy mr-3 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <div>
                            <h4 className="font-medium text-navy">Time Zone Coordination</h4>
                            <p className="text-gray-600 text-sm">Automated scheduling and workflows</p>
                          </div>
                        </li>
                      </ul>

                      <a href="#" className="inline-block bg-navy hover:bg-navy/80 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Learn More
                      </a>
                    </div>
                    <div className="md:w-1/2">
                      <div className="bg-gray-50 rounded-xl p-6 h-full">
                        <div className="flex justify-between items-center mb-6">
                          <h4 className="font-semibold text-navy">Global Coverage</h4>
                          <div className="text-navy">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>

                        <div className="mb-6">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-sm font-medium">Countries Supported</span>
                            <span className="text-sm text-navy font-bold">40+</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div className="bg-navy h-2 rounded-full" style={{width: '85%'}}></div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-3 mb-6">
                          <div className="bg-white p-3 rounded-lg text-center">
                            <div className="text-lg font-bold text-purple">🇺🇸</div>
                            <div className="text-xs text-gray-500">North America</div>
                          </div>
                          <div className="bg-white p-3 rounded-lg text-center">
                            <div className="text-lg font-bold text-teal">🇪🇺</div>
                            <div className="text-xs text-gray-500">Europe</div>
                          </div>
                          <div className="bg-white p-3 rounded-lg text-center">
                            <div className="text-lg font-bold text-navy">🇦🇺</div>
                            <div className="text-xs text-gray-500">Asia Pacific</div>
                          </div>
                          <div className="bg-white p-3 rounded-lg text-center">
                            <div className="text-lg font-bold text-purple">🌍</div>
                            <div className="text-xs text-gray-500">Global</div>
                          </div>
                        </div>

                        <div className="bg-white p-4 rounded-lg">
                          <h5 className="font-medium mb-2">Remote-First Company</h5>
                          <p className="text-sm text-gray-600 mb-3">"Managing our 200+ remote employees across 25 countries is now effortless with Naboria's global platform."</p>
                          <div className="flex items-center">
                            <div className="w-8 h-8 bg-purple/20 rounded-full flex items-center justify-center mr-3">
                              <span className="text-purple font-bold text-sm">RF</span>
                            </div>
                            <div>
                              <h6 className="font-medium text-sm">RemoteFirst Inc</h6>
                              <p className="text-xs text-gray-500">25 countries, 200+ employees</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Additional Sections */}
      <IntegrationsSection />
      <PricingSection />
      <AboutSection />
      <ContactSection />
      <Footer />
    </div>
  );
}