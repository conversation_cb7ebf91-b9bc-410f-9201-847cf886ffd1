'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ApiDocs() {
  const [activeSection, setActiveSection] = useState('getting-started');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <svg className="h-8 w-8 text-purple mr-3" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 5L30 10V30L20 35L10 30V10L20 5Z" fill="currentColor" fillOpacity="0.8"/>
                  <path d="M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z" fill="white"/>
                </svg>
                <span className="text-xl font-bold text-navy">Naboria API</span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/login" className="text-navy font-medium hover:text-purple transition-colors">
                Login
              </Link>
              <Link href="/signup" className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Get API Key
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="flex">
          {/* Sidebar */}
          <aside className="w-64 mr-8">
            <nav className="sticky top-8">
              <div className="space-y-2">
                <button
                  onClick={() => setActiveSection('getting-started')}
                  className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                    activeSection === 'getting-started' 
                      ? 'bg-purple/10 text-purple font-medium' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Getting Started
                </button>
                <button
                  onClick={() => setActiveSection('authentication')}
                  className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                    activeSection === 'authentication' 
                      ? 'bg-purple/10 text-purple font-medium' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Authentication
                </button>
                <button
                  onClick={() => setActiveSection('employees')}
                  className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                    activeSection === 'employees' 
                      ? 'bg-purple/10 text-purple font-medium' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Employees API
                </button>
                <button
                  onClick={() => setActiveSection('payroll')}
                  className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                    activeSection === 'payroll' 
                      ? 'bg-purple/10 text-purple font-medium' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Payroll API
                </button>
                <button
                  onClick={() => setActiveSection('webhooks')}
                  className={`w-full text-left px-4 py-2 rounded-lg transition-colors ${
                    activeSection === 'webhooks' 
                      ? 'bg-purple/10 text-purple font-medium' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  Webhooks
                </button>
              </div>
            </nav>
          </aside>

          {/* Main Content */}
          <main className="flex-1">
            {activeSection === 'getting-started' && (
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h1 className="text-3xl font-bold text-navy mb-6">Getting Started with Naboria API</h1>
                
                <div className="prose max-w-none">
                  <p className="text-gray-600 mb-6">
                    The Naboria API allows you to integrate your applications with our HR, payroll, and IT management platform. 
                    Our RESTful API is designed to be simple, predictable, and developer-friendly.
                  </p>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Base URL</h2>
                  <div className="bg-gray-100 p-4 rounded-lg mb-6">
                    <code className="text-sm">https://api.naboria.com/v1</code>
                  </div>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Quick Start</h2>
                  <ol className="list-decimal list-inside space-y-2 mb-6">
                    <li>Sign up for a Naboria account</li>
                    <li>Generate an API key from your dashboard</li>
                    <li>Make your first API call</li>
                  </ol>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Example Request</h2>
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`curl -X GET "https://api.naboria.com/v1/employees" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json"`}</code></pre>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-blue-800">
                          Rate Limiting
                        </h3>
                        <div className="mt-1 text-sm text-blue-700">
                          <p>API requests are limited to 1000 requests per hour per API key.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'authentication' && (
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h1 className="text-3xl font-bold text-navy mb-6">Authentication</h1>
                
                <div className="prose max-w-none">
                  <p className="text-gray-600 mb-6">
                    Naboria API uses API keys for authentication. Include your API key in the Authorization header of every request.
                  </p>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">API Key Authentication</h2>
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`Authorization: Bearer YOUR_API_KEY`}</code></pre>
                  </div>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Getting Your API Key</h2>
                  <ol className="list-decimal list-inside space-y-2 mb-6">
                    <li>Log in to your Naboria dashboard</li>
                    <li>Navigate to Settings → API Keys</li>
                    <li>Click "Generate New API Key"</li>
                    <li>Copy and securely store your API key</li>
                  </ol>
                  
                  <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                          Keep Your API Key Secure
                        </h3>
                        <div className="mt-1 text-sm text-red-700">
                          <p>Never expose your API key in client-side code or public repositories.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'employees' && (
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h1 className="text-3xl font-bold text-navy mb-6">Employees API</h1>
                
                <div className="prose max-w-none">
                  <p className="text-gray-600 mb-6">
                    Manage employee data, including creating, updating, and retrieving employee information.
                  </p>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">List Employees</h2>
                  <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <code className="text-sm">GET /employees</code>
                  </div>
                  
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`curl -X GET "https://api.naboria.com/v1/employees" \\
  -H "Authorization: Bearer YOUR_API_KEY"`}</code></pre>
                  </div>
                  
                  <h3 className="text-lg font-medium text-navy mb-2">Response</h3>
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`{
  "data": [
    {
      "id": "emp_123",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "department": "Engineering",
      "position": "Software Engineer",
      "start_date": "2023-01-15",
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 25,
    "total": 1
  }
}`}</code></pre>
                  </div>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Create Employee</h2>
                  <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <code className="text-sm">POST /employees</code>
                  </div>
                  
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`curl -X POST "https://api.naboria.com/v1/employees" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "first_name": "Jane",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "department": "Marketing",
    "position": "Marketing Manager",
    "start_date": "2024-01-01"
  }'`}</code></pre>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'payroll' && (
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h1 className="text-3xl font-bold text-navy mb-6">Payroll API</h1>
                
                <div className="prose max-w-none">
                  <p className="text-gray-600 mb-6">
                    Access payroll data, run payroll processes, and manage compensation information.
                  </p>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Get Payroll Runs</h2>
                  <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <code className="text-sm">GET /payroll/runs</code>
                  </div>
                  
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`curl -X GET "https://api.naboria.com/v1/payroll/runs" \\
  -H "Authorization: Bearer YOUR_API_KEY"`}</code></pre>
                  </div>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Create Payroll Run</h2>
                  <div className="bg-gray-100 p-4 rounded-lg mb-4">
                    <code className="text-sm">POST /payroll/runs</code>
                  </div>
                  
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`curl -X POST "https://api.naboria.com/v1/payroll/runs" \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "pay_period_start": "2024-01-01",
    "pay_period_end": "2024-01-15",
    "pay_date": "2024-01-20"
  }'`}</code></pre>
                  </div>
                  
                  <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-yellow-800">
                          Payroll Operations
                        </h3>
                        <div className="mt-1 text-sm text-yellow-700">
                          <p>Payroll operations are sensitive. Always test in sandbox mode first.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeSection === 'webhooks' && (
              <div className="bg-white rounded-xl shadow-sm p-8">
                <h1 className="text-3xl font-bold text-navy mb-6">Webhooks</h1>
                
                <div className="prose max-w-none">
                  <p className="text-gray-600 mb-6">
                    Webhooks allow you to receive real-time notifications when events occur in your Naboria account.
                  </p>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Supported Events</h2>
                  <ul className="list-disc list-inside space-y-2 mb-6">
                    <li><code>employee.created</code> - New employee added</li>
                    <li><code>employee.updated</code> - Employee information changed</li>
                    <li><code>payroll.run.completed</code> - Payroll run finished</li>
                    <li><code>onboarding.completed</code> - Employee onboarding finished</li>
                  </ul>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Webhook Payload Example</h2>
                  <div className="bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto">
                    <pre><code>{`{
  "event": "employee.created",
  "data": {
    "id": "emp_123",
    "first_name": "John",
    "last_name": "Doe",
    "email": "<EMAIL>"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}`}</code></pre>
                  </div>
                  
                  <h2 className="text-xl font-semibold text-navy mb-4">Configuring Webhooks</h2>
                  <ol className="list-decimal list-inside space-y-2 mb-6">
                    <li>Go to Settings → Webhooks in your dashboard</li>
                    <li>Click "Add Webhook Endpoint"</li>
                    <li>Enter your endpoint URL</li>
                    <li>Select the events you want to receive</li>
                    <li>Save your configuration</li>
                  </ol>
                </div>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
}
