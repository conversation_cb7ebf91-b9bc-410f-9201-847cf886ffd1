'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function FinanceManagement() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');

  const expenses = [
    {
      id: 1,
      description: 'Office Supplies',
      amount: '$1,250.00',
      category: 'Operations',
      date: '2024-01-15',
      status: 'Approved',
      employee: '<PERSON>'
    },
    {
      id: 2,
      description: 'Software Licenses',
      amount: '$5,400.00',
      category: 'Technology',
      date: '2024-01-14',
      status: 'Pending',
      employee: 'IT Department'
    },
    {
      id: 3,
      description: 'Travel Expenses',
      amount: '$2,800.00',
      category: 'Travel',
      date: '2024-01-13',
      status: 'Approved',
      employee: '<PERSON>'
    },
    {
      id: 4,
      description: 'Marketing Campaign',
      amount: '$8,500.00',
      category: 'Marketing',
      date: '2024-01-12',
      status: 'Approved',
      employee: 'Marketing Team'
    }
  ];

  const budgets = [
    { category: 'Payroll', allocated: 1200000, spent: 1150000, remaining: 50000 },
    { category: 'Technology', allocated: 150000, spent: 89000, remaining: 61000 },
    { category: 'Marketing', allocated: 80000, spent: 67000, remaining: 13000 },
    { category: 'Operations', allocated: 60000, spent: 45000, remaining: 15000 },
    { category: 'Travel', allocated: 40000, spent: 28000, remaining: 12000 }
  ];

  const invoices = [
    { id: 'INV-001', vendor: 'Microsoft Corporation', amount: '$12,350', dueDate: '2024-01-25', status: 'Pending' },
    { id: 'INV-002', vendor: 'Adobe Systems', amount: '$2,250', dueDate: '2024-01-20', status: 'Overdue' },
    { id: 'INV-003', vendor: 'Slack Technologies', amount: '$1,975', dueDate: '2024-01-30', status: 'Paid' },
    { id: 'INV-004', vendor: 'Amazon Web Services', amount: '$4,800', dueDate: '2024-02-05', status: 'Pending' }
  ];

  const financialMetrics = {
    totalRevenue: '$2,450,000',
    totalExpenses: '$1,890,000',
    netProfit: '$560,000',
    profitMargin: '22.9%',
    cashFlow: '+$340,000',
    burnRate: '$157,500'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Finance Management</h1>
                <p className="text-gray-600 text-sm">Track expenses, budgets, and financial performance</p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto">
              <Link
                href="/dashboard/finance/add-expense"
                className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors text-center"
              >
                Add Expense
              </Link>
              <Link
                href="/dashboard/finance/generate-report"
                className="border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors text-center"
              >
                Generate Report
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              <button
                onClick={() => setActiveTab('expenses')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'expenses' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Expenses
                <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {expenses.filter(e => e.status === 'Pending').length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('budgets')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'budgets' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Budgets
              </button>
              <button
                onClick={() => setActiveTab('invoices')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'invoices' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Invoices
                <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {invoices.filter(i => i.status === 'Overdue').length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'reports' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Reports
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Financial Metrics */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">{financialMetrics.totalRevenue}</p>
                      <p className="text-sm text-green-600">+12.5% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">{financialMetrics.totalExpenses}</p>
                      <p className="text-sm text-orange-600">+3.2% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Net Profit</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">{financialMetrics.netProfit}</p>
                      <p className="text-sm text-green-600">Margin: {financialMetrics.profitMargin}</p>
                    </div>
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Cash Flow</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">{financialMetrics.cashFlow}</p>
                      <p className="text-sm text-green-600">Positive trend</p>
                    </div>
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Monthly Burn Rate</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">{financialMetrics.burnRate}</p>
                      <p className="text-sm text-blue-600">18 months runway</p>
                    </div>
                    <div className="w-12 h-12 bg-red/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Transactions */}
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Recent Transactions</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {expenses.slice(0, 5).map((expense) => (
                    <div key={expense.id} className="p-4 lg:p-6">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{expense.description}</h4>
                          <p className="text-sm text-gray-500">
                            {expense.employee} • {expense.category} • {expense.date}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-lg font-semibold text-navy">{expense.amount}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            expense.status === 'Approved' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {expense.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'expenses' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
                    <h3 className="text-lg font-semibold text-navy">Expense Reports</h3>
                    <select
                      value={selectedPeriod}
                      onChange={(e) => setSelectedPeriod(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    >
                      <option value="current-month">Current Month</option>
                      <option value="last-month">Last Month</option>
                      <option value="quarter">This Quarter</option>
                      <option value="year">This Year</option>
                    </select>
                  </div>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {expenses.map((expense) => (
                    <div key={expense.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{expense.description}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          expense.status === 'Approved' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {expense.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Amount:</span>
                          <p className="font-medium">{expense.amount}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Category:</span>
                          <p className="font-medium">{expense.category}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Employee:</span>
                          <p className="font-medium">{expense.employee}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Date:</span>
                          <p className="font-medium">{expense.date}</p>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          Review
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          View Receipt
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employee
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {expenses.map((expense) => (
                        <tr key={expense.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {expense.description}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {expense.amount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {expense.category}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {expense.employee}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {expense.date}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              expense.status === 'Approved' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {expense.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">Review</button>
                            <button className="text-gray-400 hover:text-gray-600">View</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'budgets' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-6">Budget Overview</h3>
                <div className="space-y-6">
                  {budgets.map((budget, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{budget.category}</h4>
                        <div className="text-sm text-gray-500">
                          ${budget.spent.toLocaleString()} of ${budget.allocated.toLocaleString()}
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                        <div 
                          className={`h-3 rounded-full ${
                            (budget.spent / budget.allocated) > 0.9 ? 'bg-red-500' :
                            (budget.spent / budget.allocated) > 0.7 ? 'bg-orange-500' : 'bg-green-500'
                          }`}
                          style={{ width: `${Math.min((budget.spent / budget.allocated) * 100, 100)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">
                          Remaining: ${budget.remaining.toLocaleString()}
                        </span>
                        <span className={`font-medium ${
                          (budget.spent / budget.allocated) > 0.9 ? 'text-red-600' :
                          (budget.spent / budget.allocated) > 0.7 ? 'text-orange-600' : 'text-green-600'
                        }`}>
                          {Math.round((budget.spent / budget.allocated) * 100)}% used
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'invoices' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Vendor Invoices</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{invoice.vendor}</h4>
                          <p className="text-sm text-gray-500">
                            {invoice.id} • Due: {invoice.dueDate}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className="text-lg font-semibold text-navy">{invoice.amount}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :
                            invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :
                            'bg-orange-100 text-orange-800'
                          }`}>
                            {invoice.status}
                          </span>
                          <button className="text-purple hover:text-darkpurple text-sm font-medium">
                            {invoice.status === 'Paid' ? 'View' : 'Pay Now'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <h3 className="text-lg font-semibold text-navy mb-6">Financial Reports</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Profit & Loss</h4>
                    <p className="text-sm text-gray-500">Monthly P&L statement</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Cash Flow</h4>
                    <p className="text-sm text-gray-500">Cash flow analysis</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Budget vs Actual</h4>
                    <p className="text-sm text-gray-500">Budget performance report</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Expense Analysis</h4>
                    <p className="text-sm text-gray-500">Detailed expense breakdown</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Tax Summary</h4>
                    <p className="text-sm text-gray-500">Tax preparation report</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Vendor Analysis</h4>
                    <p className="text-sm text-gray-500">Vendor spending analysis</p>
                  </button>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
