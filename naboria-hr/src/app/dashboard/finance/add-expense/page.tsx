'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function AddExpense() {
  const [expenseData, setExpenseData] = useState({
    // Basic Information
    title: '',
    description: '',
    amount: '',
    currency: 'USD',
    date: '',
    
    // Categorization
    category: '',
    subcategory: '',
    department: '',
    project: '',
    
    // Details
    vendor: '',
    paymentMethod: '',
    reference: '',
    taxAmount: '',
    
    // Approval
    approver: '',
    businessJustification: '',
    
    // Receipts
    receipts: [],
    
    // Status
    status: 'Draft',
    recurring: false,
    recurringFrequency: '',
    recurringEndDate: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [receiptFiles, setReceiptFiles] = useState<File[]>([]);

  const categories = [
    { id: 'travel', name: 'Travel & Transportation', subcategories: ['Flights', 'Hotels', 'Car Rental', 'Meals', 'Taxi/Uber', 'Parking'] },
    { id: 'office', name: 'Office Supplies', subcategories: ['Stationery', 'Equipment', 'Furniture', 'Software', 'Maintenance'] },
    { id: 'marketing', name: 'Marketing & Advertising', subcategories: ['Digital Ads', 'Print Materials', 'Events', 'Sponsorships', 'Content Creation'] },
    { id: 'professional', name: 'Professional Services', subcategories: ['Legal', 'Consulting', 'Accounting', 'Training', 'Recruitment'] },
    { id: 'utilities', name: 'Utilities & Communications', subcategories: ['Internet', 'Phone', 'Electricity', 'Water', 'Gas'] },
    { id: 'entertainment', name: 'Entertainment & Meals', subcategories: ['Client Meals', 'Team Events', 'Conference Meals', 'Office Catering'] },
    { id: 'technology', name: 'Technology', subcategories: ['Software Licenses', 'Hardware', 'Cloud Services', 'IT Support', 'Security'] },
    { id: 'other', name: 'Other', subcategories: ['Miscellaneous', 'One-time', 'Emergency'] }
  ];

  const departments = [
    'Engineering', 'Marketing', 'Sales', 'Finance', 'HR', 'Operations', 'IT', 'Legal'
  ];

  const projects = [
    'Website Redesign', 'Mobile App Development', 'Q1 Marketing Campaign', 'Office Expansion', 
    'Security Upgrade', 'Training Program', 'Product Launch', 'General Operations'
  ];

  const paymentMethods = [
    'Corporate Credit Card', 'Bank Transfer', 'Check', 'Cash', 'Personal (Reimbursement)', 'Petty Cash'
  ];

  const approvers = [
    'Sarah Johnson - CFO', 'Michael Rodriguez - VP Marketing', 'Emily Davis - VP Engineering', 
    'David Kim - VP Sales', 'Lisa Wang - Finance Manager'
  ];

  const currencies = [
    { code: 'USD', symbol: '$', name: 'US Dollar' },
    { code: 'EUR', symbol: '€', name: 'Euro' },
    { code: 'GBP', symbol: '£', name: 'British Pound' },
    { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' }
  ];

  const selectedCategory = categories.find(cat => cat.id === expenseData.category);

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setReceiptFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setReceiptFiles(prev => prev.filter((_, i) => i !== index));
  };

  const calculateTotalAmount = () => {
    const amount = parseFloat(expenseData.amount) || 0;
    const tax = parseFloat(expenseData.taxAmount) || 0;
    return amount + tax;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    alert('Expense submitted successfully!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/finance" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Add New Expense</h1>
                <p className="text-gray-600 text-sm">Submit a new expense for approval and reimbursement</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-purple">
                {currencies.find(c => c.code === expenseData.currency)?.symbol}
                {calculateTotalAmount().toFixed(2)}
              </p>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Expense Title *</label>
                <input
                  type="text"
                  value={expenseData.title}
                  onChange={(e) => setExpenseData({...expenseData, title: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., Client dinner at Restaurant ABC"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Amount *</label>
                <div className="relative">
                  <select
                    value={expenseData.currency}
                    onChange={(e) => setExpenseData({...expenseData, currency: e.target.value})}
                    className="absolute left-0 top-0 h-full w-20 px-2 border border-gray-300 rounded-l-lg bg-gray-50 text-sm"
                  >
                    {currencies.map(currency => (
                      <option key={currency.code} value={currency.code}>{currency.symbol}</option>
                    ))}
                  </select>
                  <input
                    type="number"
                    step="0.01"
                    value={expenseData.amount}
                    onChange={(e) => setExpenseData({...expenseData, amount: e.target.value})}
                    className="w-full pl-20 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="0.00"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date *</label>
                <input
                  type="date"
                  value={expenseData.date}
                  onChange={(e) => setExpenseData({...expenseData, date: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Vendor/Merchant</label>
                <input
                  type="text"
                  value={expenseData.vendor}
                  onChange={(e) => setExpenseData({...expenseData, vendor: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., Amazon, Starbucks, Delta Airlines"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                <select
                  value={expenseData.paymentMethod}
                  onChange={(e) => setExpenseData({...expenseData, paymentMethod: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Payment Method</option>
                  {paymentMethods.map(method => (
                    <option key={method} value={method}>{method}</option>
                  ))}
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea
                  value={expenseData.description}
                  onChange={(e) => setExpenseData({...expenseData, description: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="Provide details about the expense..."
                />
              </div>
            </div>
          </div>

          {/* Categorization */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Categorization</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                <select
                  value={expenseData.category}
                  onChange={(e) => setExpenseData({...expenseData, category: e.target.value, subcategory: ''})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  required
                >
                  <option value="">Select Category</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subcategory</label>
                <select
                  value={expenseData.subcategory}
                  onChange={(e) => setExpenseData({...expenseData, subcategory: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  disabled={!selectedCategory}
                >
                  <option value="">Select Subcategory</option>
                  {selectedCategory?.subcategories.map(sub => (
                    <option key={sub} value={sub}>{sub}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select
                  value={expenseData.department}
                  onChange={(e) => setExpenseData({...expenseData, department: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Department</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Project</label>
                <select
                  value={expenseData.project}
                  onChange={(e) => setExpenseData({...expenseData, project: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Project</option>
                  {projects.map(project => (
                    <option key={project} value={project}>{project}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Tax & Reference */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Additional Details</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Tax Amount</label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">
                    {currencies.find(c => c.code === expenseData.currency)?.symbol}
                  </span>
                  <input
                    type="number"
                    step="0.01"
                    value={expenseData.taxAmount}
                    onChange={(e) => setExpenseData({...expenseData, taxAmount: e.target.value})}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
                <input
                  type="text"
                  value={expenseData.reference}
                  onChange={(e) => setExpenseData({...expenseData, reference: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="Invoice #, Receipt #, etc."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Approver</label>
                <select
                  value={expenseData.approver}
                  onChange={(e) => setExpenseData({...expenseData, approver: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Auto-assign based on amount</option>
                  {approvers.map(approver => (
                    <option key={approver} value={approver}>{approver}</option>
                  ))}
                </select>
              </div>

              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={expenseData.recurring}
                    onChange={(e) => setExpenseData({...expenseData, recurring: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm font-medium text-gray-700">Recurring Expense</span>
                </label>
              </div>

              {expenseData.recurring && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
                    <select
                      value={expenseData.recurringFrequency}
                      onChange={(e) => setExpenseData({...expenseData, recurringFrequency: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    >
                      <option value="">Select Frequency</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                      <option value="quarterly">Quarterly</option>
                      <option value="annually">Annually</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                    <input
                      type="date"
                      value={expenseData.recurringEndDate}
                      onChange={(e) => setExpenseData({...expenseData, recurringEndDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                </>
              )}

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">Business Justification</label>
                <textarea
                  value={expenseData.businessJustification}
                  onChange={(e) => setExpenseData({...expenseData, businessJustification: e.target.value})}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="Explain the business purpose of this expense..."
                />
              </div>
            </div>
          </div>

          {/* Receipt Upload */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Receipt Upload</h2>
            
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4">
              <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
              <div className="mt-4">
                <label htmlFor="receipt-upload" className="cursor-pointer">
                  <span className="mt-2 block text-sm font-medium text-gray-900">
                    Upload Receipt(s)
                  </span>
                  <span className="mt-1 block text-sm text-gray-500">
                    PNG, JPG, PDF up to 10MB each
                  </span>
                </label>
                <input
                  id="receipt-upload"
                  name="receipt-upload"
                  type="file"
                  className="sr-only"
                  multiple
                  accept=".png,.jpg,.jpeg,.pdf"
                  onChange={handleFileUpload}
                />
              </div>
            </div>

            {receiptFiles.length > 0 && (
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700">Uploaded Files:</h3>
                {receiptFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <span className="text-sm text-gray-900">{file.name}</span>
                      <span className="text-xs text-gray-500 ml-2">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Summary */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Expense Summary</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Expense Amount:</span>
                  <span className="font-medium">
                    {currencies.find(c => c.code === expenseData.currency)?.symbol}
                    {parseFloat(expenseData.amount || '0').toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax Amount:</span>
                  <span className="font-medium">
                    {currencies.find(c => c.code === expenseData.currency)?.symbol}
                    {parseFloat(expenseData.taxAmount || '0').toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between border-t pt-3">
                  <span className="text-lg font-medium text-gray-900">Total Amount:</span>
                  <span className="text-lg font-bold text-purple">
                    {currencies.find(c => c.code === expenseData.currency)?.symbol}
                    {calculateTotalAmount().toFixed(2)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm">
                  <span className="text-gray-600">Category:</span>
                  <span className="ml-2 font-medium">
                    {categories.find(c => c.id === expenseData.category)?.name || 'Not selected'}
                  </span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Department:</span>
                  <span className="ml-2 font-medium">{expenseData.department || 'Not selected'}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Payment Method:</span>
                  <span className="ml-2 font-medium">{expenseData.paymentMethod || 'Not selected'}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Receipts:</span>
                  <span className="ml-2 font-medium">{receiptFiles.length} file(s)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <Link
              href="/dashboard/finance"
              className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Cancel
            </Link>
            
            <div className="flex space-x-3">
              <button
                type="button"
                className="border border-purple text-purple hover:bg-purple hover:text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Save as Draft
              </button>
              
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Submitting...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Submit Expense
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
