'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function GenerateFinanceReport() {
  const [reportConfig, setReportConfig] = useState({
    reportType: 'expense-summary',
    dateRange: 'current-month',
    startDate: '',
    endDate: '',
    department: 'all',
    category: 'all',
    format: 'pdf',
    includeCharts: true,
    includeDetails: true,
    groupBy: 'department'
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const reportTypes = [
    { id: 'expense-summary', name: 'Expense Summary Report', description: 'Overview of all expenses by category and department' },
    { id: 'budget-analysis', name: 'Budget Analysis Report', description: 'Budget vs actual spending analysis' },
    { id: 'vendor-spending', name: 'Vendor Spending Report', description: 'Spending breakdown by vendor/supplier' },
    { id: 'department-costs', name: 'Department Cost Report', description: 'Cost analysis by department' },
    { id: 'project-financials', name: 'Project Financial Report', description: 'Financial performance by project' },
    { id: 'cash-flow', name: 'Cash Flow Report', description: 'Cash flow analysis and projections' },
    { id: 'profit-loss', name: 'Profit & Loss Statement', description: 'P&L statement for specified period' },
    { id: 'tax-report', name: 'Tax Report', description: 'Tax-related expenses and deductions' }
  ];

  const categories = [
    'All Categories', 'Travel & Transportation', 'Office Supplies', 'Marketing & Advertising',
    'Professional Services', 'Utilities & Communications', 'Entertainment & Meals', 'Technology', 'Other'
  ];

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    await new Promise(resolve => setTimeout(resolve, 3000));
    setIsGenerating(false);
    alert('Finance report generated successfully!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/finance" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Generate Finance Report</h1>
                <p className="text-gray-600 text-sm">Create detailed financial reports and analytics</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Report Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* Report Type */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Report Type</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {reportTypes.map((type) => (
                  <div
                    key={type.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      reportConfig.reportType === type.id
                        ? 'border-purple bg-purple/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setReportConfig({...reportConfig, reportType: type.id})}
                  >
                    <h3 className="font-medium text-gray-900 mb-1">{type.name}</h3>
                    <p className="text-sm text-gray-500">{type.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Filters & Options</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                  <select
                    value={reportConfig.dateRange}
                    onChange={(e) => setReportConfig({...reportConfig, dateRange: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="current-month">Current Month</option>
                    <option value="last-month">Last Month</option>
                    <option value="current-quarter">Current Quarter</option>
                    <option value="last-quarter">Last Quarter</option>
                    <option value="current-year">Current Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                  <select
                    value={reportConfig.department}
                    onChange={(e) => setReportConfig({...reportConfig, department: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="all">All Departments</option>
                    <option value="engineering">Engineering</option>
                    <option value="marketing">Marketing</option>
                    <option value="sales">Sales</option>
                    <option value="finance">Finance</option>
                    <option value="hr">HR</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={reportConfig.category}
                    onChange={(e) => setReportConfig({...reportConfig, category: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    {categories.map(category => (
                      <option key={category} value={category.toLowerCase().replace(/\s+/g, '-')}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
                  <select
                    value={reportConfig.format}
                    onChange={(e) => setReportConfig({...reportConfig, format: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel (XLSX)</option>
                    <option value="csv">CSV</option>
                  </select>
                </div>
              </div>

              {reportConfig.dateRange === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input
                      type="date"
                      value={reportConfig.startDate}
                      onChange={(e) => setReportConfig({...reportConfig, startDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                    <input
                      type="date"
                      value={reportConfig.endDate}
                      onChange={(e) => setReportConfig({...reportConfig, endDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                </div>
              )}

              <div className="mt-6 space-y-3">
                <h3 className="text-sm font-medium text-gray-700">Include in Report:</h3>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeCharts}
                    onChange={(e) => setReportConfig({...reportConfig, includeCharts: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Charts and visualizations</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeDetails}
                    onChange={(e) => setReportConfig({...reportConfig, includeDetails: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Detailed transaction breakdown</span>
                </label>
              </div>
            </div>
          </div>

          {/* Preview & Actions */}
          <div className="space-y-6">
            {/* Preview */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Report Preview</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Report Type</h3>
                  <p className="text-lg font-medium text-gray-900">
                    {reportTypes.find(r => r.id === reportConfig.reportType)?.name}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Period</h3>
                  <p className="text-gray-900">
                    {reportConfig.dateRange === 'custom' && reportConfig.startDate && reportConfig.endDate
                      ? `${reportConfig.startDate} to ${reportConfig.endDate}`
                      : reportConfig.dateRange.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())
                    }
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Scope</h3>
                  <p className="text-gray-900">
                    {reportConfig.department === 'all' ? 'All Departments' : reportConfig.department}
                  </p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Format</h3>
                  <p className="text-gray-900">{reportConfig.format.toUpperCase()}</p>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Financial Summary</h2>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Expenses</span>
                  <span className="font-medium text-red-600">$247,850</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Budget Allocated</span>
                  <span className="font-medium text-blue-600">$300,000</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Remaining Budget</span>
                  <span className="font-medium text-green-600">$52,150</span>
                </div>
                <div className="flex justify-between items-center border-t pt-3">
                  <span className="text-sm text-gray-600">Budget Utilization</span>
                  <span className="font-medium text-purple">82.6%</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Actions</h2>
              
              <div className="space-y-3">
                <button
                  onClick={handleGenerateReport}
                  disabled={isGenerating}
                  className="w-full bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
                >
                  {isGenerating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating Report...
                    </>
                  ) : (
                    <>
                      <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Generate Report
                    </>
                  )}
                </button>

                <button className="w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors">
                  Save Configuration
                </button>

                <button className="w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors">
                  Schedule Report
                </button>
              </div>
            </div>

            {/* Recent Reports */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Recent Reports</h2>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Expense Summary</p>
                    <p className="text-xs text-gray-500">December 2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Budget Analysis</p>
                    <p className="text-xs text-gray-500">Q4 2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">P&L Statement</p>
                    <p className="text-xs text-gray-500">2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
