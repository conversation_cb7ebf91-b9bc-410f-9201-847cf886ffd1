'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function AddWorkflow() {
  const [workflowData, setWorkflowData] = useState({
    name: '',
    description: '',
    trigger: '',
    triggerConditions: {},
    actions: [],
    isActive: true,
    category: '',
    priority: 'medium'
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [isCreating, setIsCreating] = useState(false);

  const triggers = [
    { id: 'employee-onboard', name: 'New Employee Onboarding', description: 'Triggered when a new employee is added' },
    { id: 'expense-submit', name: 'Expense Submission', description: 'Triggered when an expense is submitted' },
    { id: 'time-off-request', name: 'Time Off Request', description: 'Triggered when time off is requested' },
    { id: 'device-assignment', name: 'Device Assignment', description: 'Triggered when a device is assigned' },
    { id: 'payroll-run', name: 'Payroll Processing', description: 'Triggered during payroll runs' },
    { id: 'compliance-due', name: 'Compliance Due Date', description: 'Triggered when compliance training is due' },
    { id: 'performance-review', name: 'Performance Review', description: 'Triggered for performance review cycles' },
    { id: 'schedule', name: 'Scheduled Trigger', description: 'Triggered on a schedule (daily, weekly, monthly)' }
  ];

  const actionTypes = [
    { id: 'send-email', name: 'Send Email', description: 'Send automated email notifications', icon: '📧' },
    { id: 'create-task', name: 'Create Task', description: 'Create tasks for team members', icon: '✅' },
    { id: 'update-record', name: 'Update Record', description: 'Update employee or system records', icon: '📝' },
    { id: 'send-slack', name: 'Send Slack Message', description: 'Send notifications to Slack channels', icon: '💬' },
    { id: 'generate-report', name: 'Generate Report', description: 'Automatically generate reports', icon: '📊' },
    { id: 'approve-request', name: 'Auto-Approve', description: 'Automatically approve certain requests', icon: '✅' },
    { id: 'assign-device', name: 'Assign Device', description: 'Automatically assign devices to employees', icon: '💻' },
    { id: 'schedule-meeting', name: 'Schedule Meeting', description: 'Schedule meetings or interviews', icon: '📅' }
  ];

  const categories = [
    'HR & Onboarding', 'Finance & Expenses', 'IT & Equipment', 'Compliance & Training', 
    'Payroll & Benefits', 'Performance Management', 'General Operations'
  ];

  const addAction = (actionType: string) => {
    const newAction = {
      id: Date.now(),
      type: actionType,
      config: {},
      order: workflowData.actions.length + 1
    };
    setWorkflowData({
      ...workflowData,
      actions: [...workflowData.actions, newAction]
    });
  };

  const removeAction = (actionId: number) => {
    setWorkflowData({
      ...workflowData,
      actions: workflowData.actions.filter(action => action.id !== actionId)
    });
  };

  const handleCreateWorkflow = async () => {
    setIsCreating(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsCreating(false);
    alert('Workflow created successfully!');
  };

  const selectedTrigger = triggers.find(t => t.id === workflowData.trigger);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/automation" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Create New Workflow</h1>
                <p className="text-gray-600 text-sm">Build automated workflows to streamline your processes</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Step {currentStep} of 4</p>
              <p className="text-lg font-semibold text-purple">{Math.round((currentStep / 4) * 100)}%</p>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep > step ? 'bg-green-500 text-white' :
                  currentStep === step ? 'bg-purple text-white' :
                  'bg-gray-200 text-gray-600'
                }`}>
                  {currentStep > step ? (
                    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    step
                  )}
                </div>
                {step < 4 && (
                  <div className={`w-16 h-1 mx-2 ${
                    currentStep > step ? 'bg-green-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-navy">
              {currentStep === 1 && 'Basic Information'}
              {currentStep === 2 && 'Choose Trigger'}
              {currentStep === 3 && 'Configure Actions'}
              {currentStep === 4 && 'Review & Create'}
            </h2>
          </div>
        </div>

        {/* Step Content */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          {currentStep === 1 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Workflow Details</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Workflow Name *</label>
                  <input
                    type="text"
                    value={workflowData.name}
                    onChange={(e) => setWorkflowData({...workflowData, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="e.g., New Employee Onboarding Automation"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select
                    value={workflowData.category}
                    onChange={(e) => setWorkflowData({...workflowData, category: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="">Select Category</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                  <select
                    value={workflowData.priority}
                    onChange={(e) => setWorkflowData({...workflowData, priority: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    value={workflowData.description}
                    onChange={(e) => setWorkflowData({...workflowData, description: e.target.value})}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="Describe what this workflow does and when it should run..."
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={workflowData.isActive}
                      onChange={(e) => setWorkflowData({...workflowData, isActive: e.target.checked})}
                      className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                    />
                    <span className="text-sm font-medium text-gray-700">Activate workflow immediately after creation</span>
                  </label>
                </div>
              </div>
            </div>
          )}

          {currentStep === 2 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Choose Trigger Event</h3>
              <p className="text-gray-600">Select what event will start this workflow</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {triggers.map((trigger) => (
                  <div
                    key={trigger.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      workflowData.trigger === trigger.id
                        ? 'border-purple bg-purple/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setWorkflowData({...workflowData, trigger: trigger.id})}
                  >
                    <h4 className="font-medium text-gray-900 mb-1">{trigger.name}</h4>
                    <p className="text-sm text-gray-500">{trigger.description}</p>
                  </div>
                ))}
              </div>

              {selectedTrigger && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 mb-2">Selected Trigger: {selectedTrigger.name}</h4>
                  <p className="text-blue-700 text-sm">{selectedTrigger.description}</p>
                </div>
              )}
            </div>
          )}

          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Configure Actions</h3>
                  <p className="text-gray-600">Add actions that will be executed when the trigger occurs</p>
                </div>
                <span className="bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium">
                  {workflowData.actions.length} action(s)
                </span>
              </div>

              {/* Available Actions */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">Available Actions</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                  {actionTypes.map((actionType) => (
                    <button
                      key={actionType.id}
                      onClick={() => addAction(actionType.id)}
                      className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <div className="text-2xl mb-1">{actionType.icon}</div>
                      <h5 className="font-medium text-gray-900 text-sm">{actionType.name}</h5>
                      <p className="text-xs text-gray-500">{actionType.description}</p>
                    </button>
                  ))}
                </div>
              </div>

              {/* Selected Actions */}
              {workflowData.actions.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Workflow Actions</h4>
                  <div className="space-y-3">
                    {workflowData.actions.map((action, index) => {
                      const actionType = actionTypes.find(at => at.id === action.type);
                      return (
                        <div key={action.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div className="flex items-center">
                            <span className="text-2xl mr-3">{actionType?.icon}</span>
                            <div>
                              <h5 className="font-medium text-gray-900">{actionType?.name}</h5>
                              <p className="text-sm text-gray-500">Step {index + 1}: {actionType?.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <button className="text-purple hover:text-darkpurple text-sm font-medium">
                              Configure
                            </button>
                            <button
                              onClick={() => removeAction(action.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          {currentStep === 4 && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Review Workflow</h3>
              
              <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">Workflow Name</h4>
                  <p className="text-gray-600">{workflowData.name || 'Untitled Workflow'}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900">Description</h4>
                  <p className="text-gray-600">{workflowData.description || 'No description provided'}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900">Category</h4>
                    <p className="text-gray-600">{workflowData.category || 'Not specified'}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Priority</h4>
                    <p className="text-gray-600 capitalize">{workflowData.priority}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Status</h4>
                    <p className="text-gray-600">{workflowData.isActive ? 'Active' : 'Inactive'}</p>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900">Trigger</h4>
                  <p className="text-gray-600">{selectedTrigger?.name || 'No trigger selected'}</p>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900">Actions ({workflowData.actions.length})</h4>
                  <div className="space-y-2 mt-2">
                    {workflowData.actions.map((action, index) => {
                      const actionType = actionTypes.find(at => at.id === action.type);
                      return (
                        <div key={action.id} className="flex items-center">
                          <span className="text-lg mr-2">{actionType?.icon}</span>
                          <span className="text-gray-600">{index + 1}. {actionType?.name}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <p className="text-green-800 font-medium">Ready to Create</p>
                    <p className="text-green-700 text-sm">Your workflow is configured and ready to be created.</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
              disabled={currentStep === 1}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
              </svg>
              Previous
            </button>
            
            {currentStep < 4 ? (
              <button
                onClick={() => setCurrentStep(Math.min(4, currentStep + 1))}
                className="flex items-center px-4 py-2 bg-purple hover:bg-darkpurple text-white rounded-lg font-medium"
              >
                Next
                <svg className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            ) : (
              <button
                onClick={handleCreateWorkflow}
                disabled={isCreating}
                className="flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium"
              >
                {isCreating ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Create Workflow
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
