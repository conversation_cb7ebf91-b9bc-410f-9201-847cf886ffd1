'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function AutomationManagement() {
  const [activeTab, setActiveTab] = useState('overview');

  const workflows = [
    {
      id: 1,
      name: 'Employee Onboarding',
      description: 'Automated new hire setup and account creation',
      status: 'Active',
      triggers: 12,
      lastRun: '2024-01-15 09:30',
      success: 98.5
    },
    {
      id: 2,
      name: 'Expense Approval',
      description: 'Auto-approve expenses under $500',
      status: 'Active',
      triggers: 45,
      lastRun: '2024-01-15 14:22',
      success: 100
    },
    {
      id: 3,
      name: 'IT Equipment Request',
      description: 'Automated equipment provisioning workflow',
      status: 'Active',
      triggers: 8,
      lastRun: '2024-01-14 16:45',
      success: 95.2
    },
    {
      id: 4,
      name: 'Payroll Processing',
      description: 'Monthly payroll calculation and distribution',
      status: 'Scheduled',
      triggers: 1,
      lastRun: '2023-12-31 23:59',
      success: 100
    }
  ];

  const integrations = [
    { name: 'Slack', status: 'Connected', type: 'Communication', lastSync: '2024-01-15 15:30' },
    { name: 'Google Workspace', status: 'Connected', type: 'Productivity', lastSync: '2024-01-15 15:25' },
    { name: 'Microsoft 365', status: 'Connected', type: 'Productivity', lastSync: '2024-01-15 15:20' },
    { name: 'Salesforce', status: 'Connected', type: 'CRM', lastSync: '2024-01-15 15:15' },
    { name: 'QuickBooks', status: 'Connected', type: 'Finance', lastSync: '2024-01-15 15:10' },
    { name: 'Jira', status: 'Error', type: 'Project Management', lastSync: '2024-01-15 12:00' }
  ];

  const automationRules = [
    {
      id: 1,
      name: 'Auto-assign IT equipment',
      condition: 'New employee in Engineering department',
      action: 'Create equipment request for laptop and monitor',
      enabled: true
    },
    {
      id: 2,
      name: 'Expense auto-approval',
      condition: 'Expense amount < $500 AND category = Office Supplies',
      action: 'Automatically approve and process payment',
      enabled: true
    },
    {
      id: 3,
      name: 'Time-off notification',
      condition: 'Time-off request submitted',
      action: 'Notify manager and update calendar',
      enabled: true
    },
    {
      id: 4,
      name: 'Security alert',
      condition: 'Failed login attempts > 5',
      action: 'Lock account and notify IT security',
      enabled: false
    }
  ];

  const recentActivity = [
    {
      id: 1,
      workflow: 'Employee Onboarding',
      action: 'Created accounts for Emily Johnson',
      timestamp: '2024-01-15 09:30',
      status: 'Success'
    },
    {
      id: 2,
      workflow: 'Expense Approval',
      action: 'Auto-approved $245 office supplies expense',
      timestamp: '2024-01-15 14:22',
      status: 'Success'
    },
    {
      id: 3,
      workflow: 'IT Equipment Request',
      action: 'Provisioned MacBook for new hire',
      timestamp: '2024-01-14 16:45',
      status: 'Success'
    },
    {
      id: 4,
      workflow: 'Security Alert',
      action: 'Detected suspicious login attempt',
      timestamp: '2024-01-14 11:20',
      status: 'Warning'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Automation Center</h1>
                <p className="text-gray-600 text-sm">Manage workflows, integrations, and automated processes</p>
              </div>
            </div>
            <button className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">
              Create Workflow
            </button>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              <button
                onClick={() => setActiveTab('workflows')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'workflows' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                Workflows
                <span className="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                  {workflows.filter(w => w.status === 'Active').length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('integrations')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'integrations' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Integrations
                <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {integrations.filter(i => i.status === 'Error').length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('rules')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'rules' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                Rules
              </button>
              <button
                onClick={() => setActiveTab('activity')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'activity' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Activity Log
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Workflows</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">
                        {workflows.filter(w => w.status === 'Active').length}
                      </p>
                      <p className="text-sm text-green-600">All running smoothly</p>
                    </div>
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Tasks Automated</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">1,247</p>
                      <p className="text-sm text-blue-600">This month</p>
                    </div>
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Time Saved</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">156h</p>
                      <p className="text-sm text-purple">This month</p>
                    </div>
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Success Rate</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">98.4%</p>
                      <p className="text-sm text-green-600">Excellent</p>
                    </div>
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <div className="w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3">
                      <svg className="h-4 w-4 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">Create Workflow</h4>
                    <p className="text-sm text-gray-500">Build new automation</p>
                  </button>
                  
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <div className="w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3">
                      <svg className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 9l3 3-3 3m5 0h3" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">Add Integration</h4>
                    <p className="text-sm text-gray-500">Connect new service</p>
                  </button>
                  
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <div className="w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3">
                      <svg className="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">Create Rule</h4>
                    <p className="text-sm text-gray-500">Set up automation rule</p>
                  </button>
                  
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <div className="w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3">
                      <svg className="h-4 w-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-1">View Reports</h4>
                    <p className="text-sm text-gray-500">Automation analytics</p>
                  </button>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Recent Activity</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {recentActivity.slice(0, 5).map((activity) => (
                    <div key={activity.id} className="p-4 lg:p-6">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          activity.status === 'Success' ? 'bg-green/10' :
                          activity.status === 'Warning' ? 'bg-orange/10' : 'bg-red/10'
                        }`}>
                          <svg className={`h-4 w-4 ${
                            activity.status === 'Success' ? 'text-green-500' :
                            activity.status === 'Warning' ? 'text-orange-500' : 'text-red-500'
                          }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {activity.status === 'Success' ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            )}
                          </svg>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                          <p className="text-xs text-gray-500">{activity.workflow} • {activity.timestamp}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          activity.status === 'Success' ? 'bg-green-100 text-green-800' :
                          activity.status === 'Warning' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {activity.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'workflows' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Automation Workflows</h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {workflows.map((workflow) => (
                    <div key={workflow.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{workflow.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          workflow.status === 'Active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {workflow.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 mb-3">{workflow.description}</p>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Triggers:</span>
                          <p className="font-medium">{workflow.triggers}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Success Rate:</span>
                          <p className="font-medium">{workflow.success}%</p>
                        </div>
                        <div className="col-span-2">
                          <span className="text-gray-500">Last Run:</span>
                          <p className="font-medium">{workflow.lastRun}</p>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          Edit
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          View Logs
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Workflow
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Triggers
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Success Rate
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Last Run
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {workflows.map((workflow) => (
                        <tr key={workflow.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">{workflow.name}</div>
                              <div className="text-sm text-gray-500">{workflow.description}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              workflow.status === 'Active' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {workflow.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {workflow.triggers}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {workflow.success}%
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {workflow.lastRun}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">Edit</button>
                            <button className="text-gray-400 hover:text-gray-600">View Logs</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'integrations' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">System Integrations</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {integrations.map((integration, index) => (
                    <div key={index} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{integration.name}</h4>
                          <p className="text-sm text-gray-500">
                            {integration.type} • Last sync: {integration.lastSync}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            integration.status === 'Connected' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {integration.status}
                          </span>
                          <button className="text-purple hover:text-darkpurple text-sm font-medium">
                            {integration.status === 'Connected' ? 'Configure' : 'Reconnect'}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rules' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Automation Rules</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {automationRules.map((rule) => (
                    <div key={rule.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-start justify-between space-y-3 lg:space-y-0">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="font-medium text-gray-900">{rule.name}</h4>
                            <label className="relative inline-flex items-center cursor-pointer">
                              <input 
                                type="checkbox" 
                                checked={rule.enabled} 
                                className="sr-only peer" 
                                readOnly
                              />
                              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple"></div>
                            </label>
                          </div>
                          <p className="text-sm text-gray-600 mb-1">
                            <strong>When:</strong> {rule.condition}
                          </p>
                          <p className="text-sm text-gray-600">
                            <strong>Then:</strong> {rule.action}
                          </p>
                        </div>
                        <button className="text-purple hover:text-darkpurple text-sm font-medium">
                          Edit Rule
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'activity' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Activity Log</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="p-4 lg:p-6">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          activity.status === 'Success' ? 'bg-green/10' :
                          activity.status === 'Warning' ? 'bg-orange/10' : 'bg-red/10'
                        }`}>
                          <svg className={`h-4 w-4 ${
                            activity.status === 'Success' ? 'text-green-500' :
                            activity.status === 'Warning' ? 'text-orange-500' : 'text-red-500'
                          }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            {activity.status === 'Success' ? (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                            ) : (
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            )}
                          </svg>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                          <p className="text-xs text-gray-500">{activity.workflow} • {activity.timestamp}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          activity.status === 'Success' ? 'bg-green-100 text-green-800' :
                          activity.status === 'Warning' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {activity.status}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
