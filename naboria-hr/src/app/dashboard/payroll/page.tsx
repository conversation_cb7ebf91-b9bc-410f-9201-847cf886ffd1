'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function PayrollManagement() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('current');

  const payrollRuns = [
    {
      id: 1,
      period: 'December 2024',
      status: 'Completed',
      employees: 247,
      totalAmount: '$1,234,567',
      runDate: '2024-12-31',
      payDate: '2025-01-05'
    },
    {
      id: 2,
      period: 'November 2024',
      status: 'Completed',
      employees: 245,
      totalAmount: '$1,198,432',
      runDate: '2024-11-30',
      payDate: '2024-12-05'
    },
    {
      id: 3,
      period: 'January 2025',
      status: 'In Progress',
      employees: 250,
      totalAmount: '$1,267,890',
      runDate: '2025-01-31',
      payDate: '2025-02-05'
    }
  ];

  const upcomingPayments = [
    { employee: '<PERSON>', amount: '$5,200', date: '2025-01-15', type: 'Salary' },
    { employee: '<PERSON>', amount: '$3,800', date: '2025-01-15', type: 'Sal<PERSON>' },
    { employee: '<PERSON>', amount: '$4,100', date: '2025-01-15', type: 'Salary' },
    { employee: 'David Kim', amount: '$1,200', date: '2025-01-20', type: 'Bonus' }
  ];

  const taxSummary = {
    federalTax: '$156,789',
    stateTax: '$89,234',
    socialSecurity: '$76,543',
    medicare: '$17,890',
    unemployment: '$12,345'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Payroll Management</h1>
                <p className="text-gray-600 text-sm">Process payroll, manage taxes, and track payments</p>
              </div>
            </div>
            <button className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">
              Run Payroll
            </button>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              <button
                onClick={() => setActiveTab('runs')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'runs' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Payroll Runs
              </button>
              <button
                onClick={() => setActiveTab('taxes')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'taxes' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                Tax Management
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'reports' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Reports
              </button>
              <button
                onClick={() => setActiveTab('compliance')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'compliance' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Compliance
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Monthly Payroll</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">$1.2M</p>
                      <p className="text-sm text-green-600">+5.2% from last month</p>
                    </div>
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Employees Paid</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">247</p>
                      <p className="text-sm text-blue-600">100% processed</p>
                    </div>
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Tax Withholdings</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">$352K</p>
                      <p className="text-sm text-purple">29.3% of gross</p>
                    </div>
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Next Payroll</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">Jan 31</p>
                      <p className="text-sm text-orange-600">12 days remaining</p>
                    </div>
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Upcoming Payments */}
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Upcoming Payments</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {upcomingPayments.map((payment, index) => (
                    <div key={index} className="p-4 lg:p-6">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{payment.employee}</h4>
                          <p className="text-sm text-gray-500">{payment.type} • Due {payment.date}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-navy">{payment.amount}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'runs' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Payroll Runs</h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {payrollRuns.map((run) => (
                    <div key={run.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{run.period}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          run.status === 'Completed' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {run.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Employees:</span>
                          <p className="font-medium">{run.employees}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Total:</span>
                          <p className="font-medium">{run.totalAmount}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Run Date:</span>
                          <p className="font-medium">{run.runDate}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Pay Date:</span>
                          <p className="font-medium">{run.payDate}</p>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          View Details
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          Download
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Period
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employees
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total Amount
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Pay Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {payrollRuns.map((run) => (
                        <tr key={run.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {run.period}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              run.status === 'Completed' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {run.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {run.employees}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {run.totalAmount}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {run.payDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">View</button>
                            <button className="text-gray-400 hover:text-gray-600">Download</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'taxes' && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <h3 className="text-lg font-semibold text-navy mb-6">Tax Summary (YTD)</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Federal Tax</p>
                    <p className="text-xl font-bold text-navy">{taxSummary.federalTax}</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">State Tax</p>
                    <p className="text-xl font-bold text-navy">{taxSummary.stateTax}</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Social Security</p>
                    <p className="text-xl font-bold text-navy">{taxSummary.socialSecurity}</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Medicare</p>
                    <p className="text-xl font-bold text-navy">{taxSummary.medicare}</p>
                  </div>
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-600">Unemployment</p>
                    <p className="text-xl font-bold text-navy">{taxSummary.unemployment}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <h3 className="text-lg font-semibold text-navy mb-6">Payroll Reports</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Payroll Summary</h4>
                    <p className="text-sm text-gray-500">Detailed payroll breakdown by period</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Tax Reports</h4>
                    <p className="text-sm text-gray-500">Federal and state tax filings</p>
                  </button>
                  <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                    <h4 className="font-medium text-gray-900 mb-2">Employee Earnings</h4>
                    <p className="text-sm text-gray-500">Individual employee pay statements</p>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <h3 className="text-lg font-semibold text-navy mb-6">Compliance Status</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-medium">Federal Tax Filing</span>
                    </div>
                    <span className="text-green-600 text-sm">Up to date</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-medium">State Compliance</span>
                    </div>
                    <span className="text-green-600 text-sm">Compliant</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-5 w-5 text-orange-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span className="font-medium">Quarterly Filing</span>
                    </div>
                    <span className="text-orange-600 text-sm">Due in 15 days</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
