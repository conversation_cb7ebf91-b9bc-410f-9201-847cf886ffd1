'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function GeneratePayrollReport() {
  const [reportConfig, setReportConfig] = useState({
    reportType: 'payroll-summary',
    dateRange: 'current-month',
    startDate: '',
    endDate: '',
    department: 'all',
    format: 'pdf',
    includeDetails: true,
    includeTaxBreakdown: true,
    includeDeductions: true,
    groupBy: 'department'
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const reportTypes = [
    { id: 'payroll-summary', name: 'Payroll Summary Report', description: 'Overview of payroll costs and employee payments' },
    { id: 'tax-report', name: 'Tax Liability Report', description: 'Federal, state, and local tax obligations' },
    { id: 'employee-earnings', name: 'Employee Earnings Report', description: 'Detailed earnings breakdown by employee' },
    { id: 'deductions-report', name: 'Deductions Report', description: 'All payroll deductions and benefits' },
    { id: 'overtime-report', name: 'Overtime Report', description: 'Overtime hours and payments analysis' },
    { id: 'year-end-report', name: 'Year-End Report', description: 'Annual payroll summary for tax filing' },
    { id: 'cost-center', name: 'Cost Center Report', description: 'Payroll costs by department/cost center' },
    { id: 'compliance-report', name: 'Compliance Report', description: 'Regulatory compliance and audit trail' }
  ];

  const dateRanges = [
    { id: 'current-month', name: 'Current Month' },
    { id: 'last-month', name: 'Last Month' },
    { id: 'current-quarter', name: 'Current Quarter' },
    { id: 'last-quarter', name: 'Last Quarter' },
    { id: 'current-year', name: 'Current Year' },
    { id: 'last-year', name: 'Last Year' },
    { id: 'custom', name: 'Custom Date Range' }
  ];

  const departments = [
    { id: 'all', name: 'All Departments' },
    { id: 'engineering', name: 'Engineering' },
    { id: 'marketing', name: 'Marketing' },
    { id: 'sales', name: 'Sales' },
    { id: 'finance', name: 'Finance' },
    { id: 'hr', name: 'Human Resources' },
    { id: 'operations', name: 'Operations' }
  ];

  const handleGenerateReport = async () => {
    setIsGenerating(true);
    
    // Simulate report generation
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    setIsGenerating(false);
    
    // In a real app, this would download the report
    const reportName = reportTypes.find(r => r.id === reportConfig.reportType)?.name || 'Report';
    alert(`${reportName} generated successfully! Download will start automatically.`);
  };

  const getReportPreview = () => {
    const reportType = reportTypes.find(r => r.id === reportConfig.reportType);
    const dateRange = dateRanges.find(r => r.id === reportConfig.dateRange);
    const department = departments.find(d => d.id === reportConfig.department);
    
    return {
      title: reportType?.name || 'Report',
      description: reportType?.description || '',
      period: dateRange?.name || 'Custom Range',
      scope: department?.name || 'All Departments',
      format: reportConfig.format.toUpperCase()
    };
  };

  const preview = getReportPreview();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/payroll" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Generate Payroll Report</h1>
                <p className="text-gray-600 text-sm">Create detailed payroll and tax reports</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Report Configuration */}
          <div className="lg:col-span-2 space-y-6">
            {/* Report Type Selection */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Report Type</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {reportTypes.map((type) => (
                  <div
                    key={type.id}
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${
                      reportConfig.reportType === type.id
                        ? 'border-purple bg-purple/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setReportConfig({...reportConfig, reportType: type.id})}
                  >
                    <h3 className="font-medium text-gray-900 mb-1">{type.name}</h3>
                    <p className="text-sm text-gray-500">{type.description}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Date Range</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6">
                {dateRanges.map((range) => (
                  <button
                    key={range.id}
                    onClick={() => setReportConfig({...reportConfig, dateRange: range.id})}
                    className={`p-3 text-sm font-medium rounded-lg transition-colors ${
                      reportConfig.dateRange === range.id
                        ? 'bg-purple text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {range.name}
                  </button>
                ))}
              </div>

              {reportConfig.dateRange === 'custom' && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                    <input
                      type="date"
                      value={reportConfig.startDate}
                      onChange={(e) => setReportConfig({...reportConfig, startDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                    <input
                      type="date"
                      value={reportConfig.endDate}
                      onChange={(e) => setReportConfig({...reportConfig, endDate: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Filters */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Filters & Options</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                  <select
                    value={reportConfig.department}
                    onChange={(e) => setReportConfig({...reportConfig, department: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    {departments.map((dept) => (
                      <option key={dept.id} value={dept.id}>{dept.name}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Group By</label>
                  <select
                    value={reportConfig.groupBy}
                    onChange={(e) => setReportConfig({...reportConfig, groupBy: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="department">Department</option>
                    <option value="employee">Employee</option>
                    <option value="pay-period">Pay Period</option>
                    <option value="cost-center">Cost Center</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Format</label>
                  <select
                    value={reportConfig.format}
                    onChange={(e) => setReportConfig({...reportConfig, format: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    <option value="pdf">PDF</option>
                    <option value="excel">Excel (XLSX)</option>
                    <option value="csv">CSV</option>
                  </select>
                </div>
              </div>

              <div className="mt-6 space-y-3">
                <h3 className="text-sm font-medium text-gray-700">Include in Report:</h3>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeDetails}
                    onChange={(e) => setReportConfig({...reportConfig, includeDetails: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Detailed employee breakdown</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeTaxBreakdown}
                    onChange={(e) => setReportConfig({...reportConfig, includeTaxBreakdown: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Tax breakdown and liabilities</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeDeductions}
                    onChange={(e) => setReportConfig({...reportConfig, includeDeductions: e.target.checked})}
                    className="mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Deductions and benefits</span>
                </label>
              </div>
            </div>
          </div>

          {/* Report Preview & Actions */}
          <div className="space-y-6">
            {/* Preview */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Report Preview</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">Report Type</h3>
                  <p className="text-lg font-medium text-gray-900">{preview.title}</p>
                  <p className="text-sm text-gray-600">{preview.description}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Period</h3>
                  <p className="text-gray-900">{preview.period}</p>
                  {reportConfig.dateRange === 'custom' && reportConfig.startDate && reportConfig.endDate && (
                    <p className="text-sm text-gray-600">
                      {reportConfig.startDate} to {reportConfig.endDate}
                    </p>
                  )}
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Scope</h3>
                  <p className="text-gray-900">{preview.scope}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Format</h3>
                  <p className="text-gray-900">{preview.format}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500">Options</h3>
                  <div className="space-y-1">
                    {reportConfig.includeDetails && (
                      <p className="text-sm text-gray-600">✓ Detailed breakdown</p>
                    )}
                    {reportConfig.includeTaxBreakdown && (
                      <p className="text-sm text-gray-600">✓ Tax breakdown</p>
                    )}
                    {reportConfig.includeDeductions && (
                      <p className="text-sm text-gray-600">✓ Deductions</p>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Report Statistics</h2>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Employees</span>
                  <span className="font-medium">156</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Pay Periods</span>
                  <span className="font-medium">4</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Gross Pay</span>
                  <span className="font-medium text-green-600">$1,247,850</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Taxes</span>
                  <span className="font-medium text-red-600">$374,355</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Net Payroll</span>
                  <span className="font-medium text-blue-600">$873,495</span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Actions</h2>
              
              <div className="space-y-3">
                <button
                  onClick={handleGenerateReport}
                  disabled={isGenerating}
                  className="w-full bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
                >
                  {isGenerating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Generating Report...
                    </>
                  ) : (
                    <>
                      <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Generate Report
                    </>
                  )}
                </button>

                <button className="w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors">
                  Save Configuration
                </button>

                <button className="w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors">
                  Schedule Report
                </button>
              </div>
            </div>

            {/* Recent Reports */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-navy mb-4">Recent Reports</h2>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Payroll Summary</p>
                    <p className="text-xs text-gray-500">December 2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Tax Report</p>
                    <p className="text-xs text-gray-500">Q4 2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>

                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="text-sm font-medium text-gray-900">Year-End Report</p>
                    <p className="text-xs text-gray-500">2023</p>
                  </div>
                  <button className="text-purple hover:text-darkpurple text-sm font-medium">
                    Download
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
