'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function AddDevice() {
  const [deviceData, setDeviceData] = useState({
    // Basic Information
    deviceName: '',
    deviceType: '',
    brand: '',
    model: '',
    serialNumber: '',
    assetTag: '',
    
    // Assignment
    assignedTo: '',
    department: '',
    location: '',
    
    // Technical Details
    operatingSystem: '',
    processor: '',
    memory: '',
    storage: '',
    networkMAC: '',
    ipAddress: '',
    
    // Purchase Information
    vendor: '',
    purchaseDate: '',
    purchasePrice: '',
    warrantyExpiry: '',
    
    // Status
    status: 'Available',
    condition: 'New',
    notes: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const deviceTypes = [
    'Laptop', 'Desktop', 'Monitor', 'Printer', 'Scanner', 'Tablet', 'Smartphone', 
    'Server', 'Network Equipment', 'Projector', 'Camera', 'Audio Equipment', 'Other'
  ];

  const brands = [
    'Apple', 'Dell', 'HP', 'Lenovo', 'Microsoft', 'ASUS', 'Acer', 'Samsung', 
    'LG', 'Canon', 'Epson', 'Cisco', 'Netgear', 'Other'
  ];

  const employees = [
    'Sarah Chen', 'Michael Rodriguez', 'Emily Johnson', 'David Kim', 'Lisa Wang',
    'Alex Thompson', 'Jessica Brown', 'Ryan Miller'
  ];

  const departments = [
    'Engineering', 'Marketing', 'Sales', 'Finance', 'HR', 'Operations', 'IT'
  ];

  const locations = [
    'San Francisco Office', 'New York Office', 'Austin Office', 'Remote', 'Warehouse', 'Data Center'
  ];

  const operatingSystems = [
    'Windows 11', 'Windows 10', 'macOS Sonoma', 'macOS Ventura', 'Ubuntu 22.04', 
    'CentOS 8', 'iOS 17', 'Android 14', 'Other'
  ];

  const generateAssetTag = () => {
    const prefix = deviceData.deviceType.substring(0, 3).toUpperCase() || 'DEV';
    const number = Math.random().toString().substring(2, 8);
    setDeviceData({...deviceData, assetTag: `${prefix}-${number}`});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    alert('Device added successfully!');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/it" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">Add New Device</h1>
                <p className="text-gray-600 text-sm">Register a new device in the inventory system</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Device Name *</label>
                <input
                  type="text"
                  value={deviceData.deviceName}
                  onChange={(e) => setDeviceData({...deviceData, deviceName: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., Sarah's MacBook Pro"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Device Type *</label>
                <select
                  value={deviceData.deviceType}
                  onChange={(e) => setDeviceData({...deviceData, deviceType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  required
                >
                  <option value="">Select Device Type</option>
                  {deviceTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                <select
                  value={deviceData.brand}
                  onChange={(e) => setDeviceData({...deviceData, brand: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Brand</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Model</label>
                <input
                  type="text"
                  value={deviceData.model}
                  onChange={(e) => setDeviceData({...deviceData, model: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., MacBook Pro 16-inch"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Serial Number</label>
                <input
                  type="text"
                  value={deviceData.serialNumber}
                  onChange={(e) => setDeviceData({...deviceData, serialNumber: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="Device serial number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Asset Tag</label>
                <div className="flex">
                  <input
                    type="text"
                    value={deviceData.assetTag}
                    onChange={(e) => setDeviceData({...deviceData, assetTag: e.target.value})}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="LAP-123456"
                  />
                  <button
                    type="button"
                    onClick={generateAssetTag}
                    className="px-4 py-2 bg-purple text-white rounded-r-lg hover:bg-darkpurple transition-colors"
                  >
                    Generate
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Assignment Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Assignment Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Assigned To</label>
                <select
                  value={deviceData.assignedTo}
                  onChange={(e) => setDeviceData({...deviceData, assignedTo: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Unassigned</option>
                  {employees.map(employee => (
                    <option key={employee} value={employee}>{employee}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select
                  value={deviceData.department}
                  onChange={(e) => setDeviceData({...deviceData, department: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Department</option>
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                <select
                  value={deviceData.location}
                  onChange={(e) => setDeviceData({...deviceData, location: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select Location</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Technical Details */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Technical Details</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Operating System</label>
                <select
                  value={deviceData.operatingSystem}
                  onChange={(e) => setDeviceData({...deviceData, operatingSystem: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="">Select OS</option>
                  {operatingSystems.map(os => (
                    <option key={os} value={os}>{os}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Processor</label>
                <input
                  type="text"
                  value={deviceData.processor}
                  onChange={(e) => setDeviceData({...deviceData, processor: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., Apple M3 Pro, Intel i7-13700K"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Memory (RAM)</label>
                <input
                  type="text"
                  value={deviceData.memory}
                  onChange={(e) => setDeviceData({...deviceData, memory: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., 16GB, 32GB"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Storage</label>
                <input
                  type="text"
                  value={deviceData.storage}
                  onChange={(e) => setDeviceData({...deviceData, storage: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., 512GB SSD, 1TB HDD"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">MAC Address</label>
                <input
                  type="text"
                  value={deviceData.networkMAC}
                  onChange={(e) => setDeviceData({...deviceData, networkMAC: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="00:1B:44:11:3A:B7"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">IP Address</label>
                <input
                  type="text"
                  value={deviceData.ipAddress}
                  onChange={(e) => setDeviceData({...deviceData, ipAddress: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="*************"
                />
              </div>
            </div>
          </div>

          {/* Purchase Information */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Purchase Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Vendor</label>
                <input
                  type="text"
                  value={deviceData.vendor}
                  onChange={(e) => setDeviceData({...deviceData, vendor: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  placeholder="e.g., Apple Store, Amazon Business"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Purchase Date</label>
                <input
                  type="date"
                  value={deviceData.purchaseDate}
                  onChange={(e) => setDeviceData({...deviceData, purchaseDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Purchase Price</label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    value={deviceData.purchasePrice}
                    onChange={(e) => setDeviceData({...deviceData, purchasePrice: e.target.value})}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    placeholder="2499.00"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Warranty Expiry</label>
                <input
                  type="date"
                  value={deviceData.warrantyExpiry}
                  onChange={(e) => setDeviceData({...deviceData, warrantyExpiry: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Status & Notes */}
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-navy mb-6">Status & Notes</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={deviceData.status}
                  onChange={(e) => setDeviceData({...deviceData, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="Available">Available</option>
                  <option value="Assigned">Assigned</option>
                  <option value="In Repair">In Repair</option>
                  <option value="Retired">Retired</option>
                  <option value="Lost/Stolen">Lost/Stolen</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Condition</label>
                <select
                  value={deviceData.condition}
                  onChange={(e) => setDeviceData({...deviceData, condition: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                >
                  <option value="New">New</option>
                  <option value="Excellent">Excellent</option>
                  <option value="Good">Good</option>
                  <option value="Fair">Fair</option>
                  <option value="Poor">Poor</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
              <textarea
                value={deviceData.notes}
                onChange={(e) => setDeviceData({...deviceData, notes: e.target.value})}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                placeholder="Additional notes about the device..."
              />
            </div>
          </div>

          {/* Submit Buttons */}
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <Link
              href="/dashboard/it"
              className="border border-gray-300 text-gray-600 hover:bg-gray-50 px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Cancel
            </Link>
            
            <div className="flex space-x-3">
              <button
                type="button"
                className="border border-purple text-purple hover:bg-purple hover:text-white px-6 py-2 rounded-lg font-medium transition-colors"
              >
                Save as Draft
              </button>
              
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Adding Device...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                    Add Device
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
