'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function ITManagement() {
  const [activeTab, setActiveTab] = useState('overview');

  const devices = [
    {
      id: 1,
      name: 'MacBook Pro 16"',
      type: 'Laptop',
      assignedTo: '<PERSON>',
      status: 'Active',
      serialNumber: 'MBP2023001',
      purchaseDate: '2023-01-15',
      warranty: 'Active until 2026'
    },
    {
      id: 2,
      name: 'iPhone 14 Pro',
      type: 'Mobile',
      assignedTo: '<PERSON>',
      status: 'Active',
      serialNumber: 'IP14P2023002',
      purchaseDate: '2023-03-20',
      warranty: 'Active until 2025'
    },
    {
      id: 3,
      name: 'Dell Monitor 27"',
      type: 'Monitor',
      assignedTo: '<PERSON>',
      status: 'Active',
      serialNumber: 'DM27-2023003',
      purchaseDate: '2023-06-10',
      warranty: 'Active until 2026'
    },
    {
      id: 4,
      name: 'Surface Laptop',
      type: 'Laptop',
      assignedTo: 'Unassigned',
      status: 'Available',
      serialNumber: '*********',
      purchaseDate: '2023-08-05',
      warranty: 'Active until 2026'
    }
  ];

  const softwareLicenses = [
    { name: 'Microsoft 365', users: 247, totalLicenses: 250, cost: '$12,350/month', renewal: '2024-12-31' },
    { name: 'Adobe Creative Suite', users: 45, totalLicenses: 50, cost: '$2,250/month', renewal: '2024-06-30' },
    { name: 'Slack Pro', users: 247, totalLicenses: 250, cost: '$1,975/month', renewal: '2024-09-15' },
    { name: 'Zoom Pro', users: 150, totalLicenses: 200, cost: '$3,000/month', renewal: '2024-11-20' }
  ];

  const supportTickets = [
    { id: 1, title: 'Laptop not connecting to WiFi', user: 'David Kim', priority: 'High', status: 'Open', created: '2024-01-15' },
    { id: 2, title: 'Software installation request', user: 'Lisa Wang', priority: 'Medium', status: 'In Progress', created: '2024-01-14' },
    { id: 3, title: 'Email sync issues', user: 'John Smith', priority: 'Low', status: 'Resolved', created: '2024-01-13' }
  ];

  const securityAlerts = [
    { id: 1, type: 'Failed Login Attempts', severity: 'Medium', count: 5, user: '<EMAIL>' },
    { id: 2, type: 'Suspicious Download', severity: 'High', count: 1, user: '<EMAIL>' },
    { id: 3, type: 'VPN Connection from New Location', severity: 'Low', count: 2, user: '<EMAIL>' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">IT Management</h1>
                <p className="text-gray-600 text-sm">Manage devices, software, and IT infrastructure</p>
              </div>
            </div>
            <Link
              href="/dashboard/it/add-device"
              className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto text-center"
            >
              Add Device
            </Link>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Overview
              </button>
              <button
                onClick={() => setActiveTab('devices')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'devices' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
                Devices
              </button>
              <button
                onClick={() => setActiveTab('software')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'software' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                Software
              </button>
              <button
                onClick={() => setActiveTab('support')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'support' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                Support
                <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {supportTickets.filter(t => t.status !== 'Resolved').length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('security')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'security' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                Security
                <span className="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                  {securityAlerts.filter(a => a.severity === 'High').length}
                </span>
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Devices</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">324</p>
                      <p className="text-sm text-green-600">+12 this month</p>
                    </div>
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Software Licenses</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">892</p>
                      <p className="text-sm text-blue-600">89% utilized</p>
                    </div>
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Open Tickets</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">8</p>
                      <p className="text-sm text-orange-600">2 high priority</p>
                    </div>
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Security Score</p>
                      <p className="text-2xl lg:text-3xl font-bold text-navy">94%</p>
                      <p className="text-sm text-green-600">Excellent</p>
                    </div>
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-navy mb-4">Recent Device Activity</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-green/10 rounded-full flex items-center justify-center">
                        <svg className="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">New MacBook assigned</p>
                        <p className="text-xs text-gray-500">Emily Johnson • 2h ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue/10 rounded-full flex items-center justify-center">
                        <svg className="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">Software update completed</p>
                        <p className="text-xs text-gray-500">Microsoft 365 • 4h ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-orange/10 rounded-full flex items-center justify-center">
                        <svg className="h-4 w-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-900">License renewal required</p>
                        <p className="text-xs text-gray-500">Adobe Creative Suite • 6h ago</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold text-navy mb-4">System Health</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Server Uptime</span>
                      <span className="text-sm font-medium text-green-600">99.9%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Network Performance</span>
                      <span className="text-sm font-medium text-green-600">Excellent</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Backup Status</span>
                      <span className="text-sm font-medium text-green-600">Current</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Security Updates</span>
                      <span className="text-sm font-medium text-orange-600">2 pending</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'devices' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Device Inventory</h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden divide-y divide-gray-200">
                  {devices.map((device) => (
                    <div key={device.id} className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{device.name}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          device.status === 'Active' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {device.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Type:</span>
                          <p className="font-medium">{device.type}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Assigned To:</span>
                          <p className="font-medium">{device.assignedTo}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Serial:</span>
                          <p className="font-medium">{device.serialNumber}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Purchase:</span>
                          <p className="font-medium">{device.purchaseDate}</p>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          Edit
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          View
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Device
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Assigned To
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Serial Number
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {devices.map((device) => (
                        <tr key={device.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {device.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {device.type}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {device.assignedTo}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              device.status === 'Active' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {device.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {device.serialNumber}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">Edit</button>
                            <button className="text-gray-400 hover:text-gray-600">View</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'software' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Software Licenses</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {softwareLicenses.map((license, index) => (
                    <div key={index} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{license.name}</h4>
                          <p className="text-sm text-gray-500">
                            {license.users} of {license.totalLicenses} licenses used • Renewal: {license.renewal}
                          </p>
                          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-purple h-2 rounded-full" 
                              style={{ width: `${(license.users / license.totalLicenses) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-navy">{license.cost}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'support' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Support Tickets</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {supportTickets.map((ticket) => (
                    <div key={ticket.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{ticket.title}</h4>
                          <p className="text-sm text-gray-500">
                            {ticket.user} • {ticket.created} • 
                            <span className={`ml-1 px-2 py-1 text-xs rounded-full ${
                              ticket.priority === 'High' ? 'bg-red-100 text-red-800' :
                              ticket.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :
                              'bg-green-100 text-green-800'
                            }`}>
                              {ticket.priority}
                            </span>
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            ticket.status === 'Open' ? 'bg-red-100 text-red-800' :
                            ticket.status === 'In Progress' ? 'bg-orange-100 text-orange-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {ticket.status}
                          </span>
                          <button className="text-purple hover:text-darkpurple text-sm font-medium">
                            View
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Security Alerts</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {securityAlerts.map((alert) => (
                    <div key={alert.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{alert.type}</h4>
                          <p className="text-sm text-gray-500">
                            {alert.user} • {alert.count} occurrence{alert.count > 1 ? 's' : ''}
                          </p>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            alert.severity === 'High' ? 'bg-red-100 text-red-800' :
                            alert.severity === 'Medium' ? 'bg-orange-100 text-orange-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {alert.severity}
                          </span>
                          <button className="text-purple hover:text-darkpurple text-sm font-medium">
                            Investigate
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
