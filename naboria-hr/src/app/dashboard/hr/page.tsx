'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function HRManagement() {
  const [activeTab, setActiveTab] = useState('employees');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');

  const employees = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Senior Software Engineer',
      status: 'Active',
      startDate: '2023-01-15',
      salary: '$120,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-001',
      manager: '<PERSON>',
      location: 'San Francisco, CA',
      phone: '+****************',
      emergencyContact: '<PERSON>',
      benefits: 'Premium Health, Dental, 401k',
      performance: 'Exceeds Expectations',
      lastReview: '2023-12-15'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: 'micha<PERSON>.<EMAIL>',
      department: 'Marketing',
      position: 'Marketing Manager',
      status: 'Active',
      startDate: '2023-03-20',
      salary: '$85,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-002',
      manager: '<PERSON>',
      location: 'New York, NY',
      phone: '+****************',
      emergencyContact: 'Maria <PERSON> - Spouse',
      benefits: 'Basic Health, Dental, 401k',
      performance: 'Meets Expectations',
      lastReview: '2023-11-20'
    },
    {
      id: 3,
      name: 'Emily Johnson',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'Frontend Developer',
      status: 'Active',
      startDate: '2023-06-10',
      salary: '$95,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-003',
      manager: 'Sarah Chen',
      location: 'Remote',
      phone: '+****************',
      emergencyContact: 'Robert Johnson - Father',
      benefits: 'Premium Health, Vision, 401k',
      performance: 'Exceeds Expectations',
      lastReview: '2023-12-01'
    },
    {
      id: 4,
      name: 'David Kim',
      email: '<EMAIL>',
      department: 'Sales',
      position: 'Sales Representative',
      status: 'On Leave',
      startDate: '2022-11-05',
      salary: '$70,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-004',
      manager: 'Lisa Wang',
      location: 'Austin, TX',
      phone: '+****************',
      emergencyContact: 'Susan Kim - Mother',
      benefits: 'Basic Health, 401k',
      performance: 'Meets Expectations',
      lastReview: '2023-10-15'
    },
    {
      id: 5,
      name: 'Lisa Wang',
      email: '<EMAIL>',
      department: 'Finance',
      position: 'Financial Analyst',
      status: 'Active',
      startDate: '2023-02-28',
      salary: '$78,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-005',
      manager: 'David Kim',
      location: 'San Francisco, CA',
      phone: '+****************',
      emergencyContact: 'James Wang - Spouse',
      benefits: 'Premium Health, Dental, Vision, 401k',
      performance: 'Exceeds Expectations',
      lastReview: '2023-09-30'
    },
    {
      id: 6,
      name: 'Alex Thompson',
      email: '<EMAIL>',
      department: 'Engineering',
      position: 'DevOps Engineer',
      status: 'Active',
      startDate: '2023-04-12',
      salary: '$110,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-006',
      manager: 'Sarah Chen',
      location: 'Seattle, WA',
      phone: '+****************',
      emergencyContact: 'Taylor Thompson - Sibling',
      benefits: 'Premium Health, Dental, 401k',
      performance: 'Meets Expectations',
      lastReview: '2023-11-10'
    },
    {
      id: 7,
      name: 'Jessica Brown',
      email: '<EMAIL>',
      department: 'HR',
      position: 'HR Specialist',
      status: 'Active',
      startDate: '2023-05-18',
      salary: '$65,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-007',
      manager: 'Michael Rodriguez',
      location: 'Chicago, IL',
      phone: '+****************',
      emergencyContact: 'Mark Brown - Spouse',
      benefits: 'Basic Health, Dental, 401k',
      performance: 'Exceeds Expectations',
      lastReview: '2023-12-05'
    },
    {
      id: 8,
      name: 'Ryan Miller',
      email: '<EMAIL>',
      department: 'Sales',
      position: 'Senior Sales Manager',
      status: 'Active',
      startDate: '2022-11-30',
      salary: '$95,000',
      avatar: '/api/placeholder/40/40',
      employeeId: 'EMP-008',
      manager: 'Lisa Wang',
      location: 'Miami, FL',
      phone: '+****************',
      emergencyContact: 'Sarah Miller - Spouse',
      benefits: 'Premium Health, Dental, Vision, 401k',
      performance: 'Outstanding',
      lastReview: '2023-11-30'
    }
  ];

  const departments = ['all', 'Engineering', 'Marketing', 'Sales', 'Finance', 'HR'];

  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         employee.position.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = selectedDepartment === 'all' || employee.department === selectedDepartment;
    return matchesSearch && matchesDepartment;
  });

  const pendingRequests = [
    { id: 1, employee: 'Sarah Chen', type: 'Time Off', date: '2024-01-20', status: 'Pending' },
    { id: 2, employee: 'Michael Rodriguez', type: 'Expense', date: '2024-01-19', status: 'Pending' },
    { id: 3, employee: 'Emily Johnson', type: 'Equipment', date: '2024-01-18', status: 'Pending' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
            <div className="flex items-center">
              <Link href="/dashboard" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-navy">HR Management</h1>
                <p className="text-gray-600 text-sm">Manage employees, requests, and HR operations</p>
              </div>
            </div>
            <Link
              href="/dashboard/hr/add-employee"
              className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto inline-block text-center"
            >
              Add Employee
            </Link>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('employees')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'employees' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                Employees
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'requests' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                Requests
                <span className="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                  {pendingRequests.length}
                </span>
              </button>
              <button
                onClick={() => setActiveTab('onboarding')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'onboarding' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
                Onboarding
              </button>
              <button
                onClick={() => setActiveTab('performance')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'performance' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Performance
              </button>
              <button
                onClick={() => setActiveTab('analytics')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'analytics' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Analytics
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'employees' && (
            <div>
              {/* Filters */}
              <div className="bg-white p-4 lg:p-6 rounded-xl shadow-sm mb-6">
                <div className="flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4">
                  <div className="flex-1">
                    <input
                      type="text"
                      placeholder="Search employees..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                    />
                  </div>
                  <select
                    value={selectedDepartment}
                    onChange={(e) => setSelectedDepartment(e.target.value)}
                    className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"
                  >
                    {departments.map(dept => (
                      <option key={dept} value={dept}>
                        {dept === 'all' ? 'All Departments' : dept}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Employee List */}
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">
                    Employees ({filteredEmployees.length})
                  </h3>
                </div>
                
                {/* Mobile Cards */}
                <div className="lg:hidden">
                  {filteredEmployees.map((employee) => (
                    <div key={employee.id} className="p-4 border-b border-gray-200 last:border-b-0">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center">
                          <span className="text-purple font-medium">
                            {employee.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{employee.name}</h4>
                          <p className="text-sm text-gray-500">{employee.position}</p>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          employee.status === 'Active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-orange-100 text-orange-800'
                        }`}>
                          {employee.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>
                          <span className="text-gray-500">Department:</span>
                          <p className="font-medium">{employee.department}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Start Date:</span>
                          <p className="font-medium">{employee.startDate}</p>
                        </div>
                      </div>
                      <div className="mt-3 flex space-x-2">
                        <button className="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">
                          Edit
                        </button>
                        <button className="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">
                          View
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop Table */}
                <div className="hidden lg:block overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Employee
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Department
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Position
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Start Date
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredEmployees.map((employee) => (
                        <tr key={employee.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3">
                                <span className="text-purple font-medium">
                                  {employee.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                              <div>
                                <div className="text-sm font-medium text-gray-900">{employee.name}</div>
                                <div className="text-sm text-gray-500">{employee.email}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {employee.department}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {employee.position}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              employee.status === 'Active' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-orange-100 text-orange-800'
                            }`}>
                              {employee.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {employee.startDate}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button className="text-purple hover:text-darkpurple mr-3">Edit</button>
                            <button className="text-gray-400 hover:text-gray-600">View</button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'requests' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="px-4 lg:px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-navy">Pending Requests</h3>
                </div>
                <div className="divide-y divide-gray-200">
                  {pendingRequests.map((request) => (
                    <div key={request.id} className="p-4 lg:p-6">
                      <div className="flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0">
                        <div>
                          <h4 className="font-medium text-gray-900">{request.employee}</h4>
                          <p className="text-sm text-gray-500">{request.type} Request • {request.date}</p>
                        </div>
                        <div className="flex space-x-2">
                          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Approve
                          </button>
                          <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            Reject
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'onboarding' && (
            <div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-semibold text-navy mb-4">New Hires This Month</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">Emily Johnson</p>
                        <p className="text-sm text-gray-500">Frontend Developer</p>
                      </div>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        Completed
                      </span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">Alex Thompson</p>
                        <p className="text-sm text-gray-500">Product Manager</p>
                      </div>
                      <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">
                        In Progress
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-semibold text-navy mb-4">Onboarding Checklist</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input type="checkbox" checked className="mr-3" readOnly />
                      <span className="text-sm">Send welcome email</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" checked className="mr-3" readOnly />
                      <span className="text-sm">Create accounts</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" className="mr-3" readOnly />
                      <span className="text-sm">Schedule orientation</span>
                    </div>
                    <div className="flex items-center">
                      <input type="checkbox" className="mr-3" readOnly />
                      <span className="text-sm">Assign equipment</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div>
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-navy mb-6">Performance Reviews</h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600">94%</div>
                    <p className="text-gray-500">Reviews Completed</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">4.2</div>
                    <p className="text-gray-500">Average Rating</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple">12</div>
                    <p className="text-gray-500">Pending Reviews</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-semibold text-navy mb-4">Department Distribution</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span>Engineering</span>
                      <span className="font-medium">45%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Sales</span>
                      <span className="font-medium">25%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Marketing</span>
                      <span className="font-medium">20%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Finance</span>
                      <span className="font-medium">10%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-semibold text-navy mb-4">Turnover Rate</h3>
                  <div className="text-center">
                    <div className="text-4xl font-bold text-green-600 mb-2">2.1%</div>
                    <p className="text-gray-500">Annual turnover rate</p>
                    <p className="text-sm text-green-600 mt-2">↓ 0.5% from last year</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
