'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function EmployeeDetail() {
  const params = useParams();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock employee data - in real app, this would be fetched based on ID
  const employee = {
    id: params.id,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    employeeId: 'EMP-001',
    department: 'Engineering',
    position: 'Senior Software Engineer',
    manager: '<PERSON>',
    startDate: '2023-01-15',
    status: 'Active',
    location: 'San Francisco, CA',
    salary: '$120,000',
    avatar: '/api/placeholder/120/120',
    
    // Detailed Information
    personalInfo: {
      dateOfBirth: '1990-05-15',
      address: '123 Main Street, San Francisco, CA 94105',
      emergencyContact: {
        name: '<PERSON>',
        relationship: 'Spouse',
        phone: '+****************'
      }
    },
    
    employment: {
      employmentType: 'Full-time',
      workSchedule: 'Monday - Friday, 9:00 AM - 5:00 PM',
      timezone: 'Pacific Time (PT)',
      probationEndDate: '2023-04-15',
      nextReviewDate: '2024-01-15'
    },
    
    compensation: {
      baseSalary: 120000,
      bonus: 15000,
      equity: '0.25%',
      benefits: ['Health Insurance', 'Dental', 'Vision', '401k', 'Life Insurance']
    },
    
    performance: {
      currentRating: 'Exceeds Expectations',
      lastReviewDate: '2023-12-15',
      goals: [
        { title: 'Lead React Migration Project', status: 'In Progress', dueDate: '2024-03-31' },
        { title: 'Mentor 2 Junior Developers', status: 'Completed', dueDate: '2023-12-31' },
        { title: 'Complete AWS Certification', status: 'Not Started', dueDate: '2024-06-30' }
      ]
    },
    
    timeOff: {
      ptoBalance: 15.5,
      sickBalance: 8,
      personalBalance: 3,
      recentRequests: [
        { type: 'PTO', dates: 'Feb 15-22, 2024', days: 6, status: 'Approved' },
        { type: 'Sick', dates: 'Jan 10, 2024', days: 1, status: 'Approved' }
      ]
    },
    
    devices: [
      { type: 'Laptop', model: 'MacBook Pro 16"', serialNumber: 'ABC123456', assignedDate: '2023-01-15' },
      { type: 'Monitor', model: 'Dell 27" 4K', serialNumber: 'DEF789012', assignedDate: '2023-01-15' },
      { type: 'Phone', model: 'iPhone 15 Pro', serialNumber: 'GHI345678', assignedDate: '2023-09-20' }
    ],
    
    documents: [
      { name: 'Employment Contract', type: 'Contract', uploadDate: '2023-01-10', status: 'Signed' },
      { name: 'I-9 Form', type: 'Compliance', uploadDate: '2023-01-12', status: 'Complete' },
      { name: 'W-4 Form', type: 'Tax', uploadDate: '2023-01-12', status: 'Complete' },
      { name: 'Performance Review 2023', type: 'Review', uploadDate: '2023-12-15', status: 'Complete' }
    ]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/dashboard/hr" className="flex items-center mr-4">
                <svg className="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                </svg>
              </Link>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple/10 rounded-full flex items-center justify-center mr-4">
                  <span className="text-lg font-bold text-purple">SC</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-navy">{employee.name}</h1>
                  <p className="text-gray-600 text-sm">{employee.position} • {employee.department}</p>
                </div>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Edit Employee
              </button>
              <button className="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors">
                Actions
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="flex flex-col lg:flex-row">
        {/* Sidebar */}
        <aside className="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0">
          <nav className="p-4 lg:p-6">
            <div className="space-y-2">
              <button
                onClick={() => setActiveTab('overview')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'overview' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Overview
              </button>
              
              <button
                onClick={() => setActiveTab('employment')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'employment' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                </svg>
                Employment
              </button>
              
              <button
                onClick={() => setActiveTab('performance')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'performance' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Performance
              </button>
              
              <button
                onClick={() => setActiveTab('timeoff')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'timeoff' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                Time Off
              </button>
              
              <button
                onClick={() => setActiveTab('devices')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'devices' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
                Devices
              </button>
              
              <button
                onClick={() => setActiveTab('documents')}
                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${
                  activeTab === 'documents' 
                    ? 'bg-purple/10 text-purple border-r-2 border-purple' 
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
              >
                <svg className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Documents
              </button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-8">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Info Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Tenure</p>
                      <p className="text-xl font-bold text-navy">1.2 years</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Performance</p>
                      <p className="text-xl font-bold text-navy">Exceeds</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">PTO Balance</p>
                      <p className="text-xl font-bold text-navy">15.5 days</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mr-4">
                      <svg className="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Devices</p>
                      <p className="text-xl font-bold text-navy">{employee.devices.length}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Personal Information */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Personal Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Contact Information</h3>
                    <div className="space-y-2">
                      <p className="text-gray-900">{employee.email}</p>
                      <p className="text-gray-900">{employee.phone}</p>
                      <p className="text-gray-900">{employee.personalInfo.address}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Emergency Contact</h3>
                    <div className="space-y-2">
                      <p className="text-gray-900">{employee.personalInfo.emergencyContact.name}</p>
                      <p className="text-gray-600">{employee.personalInfo.emergencyContact.relationship}</p>
                      <p className="text-gray-900">{employee.personalInfo.emergencyContact.phone}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Employment Summary */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Employment Summary</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Position Details</h3>
                    <div className="space-y-2">
                      <p className="text-gray-900">{employee.position}</p>
                      <p className="text-gray-600">{employee.department}</p>
                      <p className="text-gray-600">Reports to: {employee.manager}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Employment</h3>
                    <div className="space-y-2">
                      <p className="text-gray-900">Start Date: {employee.startDate}</p>
                      <p className="text-gray-600">{employee.employment.employmentType}</p>
                      <p className="text-gray-600">{employee.location}</p>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Compensation</h3>
                    <div className="space-y-2">
                      <p className="text-gray-900">{employee.salary} annually</p>
                      <p className="text-gray-600">Bonus: ${employee.compensation.bonus.toLocaleString()}</p>
                      <p className="text-gray-600">Equity: {employee.compensation.equity}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'employment' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Employment Details</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Basic Information</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Employee ID:</span>
                        <span className="font-medium">{employee.employeeId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Employment Type:</span>
                        <span className="font-medium">{employee.employment.employmentType}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Status:</span>
                        <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                          {employee.status}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Start Date:</span>
                        <span className="font-medium">{employee.startDate}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Work Schedule</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Schedule:</span>
                        <span className="font-medium">{employee.employment.workSchedule}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Timezone:</span>
                        <span className="font-medium">{employee.employment.timezone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Location:</span>
                        <span className="font-medium">{employee.location}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Compensation & Benefits</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Compensation</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Base Salary:</span>
                        <span className="font-medium">${employee.compensation.baseSalary.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Annual Bonus:</span>
                        <span className="font-medium">${employee.compensation.bonus.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Equity:</span>
                        <span className="font-medium">{employee.compensation.equity}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 mb-4">Benefits</h3>
                    <div className="space-y-2">
                      {employee.compensation.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-center">
                          <svg className="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="text-gray-900">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'performance' && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Performance Overview</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple mb-2">{employee.performance.currentRating}</div>
                    <p className="text-gray-600">Current Rating</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">{employee.performance.lastReviewDate}</div>
                    <p className="text-gray-600">Last Review</p>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">{employee.employment.nextReviewDate}</div>
                    <p className="text-gray-600">Next Review</p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Goals & Objectives</h2>
                <div className="space-y-4">
                  {employee.performance.goals.map((goal, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-gray-900">{goal.title}</h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          goal.status === 'Completed' ? 'bg-green-100 text-green-800' :
                          goal.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {goal.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">Due: {goal.dueDate}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'timeoff' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">PTO Balance</h3>
                  <p className="text-3xl font-bold text-blue-600">{employee.timeOff.ptoBalance}</p>
                  <p className="text-sm text-gray-500">days available</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sick Leave</h3>
                  <p className="text-3xl font-bold text-green-600">{employee.timeOff.sickBalance}</p>
                  <p className="text-sm text-gray-500">days available</p>
                </div>
                <div className="bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Personal Days</h3>
                  <p className="text-3xl font-bold text-orange-600">{employee.timeOff.personalBalance}</p>
                  <p className="text-sm text-gray-500">days available</p>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-navy mb-6">Recent Requests</h2>
                <div className="space-y-4">
                  {employee.timeOff.recentRequests.map((request, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{request.type}</h3>
                        <p className="text-sm text-gray-600">{request.dates} ({request.days} days)</p>
                      </div>
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
                        {request.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'devices' && (
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Assigned Devices</h2>
              <div className="space-y-4">
                {employee.devices.map((device, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div>
                      <h3 className="font-medium text-gray-900">{device.type}</h3>
                      <p className="text-sm text-gray-600">{device.model}</p>
                      <p className="text-xs text-gray-500">Serial: {device.serialNumber}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-600">Assigned: {device.assignedDate}</p>
                      <button className="text-purple hover:text-darkpurple text-sm font-medium">
                        View Details
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'documents' && (
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-semibold text-navy mb-6">Employee Documents</h2>
              <div className="space-y-4">
                {employee.documents.map((doc, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      <svg className="h-8 w-8 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <div>
                        <h3 className="font-medium text-gray-900">{doc.name}</h3>
                        <p className="text-sm text-gray-600">{doc.type} • Uploaded: {doc.uploadDate}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        doc.status === 'Complete' || doc.status === 'Signed' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                      }`}>
                        {doc.status}
                      </span>
                      <button className="text-purple hover:text-darkpurple text-sm font-medium">
                        Download
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
}
