# Naboria - Heroku Deployment Guide

This guide will help you deploy the Naboria HR platform to Heroku.

## Prerequisites

- [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli) installed
- Git repository with the Naboria code
- Heroku account

## Quick Deployment

### 1. <PERSON>lone the Repository

```bash
git clone https://github.com/joelgriiyo/naboria.git
cd naboria
```

### 2. Login to Heroku

```bash
heroku login
```

### 3. Create a Heroku App

```bash
heroku create your-app-name
```

Replace `your-app-name` with your desired app name (must be unique).

### 4. Set Environment Variables (Optional)

```bash
heroku config:set NODE_ENV=production
heroku config:set NEXT_PUBLIC_APP_URL=https://your-app-name.herokuapp.com
```

### 5. Deploy to Heroku

```bash
git push heroku main
```

### 6. Open Your App

```bash
heroku open
```

## Configuration Files

The following files are already configured for Heroku deployment:

### Procfile
```
web: npm start
```

### package.json Scripts
```json
{
  "scripts": {
    "dev": "next dev --turbopack",
    "build": "next build",
    "start": "next start",
    "lint": "next lint"
  }
}
```

## Environment Variables

### Required Variables
- `NODE_ENV=production` (automatically set by Heroku)

### Optional Variables
- `NEXT_PUBLIC_APP_URL` - Your app's URL for absolute links
- `NEXT_PUBLIC_API_URL` - External API URL if using external services

## Build Process

Heroku will automatically:
1. Install dependencies with `npm install`
2. Build the application with `npm run build`
3. Start the application with `npm start`

## Troubleshooting

### Build Failures

If the build fails, check the logs:
```bash
heroku logs --tail
```

Common issues:
- **Memory issues**: Upgrade to a higher dyno type
- **Timeout**: The build process takes too long (increase build timeout)
- **Dependencies**: Missing or incompatible packages

### Runtime Issues

Check application logs:
```bash
heroku logs --tail --app your-app-name
```

### Performance Optimization

For production use, consider:
- Upgrading to a paid dyno (Hobby or Professional)
- Adding a CDN for static assets
- Implementing caching strategies

## Scaling

Scale your application:
```bash
# Scale to 2 web dynos
heroku ps:scale web=2

# Check current scaling
heroku ps
```

## Custom Domain

Add a custom domain:
```bash
heroku domains:add www.yourdomain.com
heroku domains:add yourdomain.com
```

## SSL Certificate

Heroku provides automatic SSL for `.herokuapp.com` domains. For custom domains:
```bash
heroku certs:auto:enable
```

## Database (If Needed)

If you need a database in the future:
```bash
# Add PostgreSQL
heroku addons:create heroku-postgresql:hobby-dev

# Add Redis
heroku addons:create heroku-redis:hobby-dev
```

## Monitoring

View app metrics:
```bash
heroku logs --tail
heroku ps
heroku releases
```

## Backup and Recovery

Create a backup:
```bash
# If using PostgreSQL
heroku pg:backups:capture
heroku pg:backups:download
```

## CI/CD Integration

For automatic deployments, connect your GitHub repository:
1. Go to your Heroku dashboard
2. Select your app
3. Go to the "Deploy" tab
4. Connect to GitHub
5. Enable automatic deploys from the main branch

## Security Considerations

1. **Environment Variables**: Store sensitive data in Heroku config vars
2. **HTTPS**: Always use HTTPS in production (enabled by default)
3. **Headers**: Consider adding security headers
4. **Rate Limiting**: Implement rate limiting for API endpoints

## Cost Optimization

- **Hobby Dyno**: $7/month, never sleeps
- **Free Dyno**: Sleeps after 30 minutes of inactivity (deprecated)
- **Professional Dyno**: $25-500/month, better performance

## Support

- [Heroku Documentation](https://devcenter.heroku.com/)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [GitHub Repository](https://github.com/joelgriiyo/naboria)

## Example Deployment Commands

Complete deployment from scratch:
```bash
# Clone and setup
git clone https://github.com/joelgriiyo/naboria.git
cd naboria

# Login to Heroku
heroku login

# Create app
heroku create naboria-hr-demo

# Set environment variables
heroku config:set NODE_ENV=production
heroku config:set NEXT_PUBLIC_APP_URL=https://naboria-hr-demo.herokuapp.com

# Deploy
git push heroku main

# Open app
heroku open
```

Your Naboria HR platform will be live and accessible at your Heroku app URL!

## Features Available After Deployment

- ✅ Complete HR Management System
- ✅ Payroll Processing & Tax Management
- ✅ IT Asset & Device Management
- ✅ Finance & Expense Tracking
- ✅ Workflow Automation
- ✅ Mobile-Responsive Design
- ✅ API Documentation
- ✅ Legal Compliance Pages
- ✅ Demo Login Functionality

**Demo Credentials:**
- Email: `<EMAIL>`
- Password: `demo123`
