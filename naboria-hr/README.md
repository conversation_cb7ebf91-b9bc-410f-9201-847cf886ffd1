# Naboria - HR, Payroll & IT Automation Platform

A next-generation HR, payroll, and IT automation platform built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Complete HR Management** - Employee onboarding, management, and automation
- **Smart Payroll Processing** - Multi-country payroll with compliance
- **IT Provisioning** - Automated device and software management
- **Finance Management** - Expense tracking and financial reporting
- **Interactive Dashboard** - Real-time analytics and insights
- **API-First Architecture** - Complete REST API with documentation
- **Enterprise Security** - SOC 2 compliance and bank-level security

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with Turbopack
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: Custom components with Radix UI
- **Icons**: Lucide React
- **Deployment**: Heroku-ready

## 📱 Pages & Features

### Core Pages
- **Landing Page** (`/`) - Complete marketing site with all sections
- **Login** (`/login`) - Authentication with demo credentials
- **Signup** (`/signup`) - User registration flow
- **Dashboard** (`/dashboard`) - Interactive admin dashboard
- **API Documentation** (`/api-docs`) - Complete developer docs

### Legal Pages
- **Privacy Policy** (`/privacy`) - GDPR-compliant privacy policy
- **Terms of Service** (`/terms`) - Comprehensive terms
- **Cookie Policy** (`/cookies`) - Cookie usage and management
- **GDPR Compliance** (`/gdpr`) - Data protection rights

## 🎯 Demo Credentials

Use these credentials to explore the dashboard:
- **Email**: `<EMAIL>`
- **Password**: `demo123`

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone https://github.com/joelgriiyo/naboria.git
cd naboria
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📦 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🌐 Deployment

### Heroku Deployment

This project is configured for Heroku deployment:

1. Create a new Heroku app:
```bash
heroku create your-app-name
```

2. Deploy:
```bash
git push heroku main
```

The `Procfile` is already configured to run the production server.

### Environment Variables

No environment variables are required for the basic functionality. For production, you may want to add:

- `NODE_ENV=production`
- `NEXT_PUBLIC_API_URL` (if using external APIs)

## 🎨 Design System

### Colors
- **Navy**: `#0A2342` - Primary brand color
- **Purple**: `#8B5CF6` - Secondary brand color
- **Teal**: `#2DD4BF` - Accent color
- **Dark Purple**: `#7C3AED` - Hover states

### Typography
- **Primary**: Inter (system font)
- **Headings**: Poppins (Google Fonts)

## 📊 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api-docs/          # API documentation page
│   ├── cookies/           # Cookie policy page
│   ├── dashboard/         # Dashboard page
│   ├── gdpr/              # GDPR compliance page
│   ├── login/             # Login page
│   ├── privacy/           # Privacy policy page
│   ├── signup/            # Signup page
│   ├── terms/             # Terms of service page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Landing page
├── components/            # Reusable components
│   ├── AboutSection.tsx
│   ├── ContactSection.tsx
│   ├── Footer.tsx
│   ├── IntegrationsSection.tsx
│   └── PricingSection.tsx
└── lib/                   # Utility functions
```

## 🔧 Configuration Files

- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `eslint.config.mjs` - ESLint configuration
- `tsconfig.json` - TypeScript configuration
- `Procfile` - Heroku deployment configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons by [Lucide](https://lucide.dev/)
- UI components inspired by [Radix UI](https://radix-ui.com/)

---

**Naboria** - The future of work starts here. 🚀
