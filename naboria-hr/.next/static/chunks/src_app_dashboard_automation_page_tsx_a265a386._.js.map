{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/automation/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function AutomationManagement() {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const workflows = [\n    {\n      id: 1,\n      name: 'Employee Onboarding',\n      description: 'Automated new hire setup and account creation',\n      status: 'Active',\n      triggers: 12,\n      lastRun: '2024-01-15 09:30',\n      success: 98.5\n    },\n    {\n      id: 2,\n      name: 'Expense Approval',\n      description: 'Auto-approve expenses under $500',\n      status: 'Active',\n      triggers: 45,\n      lastRun: '2024-01-15 14:22',\n      success: 100\n    },\n    {\n      id: 3,\n      name: 'IT Equipment Request',\n      description: 'Automated equipment provisioning workflow',\n      status: 'Active',\n      triggers: 8,\n      lastRun: '2024-01-14 16:45',\n      success: 95.2\n    },\n    {\n      id: 4,\n      name: 'Payroll Processing',\n      description: 'Monthly payroll calculation and distribution',\n      status: 'Scheduled',\n      triggers: 1,\n      lastRun: '2023-12-31 23:59',\n      success: 100\n    }\n  ];\n\n  const integrations = [\n    { name: 'Slack', status: 'Connected', type: 'Communication', lastSync: '2024-01-15 15:30' },\n    { name: 'Google Workspace', status: 'Connected', type: 'Productivity', lastSync: '2024-01-15 15:25' },\n    { name: 'Microsoft 365', status: 'Connected', type: 'Productivity', lastSync: '2024-01-15 15:20' },\n    { name: 'Salesforce', status: 'Connected', type: 'CRM', lastSync: '2024-01-15 15:15' },\n    { name: 'QuickBooks', status: 'Connected', type: 'Finance', lastSync: '2024-01-15 15:10' },\n    { name: 'Jira', status: 'Error', type: 'Project Management', lastSync: '2024-01-15 12:00' }\n  ];\n\n  const automationRules = [\n    {\n      id: 1,\n      name: 'Auto-assign IT equipment',\n      condition: 'New employee in Engineering department',\n      action: 'Create equipment request for laptop and monitor',\n      enabled: true\n    },\n    {\n      id: 2,\n      name: 'Expense auto-approval',\n      condition: 'Expense amount < $500 AND category = Office Supplies',\n      action: 'Automatically approve and process payment',\n      enabled: true\n    },\n    {\n      id: 3,\n      name: 'Time-off notification',\n      condition: 'Time-off request submitted',\n      action: 'Notify manager and update calendar',\n      enabled: true\n    },\n    {\n      id: 4,\n      name: 'Security alert',\n      condition: 'Failed login attempts > 5',\n      action: 'Lock account and notify IT security',\n      enabled: false\n    }\n  ];\n\n  const recentActivity = [\n    {\n      id: 1,\n      workflow: 'Employee Onboarding',\n      action: 'Created accounts for Emily Johnson',\n      timestamp: '2024-01-15 09:30',\n      status: 'Success'\n    },\n    {\n      id: 2,\n      workflow: 'Expense Approval',\n      action: 'Auto-approved $245 office supplies expense',\n      timestamp: '2024-01-15 14:22',\n      status: 'Success'\n    },\n    {\n      id: 3,\n      workflow: 'IT Equipment Request',\n      action: 'Provisioned MacBook for new hire',\n      timestamp: '2024-01-14 16:45',\n      status: 'Success'\n    },\n    {\n      id: 4,\n      workflow: 'Security Alert',\n      action: 'Detected suspicious login attempt',\n      timestamp: '2024-01-14 11:20',\n      status: 'Warning'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Automation Center</h1>\n                <p className=\"text-gray-600 text-sm\">Manage workflows, integrations, and automated processes</p>\n              </div>\n            </div>\n            <Link\n              href=\"/dashboard/automation/add-workflow\"\n              className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto text-center\"\n            >\n              Create Workflow\n            </Link>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              <button\n                onClick={() => setActiveTab('workflows')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'workflows' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n                Workflows\n                <span className=\"ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n                  {workflows.filter(w => w.status === 'Active').length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('integrations')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'integrations' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                </svg>\n                Integrations\n                <span className=\"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full\">\n                  {integrations.filter(i => i.status === 'Error').length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('rules')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'rules' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\" />\n                </svg>\n                Rules\n              </button>\n              <button\n                onClick={() => setActiveTab('activity')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'activity' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Activity Log\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Stats Cards */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Active Workflows</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">\n                        {workflows.filter(w => w.status === 'Active').length}\n                      </p>\n                      <p className=\"text-sm text-green-600\">All running smoothly</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Tasks Automated</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">1,247</p>\n                      <p className=\"text-sm text-blue-600\">This month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Time Saved</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">156h</p>\n                      <p className=\"text-sm text-purple\">This month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Success Rate</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">98.4%</p>\n                      <p className=\"text-sm text-green-600\">Excellent</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Quick Actions */}\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h3 className=\"text-lg font-semibold text-navy mb-4\">Quick Actions</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <div className=\"w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3\">\n                      <svg className=\"h-4 w-4 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Create Workflow</h4>\n                    <p className=\"text-sm text-gray-500\">Build new automation</p>\n                  </button>\n                  \n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <div className=\"w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3\">\n                      <svg className=\"h-4 w-4 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 9l3 3-3 3m5 0h3\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Add Integration</h4>\n                    <p className=\"text-sm text-gray-500\">Connect new service</p>\n                  </button>\n                  \n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <div className=\"w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3\">\n                      <svg className=\"h-4 w-4 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 mb-1\">Create Rule</h4>\n                    <p className=\"text-sm text-gray-500\">Set up automation rule</p>\n                  </button>\n                  \n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <div className=\"w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3\">\n                      <svg className=\"h-4 w-4 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                    </div>\n                    <h4 className=\"font-medium text-gray-900 mb-1\">View Reports</h4>\n                    <p className=\"text-sm text-gray-500\">Automation analytics</p>\n                  </button>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Recent Activity</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {recentActivity.slice(0, 5).map((activity) => (\n                    <div key={activity.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                          activity.status === 'Success' ? 'bg-green/10' :\n                          activity.status === 'Warning' ? 'bg-orange/10' : 'bg-red/10'\n                        }`}>\n                          <svg className={`h-4 w-4 ${\n                            activity.status === 'Success' ? 'text-green-500' :\n                            activity.status === 'Warning' ? 'text-orange-500' : 'text-red-500'\n                          }`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            {activity.status === 'Success' ? (\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                            ) : (\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            )}\n                          </svg>\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"text-sm font-medium text-gray-900\">{activity.action}</p>\n                          <p className=\"text-xs text-gray-500\">{activity.workflow} • {activity.timestamp}</p>\n                        </div>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          activity.status === 'Success' ? 'bg-green-100 text-green-800' :\n                          activity.status === 'Warning' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'\n                        }`}>\n                          {activity.status}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'workflows' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Automation Workflows</h3>\n                </div>\n                \n                {/* Mobile Cards */}\n                <div className=\"lg:hidden divide-y divide-gray-200\">\n                  {workflows.map((workflow) => (\n                    <div key={workflow.id} className=\"p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{workflow.name}</h4>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          workflow.status === 'Active' \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-orange-100 text-orange-800'\n                        }`}>\n                          {workflow.status}\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-500 mb-3\">{workflow.description}</p>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div>\n                          <span className=\"text-gray-500\">Triggers:</span>\n                          <p className=\"font-medium\">{workflow.triggers}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Success Rate:</span>\n                          <p className=\"font-medium\">{workflow.success}%</p>\n                        </div>\n                        <div className=\"col-span-2\">\n                          <span className=\"text-gray-500\">Last Run:</span>\n                          <p className=\"font-medium\">{workflow.lastRun}</p>\n                        </div>\n                      </div>\n                      <div className=\"mt-3 flex space-x-2\">\n                        <button className=\"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium\">\n                          Edit\n                        </button>\n                        <button className=\"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium\">\n                          View Logs\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Workflow\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Triggers\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Success Rate\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Last Run\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {workflows.map((workflow) => (\n                        <tr key={workflow.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div>\n                              <div className=\"text-sm font-medium text-gray-900\">{workflow.name}</div>\n                              <div className=\"text-sm text-gray-500\">{workflow.description}</div>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              workflow.status === 'Active' \n                                ? 'bg-green-100 text-green-800' \n                                : 'bg-orange-100 text-orange-800'\n                            }`}>\n                              {workflow.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {workflow.triggers}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {workflow.success}%\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {workflow.lastRun}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <button className=\"text-purple hover:text-darkpurple mr-3\">Edit</button>\n                            <button className=\"text-gray-400 hover:text-gray-600\">View Logs</button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'integrations' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">System Integrations</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {integrations.map((integration, index) => (\n                    <div key={index} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{integration.name}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {integration.type} • Last sync: {integration.lastSync}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            integration.status === 'Connected' \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-red-100 text-red-800'\n                          }`}>\n                            {integration.status}\n                          </span>\n                          <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                            {integration.status === 'Connected' ? 'Configure' : 'Reconnect'}\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'rules' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Automation Rules</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {automationRules.map((rule) => (\n                    <div key={rule.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-start justify-between space-y-3 lg:space-y-0\">\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center space-x-3 mb-2\">\n                            <h4 className=\"font-medium text-gray-900\">{rule.name}</h4>\n                            <label className=\"relative inline-flex items-center cursor-pointer\">\n                              <input \n                                type=\"checkbox\" \n                                checked={rule.enabled} \n                                className=\"sr-only peer\" \n                                readOnly\n                              />\n                              <div className=\"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple\"></div>\n                            </label>\n                          </div>\n                          <p className=\"text-sm text-gray-600 mb-1\">\n                            <strong>When:</strong> {rule.condition}\n                          </p>\n                          <p className=\"text-sm text-gray-600\">\n                            <strong>Then:</strong> {rule.action}\n                          </p>\n                        </div>\n                        <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                          Edit Rule\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'activity' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Activity Log</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {recentActivity.map((activity) => (\n                    <div key={activity.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${\n                          activity.status === 'Success' ? 'bg-green/10' :\n                          activity.status === 'Warning' ? 'bg-orange/10' : 'bg-red/10'\n                        }`}>\n                          <svg className={`h-4 w-4 ${\n                            activity.status === 'Success' ? 'text-green-500' :\n                            activity.status === 'Warning' ? 'text-orange-500' : 'text-red-500'\n                          }`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            {activity.status === 'Success' ? (\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                            ) : (\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                            )}\n                          </svg>\n                        </div>\n                        <div className=\"flex-1\">\n                          <p className=\"text-sm font-medium text-gray-900\">{activity.action}</p>\n                          <p className=\"text-xs text-gray-500\">{activity.workflow} • {activity.timestamp}</p>\n                        </div>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          activity.status === 'Success' ? 'bg-green-100 text-green-800' :\n                          activity.status === 'Warning' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'\n                        }`}>\n                          {activity.status}\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,QAAQ;YACR,UAAU;YACV,SAAS;YACT,SAAS;QACX;KACD;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAS,QAAQ;YAAa,MAAM;YAAiB,UAAU;QAAmB;QAC1F;YAAE,MAAM;YAAoB,QAAQ;YAAa,MAAM;YAAgB,UAAU;QAAmB;QACpG;YAAE,MAAM;YAAiB,QAAQ;YAAa,MAAM;YAAgB,UAAU;QAAmB;QACjG;YAAE,MAAM;YAAc,QAAQ;YAAa,MAAM;YAAO,UAAU;QAAmB;QACrF;YAAE,MAAM;YAAc,QAAQ;YAAa,MAAM;YAAW,UAAU;QAAmB;QACzF;YAAE,MAAM;YAAQ,QAAQ;YAAS,MAAM;YAAsB,UAAU;QAAmB;KAC3F;IAED,MAAM,kBAAkB;QACtB;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,SAAS;QACX;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;QACV;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,aACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,cACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,6LAAC;gDAAK,WAAU;0DACb,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;;;;;;;kDAGxD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,iBACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,6LAAC;gDAAK,WAAU;0DACb,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,SAAS,MAAM;;;;;;;;;;;;kDAG1D,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,UACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,aACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,6LAAC;wBAAK,WAAU;;4BACb,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EACV,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;8EAEtD,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC5E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EAAsB;;;;;;;;;;;;sEAErC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC1E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC9E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAsB,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC1E,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAwB,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC5E,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAAyB,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC7E,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAGvC,6LAAC;wDAAO,WAAU;;0EAChB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;oEAA0B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAC9E,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAGzE,6LAAC;gEAAG,WAAU;0EAAiC;;;;;;0EAC/C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;kDAM3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkC;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;0DACZ,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC/B,6LAAC;wDAAsB,WAAU;kEAC/B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,AAAC,yDAGhB,OAFC,SAAS,MAAM,KAAK,YAAY,gBAChC,SAAS,MAAM,KAAK,YAAY,iBAAiB;8EAEjD,cAAA,6LAAC;wEAAI,WAAW,AAAC,WAGhB,OAFC,SAAS,MAAM,KAAK,YAAY,mBAChC,SAAS,MAAM,KAAK,YAAY,oBAAoB;wEAClD,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFACxC,SAAS,MAAM,KAAK,0BACnB,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;iGAErE,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAY;4EAAI,GAAE;;;;;;;;;;;;;;;;8EAI3E,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAAqC,SAAS,MAAM;;;;;;sFACjE,6LAAC;4EAAE,WAAU;;gFAAyB,SAAS,QAAQ;gFAAC;gFAAI,SAAS,SAAS;;;;;;;;;;;;;8EAEhF,6LAAC;oEAAK,WAAW,AAAC,kCAGjB,OAFC,SAAS,MAAM,KAAK,YAAY,gCAChC,SAAS,MAAM,KAAK,YAAY,kCAAkC;8EAEjE,SAAS,MAAM;;;;;;;;;;;;uDAzBZ,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;4BAmC9B,cAAc,6BACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAIlD,6LAAC;4CAAI,WAAU;sDACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;oDAAsB,WAAU;;sEAC/B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,SAAS,IAAI;;;;;;8EACxD,6LAAC;oEAAK,WAAW,AAAC,kCAIjB,OAHC,SAAS,MAAM,KAAK,WAChB,gCACA;8EAEH,SAAS,MAAM;;;;;;;;;;;;sEAGpB,6LAAC;4DAAE,WAAU;sEAA8B,SAAS,WAAW;;;;;;sEAC/D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,SAAS,QAAQ;;;;;;;;;;;;8EAE/C,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;;gFAAe,SAAS,OAAO;gFAAC;;;;;;;;;;;;;8EAE/C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,SAAS,OAAO;;;;;;;;;;;;;;;;;;sEAGhD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAAwE;;;;;;8EAG1F,6LAAC;oEAAO,WAAU;8EAAyE;;;;;;;;;;;;;mDA9BrF,SAAS,EAAE;;;;;;;;;;sDAuCzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDAAM,WAAU;kEACf,cAAA,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;;;;;;;;;;;;kEAKnG,6LAAC;wDAAM,WAAU;kEACd,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC;gEAAqB,WAAU;;kFAC9B,6LAAC;wEAAG,WAAU;kFACZ,cAAA,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;8FAAqC,SAAS,IAAI;;;;;;8FACjE,6LAAC;oFAAI,WAAU;8FAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;kFAGhE,6LAAC;wEAAG,WAAU;kFACZ,cAAA,6LAAC;4EAAK,WAAW,AAAC,kCAIjB,OAHC,SAAS,MAAM,KAAK,WAChB,gCACA;sFAEH,SAAS,MAAM;;;;;;;;;;;kFAGpB,6LAAC;wEAAG,WAAU;kFACX,SAAS,QAAQ;;;;;;kFAEpB,6LAAC;wEAAG,WAAU;;4EACX,SAAS,OAAO;4EAAC;;;;;;;kFAEpB,6LAAC;wEAAG,WAAU;kFACX,SAAS,OAAO;;;;;;kFAEnB,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAO,WAAU;0FAAyC;;;;;;0FAC3D,6LAAC;gFAAO,WAAU;0FAAoC;;;;;;;;;;;;;+DA3BjD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsCjC,cAAc,gCACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;oDAAgB,WAAU;8DACzB,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA6B,YAAY,IAAI;;;;;;kFAC3D,6LAAC;wEAAE,WAAU;;4EACV,YAAY,IAAI;4EAAC;4EAAe,YAAY,QAAQ;;;;;;;;;;;;;0EAGzD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAW,AAAC,kCAIjB,OAHC,YAAY,MAAM,KAAK,cACnB,gCACA;kFAEH,YAAY,MAAM;;;;;;kFAErB,6LAAC;wEAAO,WAAU;kFACf,YAAY,MAAM,KAAK,cAAc,cAAc;;;;;;;;;;;;;;;;;;mDAjBlD;;;;;;;;;;;;;;;;;;;;;4BA4BnB,cAAc,yBACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;oDAAkB,WAAU;8DAC3B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAA6B,KAAK,IAAI;;;;;;0FACpD,6LAAC;gFAAM,WAAU;;kGACf,6LAAC;wFACC,MAAK;wFACL,SAAS,KAAK,OAAO;wFACrB,WAAU;wFACV,QAAQ;;;;;;kGAEV,6LAAC;wFAAI,WAAU;;;;;;;;;;;;;;;;;;kFAGnB,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;0FAAO;;;;;;4EAAc;4EAAE,KAAK,SAAS;;;;;;;kFAExC,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;0FAAO;;;;;;4EAAc;4EAAE,KAAK,MAAM;;;;;;;;;;;;;0EAGvC,6LAAC;gEAAO,WAAU;0EAAwD;;;;;;;;;;;;mDAtBpE,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;4BAiC1B,cAAc,4BACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,yBACnB,6LAAC;oDAAsB,WAAU;8DAC/B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAW,AAAC,yDAGhB,OAFC,SAAS,MAAM,KAAK,YAAY,gBAChC,SAAS,MAAM,KAAK,YAAY,iBAAiB;0EAEjD,cAAA,6LAAC;oEAAI,WAAW,AAAC,WAGhB,OAFC,SAAS,MAAM,KAAK,YAAY,mBAChC,SAAS,MAAM,KAAK,YAAY,oBAAoB;oEAClD,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EACxC,SAAS,MAAM,KAAK,0BACnB,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;6FAErE,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;0EAI3E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAqC,SAAS,MAAM;;;;;;kFACjE,6LAAC;wEAAE,WAAU;;4EAAyB,SAAS,QAAQ;4EAAC;4EAAI,SAAS,SAAS;;;;;;;;;;;;;0EAEhF,6LAAC;gEAAK,WAAW,AAAC,kCAGjB,OAFC,SAAS,MAAM,KAAK,YAAY,gCAChC,SAAS,MAAM,KAAK,YAAY,kCAAkC;0EAEjE,SAAS,MAAM;;;;;;;;;;;;mDAzBZ,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCzC;GArmBwB;KAAA", "debugId": null}}]}