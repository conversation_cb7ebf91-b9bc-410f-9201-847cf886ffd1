{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function Home() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('startups');\n\n  return (\n    <div className=\"bg-white\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-10 w-10 text-purple\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n              <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n            </svg>\n            <span className=\"ml-2 text-2xl font-bold text-navy font-poppins\">Naboria</span>\n          </div>\n\n          <div className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"nav-link active-nav text-navy font-medium hover:text-purple transition-colors\">Home</a>\n            <a href=\"#features\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Features</a>\n            <a href=\"#solutions\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Solutions</a>\n            <a href=\"#integrations\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Integrations</a>\n            <a href=\"#pricing\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Pricing</a>\n            <a href=\"#about\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">About</a>\n            <a href=\"#contact\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Contact</a>\n          </div>\n\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <a href=\"#\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</a>\n            <a href=\"#\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg\">Get Started</a>\n          </div>\n\n          <button\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            className=\"md:hidden text-navy\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden bg-white shadow-lg absolute w-full\">\n            <div className=\"container mx-auto px-4 py-3 flex flex-col space-y-3\">\n              <a href=\"#home\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Home</a>\n              <a href=\"#features\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Features</a>\n              <a href=\"#solutions\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Solutions</a>\n              <a href=\"#integrations\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Integrations</a>\n              <a href=\"#pricing\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Pricing</a>\n              <a href=\"#about\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">About</a>\n              <a href=\"#contact\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Contact</a>\n              <div className=\"flex flex-col space-y-3 pt-3 border-t border-gray-200\">\n                <a href=\"#\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</a>\n                <a href=\"#\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md\">Get Started</a>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Home Section */}\n      <section id=\"home\" className=\"pt-28 pb-20 md:pt-32 md:pb-24\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row items-center\">\n            <div className=\"md:w-1/2 mb-10 md:mb-0\">\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins\">\n                The Future of Work <span className=\"gradient-text\">Starts Here</span>\n              </h1>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-lg\">\n                Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.\n              </p>\n              <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                <a href=\"#\" className=\"gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1\">\n                  Request Demo\n                </a>\n                <a href=\"#\" className=\"border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors\">\n                  Learn More\n                </a>\n              </div>\n              <div className=\"mt-10 flex items-center\">\n                <div className=\"flex -space-x-2\">\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <div className=\"w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium\">+5</div>\n                </div>\n                <p className=\"ml-4 text-gray-600\">Trusted by 1000+ companies worldwide</p>\n              </div>\n            </div>\n            <div className=\"md:w-1/2\">\n              <div className=\"relative\">\n                <div className=\"absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full\"></div>\n                <div className=\"absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full\"></div>\n                <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100\">\n                  <div className=\"bg-navy p-4 flex justify-between items-center\">\n                    <div className=\"flex space-x-2\">\n                      <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                    </div>\n                    <div className=\"text-white text-sm\">Naboria Dashboard</div>\n                    <div></div>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex justify-between items-center mb-6\">\n                      <div>\n                        <h3 className=\"text-navy font-semibold\">Welcome back, Sarah</h3>\n                        <p className=\"text-gray-500 text-sm\">Monday, June 12</p>\n                      </div>\n                      <div className=\"bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium\">\n                        Admin\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-purple text-xl font-bold\">24</div>\n                        <div className=\"text-gray-500 text-sm\">New Hires</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-teal text-xl font-bold\">98%</div>\n                        <div className=\"text-gray-500 text-sm\">Onboarding</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-navy text-xl font-bold\">12</div>\n                        <div className=\"text-gray-500 text-sm\">Approvals</div>\n                      </div>\n                    </div>\n                    <div className=\"mb-6\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <h4 className=\"font-medium text-navy\">Onboarding Progress</h4>\n                        <span className=\"text-sm text-purple\">View All</span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Emily Johnson</span>\n                            <span>75%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-purple h-2 rounded-full\" style={{width: '75%'}}></div>\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Michael Chen</span>\n                            <span>90%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-teal h-2 rounded-full\" style={{width: '90%'}}></div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <button className=\"text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                        New Task\n                      </button>\n                      <button className=\"text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                        Run Payroll\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAwB,SAAQ;wCAAY,MAAK;wCAAO,OAAM;;0DAC3E,6LAAC;gDAAK,GAAE;gDAAsC,MAAK;gDAAe,aAAY;;;;;;0DAC9E,6LAAC;gDAAK,GAAE;gDAA8C,MAAK;;;;;;;;;;;;kDAE7D,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAgF;;;;;;kDAC1G,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAqE;;;;;;kDACnG,6LAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAqE;;;;;;kDACpG,6LAAC;wCAAE,MAAK;wCAAgB,WAAU;kDAAqE;;;;;;kDACvG,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;kDAClG,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqE;;;;;;kDAChG,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;;;;;;;0CAGpG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA4D;;;;;;kDAClF,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;0CAGlJ,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACjG,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAM1E,gCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAiE;;;;;;8CAC3F,6LAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAiE;;;;;;8CAC/F,6LAAC;oCAAE,MAAK;oCAAa,WAAU;8CAAiE;;;;;;8CAChG,6LAAC;oCAAE,MAAK;oCAAgB,WAAU;8CAAiE;;;;;;8CACnG,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAiE;;;;;;8CAC5F,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA4D;;;;;;sDAClF,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAsH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtJ,6LAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAuF;0DAChF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAErD,6LAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAgJ;;;;;;0DAGtK,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAuI;;;;;;;;;;;;kDAI/J,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,WAAU;kEAAkH;;;;;;;;;;;;0DAEnI,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAGtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,6LAAC;;;;;;;;;;;8DAEH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAI,WAAU;8EAAsE;;;;;;;;;;;;sEAIvF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAgC;;;;;;sFAC/C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAwB;;;;;;sFACtC,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;sGAAK;;;;;;sGACN,6LAAC;sGAAK;;;;;;;;;;;;8FAER,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;wFAA6B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;sFAGpE,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;sGAAK;;;;;;sGACN,6LAAC;sGAAK;;;;;;;;;;;;8FAER,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;wFAA2B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAKtE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;8EAGR,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9B;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1130, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}]}