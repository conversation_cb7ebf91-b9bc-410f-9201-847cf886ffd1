{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/components/IntegrationsSection.tsx"], "sourcesContent": ["export default function IntegrationsSection() {\n  return (\n    <section id=\"integrations\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">Seamless Integrations</h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">Connect with your favorite tools and platforms. Our extensive integration library ensures <PERSON><PERSON>ia works with your existing tech stack.</p>\n        </div>\n        \n        <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 mb-16\">\n          {/* Integration Logos */}\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%234285F4'%3EGoogle%3C/text%3E%3C/svg%3E\" alt=\"Google Workspace\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='15' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23611f69'%3ESlack%3C/text%3E%3C/svg%3E\" alt=\"Slack\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23FF6900'%3EZoom%3C/text%3E%3C/svg%3E\" alt=\"Zoom\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='5' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%230078D4'%3EMicrosoft%3C/text%3E%3C/svg%3E\" alt=\"Microsoft 365\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='5' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23FF5722'%3EQuickBooks%3C/text%3E%3C/svg%3E\" alt=\"QuickBooks\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23000000'%3EGitHub%3C/text%3E%3C/svg%3E\" alt=\"GitHub\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='15' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23E60023'%3ENotion%3C/text%3E%3C/svg%3E\" alt=\"Notion\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23FF6B35'%3EAsana%3C/text%3E%3C/svg%3E\" alt=\"Asana\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23026AA7'%3ETrello%3C/text%3E%3C/svg%3E\" alt=\"Trello\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='5' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23FF4B4B'%3EDocuSign%3C/text%3E%3C/svg%3E\" alt=\"DocuSign\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='10' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23635BFF'%3EStripe%3C/text%3E%3C/svg%3E\" alt=\"Stripe\" className=\"h-8\" />\n          </div>\n          <div className=\"flex items-center justify-center p-6 bg-white rounded-xl shadow-sm integration-logo\">\n            <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='40' viewBox='0 0 80 40'%3E%3Ctext x='15' y='25' font-family='Arial, sans-serif' font-size='14' font-weight='bold' fill='%23FF6900'%3EAWS%3C/text%3E%3C/svg%3E\" alt=\"AWS\" className=\"h-8\" />\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Integration Categories */}\n          <div className=\"bg-white p-8 rounded-xl shadow-lg\">\n            <div className=\"w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-6\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-navy mb-4\">Productivity Tools</h3>\n            <p className=\"text-gray-600 mb-6\">Connect with the tools your team already uses for seamless workflow integration.</p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Google Workspace & Microsoft 365\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Slack, Teams & Communication\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Project Management Tools\n              </li>\n            </ul>\n          </div>\n          \n          <div className=\"bg-white p-8 rounded-xl shadow-lg\">\n            <div className=\"w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-6\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-navy mb-4\">Financial Systems</h3>\n            <p className=\"text-gray-600 mb-6\">Sync with your existing financial and accounting platforms for unified reporting.</p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                QuickBooks & Xero\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Stripe & Payment Processors\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Banking & Expense Management\n              </li>\n            </ul>\n          </div>\n          \n          <div className=\"bg-white p-8 rounded-xl shadow-lg\">\n            <div className=\"w-14 h-14 bg-navy/10 rounded-lg flex items-center justify-center mb-6\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-xl font-semibold text-navy mb-4\">Security & Identity</h3>\n            <p className=\"text-gray-600 mb-6\">Integrate with your security infrastructure for seamless access management.</p>\n            <ul className=\"space-y-3\">\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-navy mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                SSO & SAML Providers\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-navy mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Active Directory & LDAP\n              </li>\n              <li className=\"flex items-center text-gray-600\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-navy mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Multi-Factor Authentication\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"text-center mt-16\">\n          <h3 className=\"text-2xl font-semibold text-navy mb-4\">Need a Custom Integration?</h3>\n          <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto\">Our API-first architecture makes it easy to build custom integrations. Work with our team to connect any system.</p>\n          <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4\">\n            <a href=\"#\" className=\"bg-purple hover:bg-darkpurple text-white px-8 py-3 rounded-lg font-medium transition-colors\">\n              View API Docs\n            </a>\n            <a href=\"#\" className=\"border-2 border-purple text-purple px-8 py-3 rounded-lg font-medium hover:bg-purple hover:text-white transition-colors\">\n              Request Integration\n            </a>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAC3E,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAGjD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAmP,KAAI;gCAAmB,WAAU;;;;;;;;;;;sCAE/R,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAkP,KAAI;gCAAQ,WAAU;;;;;;;;;;;sCAEnR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAiP,KAAI;gCAAO,WAAU;;;;;;;;;;;sCAEjR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAqP,KAAI;gCAAgB,WAAU;;;;;;;;;;;sCAE9R,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAsP,KAAI;gCAAa,WAAU;;;;;;;;;;;sCAE5R,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAmP,KAAI;gCAAS,WAAU;;;;;;;;;;;sCAErR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAmP,KAAI;gCAAS,WAAU;;;;;;;;;;;sCAErR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAkP,KAAI;gCAAQ,WAAU;;;;;;;;;;;sCAEnR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAmP,KAAI;gCAAS,WAAU;;;;;;;;;;;sCAErR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAoP,KAAI;gCAAW,WAAU;;;;;;;;;;;sCAExR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAmP,KAAI;gCAAS,WAAU;;;;;;;;;;;sCAErR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,KAAI;gCAAgP,KAAI;gCAAM,WAAU;;;;;;;;;;;;;;;;;8BAIjR,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAsB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC7G,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAoB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC3G,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAoB,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC3G,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;sDAGR,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAyB,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAChH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;;;;;;;8BAOd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCACpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAA8F;;;;;;8CAGpH,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAyH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3J;KA7JwB", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/components/PricingSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function PricingSection() {\n  const [billingCycle, setBillingCycle] = useState('monthly');\n\n  return (\n    <section id=\"pricing\" className=\"py-20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">Simple, Transparent Pricing</h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto mb-8\">Choose the plan that fits your organization. All plans include our core features with no hidden fees.</p>\n          \n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center mb-8\">\n            <span className={`mr-3 ${billingCycle === 'monthly' ? 'text-navy font-medium' : 'text-gray-500'}`}>Monthly</span>\n            <button\n              onClick={() => setBillingCycle(billingCycle === 'monthly' ? 'annual' : 'monthly')}\n              className=\"relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-purple focus:ring-offset-2\"\n            >\n              <span\n                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                  billingCycle === 'annual' ? 'translate-x-6' : 'translate-x-1'\n                }`}\n              />\n            </button>\n            <span className={`ml-3 ${billingCycle === 'annual' ? 'text-navy font-medium' : 'text-gray-500'}`}>\n              Annual \n              <span className=\"ml-1 text-sm bg-purple/10 text-purple px-2 py-1 rounded-full\">Save 20%</span>\n            </span>\n          </div>\n        </div>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {/* Starter Plan */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-lg pricing-card transition-all duration-300 border border-gray-100\">\n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl font-bold text-navy mb-2\">Starter</h3>\n              <p className=\"text-gray-600 mb-6\">Perfect for small teams getting started</p>\n              <div className=\"mb-6\">\n                <span className=\"text-4xl font-bold text-navy\">\n                  ${billingCycle === 'monthly' ? '29' : '23'}\n                </span>\n                <span className=\"text-gray-500 ml-2\">per employee/month</span>\n              </div>\n              <a href=\"#\" className=\"w-full bg-purple hover:bg-darkpurple text-white py-3 px-6 rounded-lg font-medium transition-colors block text-center\">\n                Start Free Trial\n              </a>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Up to 50 employees</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Core HR & Payroll</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Basic onboarding workflows</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Email support</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">5 integrations included</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Standard reporting</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Professional Plan */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-xl pricing-card transition-all duration-300 border-2 border-purple relative\">\n            <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n              <span className=\"bg-purple text-white px-4 py-2 rounded-full text-sm font-medium\">Most Popular</span>\n            </div>\n            \n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl font-bold text-navy mb-2\">Professional</h3>\n              <p className=\"text-gray-600 mb-6\">For growing companies that need more power</p>\n              <div className=\"mb-6\">\n                <span className=\"text-4xl font-bold text-navy\">\n                  ${billingCycle === 'monthly' ? '59' : '47'}\n                </span>\n                <span className=\"text-gray-500 ml-2\">per employee/month</span>\n              </div>\n              <a href=\"#\" className=\"w-full gradient-bg text-white py-3 px-6 rounded-lg font-medium transition-all transform hover:-translate-y-1 hover:shadow-lg block text-center\">\n                Start Free Trial\n              </a>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Up to 500 employees</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Full HR, Payroll & IT suite</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Advanced automation workflows</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Priority support & chat</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Unlimited integrations</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Advanced analytics & reporting</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Multi-country payroll</span>\n              </div>\n            </div>\n          </div>\n          \n          {/* Enterprise Plan */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-lg pricing-card transition-all duration-300 border border-gray-100\">\n            <div className=\"text-center mb-8\">\n              <h3 className=\"text-2xl font-bold text-navy mb-2\">Enterprise</h3>\n              <p className=\"text-gray-600 mb-6\">For large organizations with complex needs</p>\n              <div className=\"mb-6\">\n                <span className=\"text-4xl font-bold text-navy\">Custom</span>\n                <span className=\"text-gray-500 ml-2 block text-sm\">Contact for pricing</span>\n              </div>\n              <a href=\"#\" className=\"w-full border-2 border-navy text-navy hover:bg-navy hover:text-white py-3 px-6 rounded-lg font-medium transition-colors block text-center\">\n                Contact Sales\n              </a>\n            </div>\n            \n            <div className=\"space-y-4\">\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Unlimited employees</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">White-label options</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">Custom integrations & API</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">24/7 dedicated support</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">SOC 2 Type II compliance</span>\n              </div>\n              <div className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                <span className=\"text-gray-700\">On-premise deployment</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"text-center mt-16\">\n          <h3 className=\"text-2xl font-semibold text-navy mb-4\">All plans include</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 max-w-4xl mx-auto\">\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mb-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n              </div>\n              <span className=\"text-gray-700 font-medium\">99.9% Uptime SLA</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-teal/10 rounded-lg flex items-center justify-center mb-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\" />\n                </svg>\n              </div>\n              <span className=\"text-gray-700 font-medium\">Bank-level Security</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-navy/10 rounded-lg flex items-center justify-center mb-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z\" />\n                </svg>\n              </div>\n              <span className=\"text-gray-700 font-medium\">Free Migration</span>\n            </div>\n            <div className=\"flex flex-col items-center\">\n              <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mb-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <span className=\"text-gray-700 font-medium\">30-day Free Trial</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAC3E,6LAAC;4BAAE,WAAU;sCAAuC;;;;;;sCAGpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAW,AAAC,QAA8E,OAAvE,iBAAiB,YAAY,0BAA0B;8CAAmB;;;;;;8CACnG,6LAAC;oCACC,SAAS,IAAM,gBAAgB,iBAAiB,YAAY,WAAW;oCACvE,WAAU;8CAEV,cAAA,6LAAC;wCACC,WAAW,AAAC,6EAEX,OADC,iBAAiB,WAAW,kBAAkB;;;;;;;;;;;8CAIpD,6LAAC;oCAAK,WAAW,AAAC,QAA6E,OAAtE,iBAAiB,WAAW,0BAA0B;;wCAAmB;sDAEhG,6LAAC;4CAAK,WAAU;sDAA+D;;;;;;;;;;;;;;;;;;;;;;;;8BAKrF,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAA+B;wDAC3C,iBAAiB,YAAY,OAAO;;;;;;;8DAExC,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAuH;;;;;;;;;;;;8CAK/I,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAAkE;;;;;;;;;;;8CAGpF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;wDAA+B;wDAC3C,iBAAiB,YAAY,OAAO;;;;;;;8DAExC,6LAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAiJ;;;;;;;;;;;;8CAKzK,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;sCAMtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;8DAC/C,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;;sDAErD,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA4I;;;;;;;;;;;;8CAKpK,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAA2B,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAClH,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1D;GAxPwB;KAAA", "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/components/AboutSection.tsx"], "sourcesContent": ["export default function AboutSection() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">About Naboria</h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">We're building the future of work by making HR, payroll, and IT operations seamless, automated, and developer-friendly.</p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\">\n          <div>\n            <h3 className=\"text-2xl font-bold text-navy mb-6\">Our Mission</h3>\n            <p className=\"text-gray-600 mb-6 leading-relaxed\">\n              At Naboria, we believe that managing people operations shouldn't be a burden. Our mission is to eliminate the complexity and manual work that holds organizations back from focusing on what matters most: their people and their growth.\n            </p>\n            <p className=\"text-gray-600 mb-8 leading-relaxed\">\n              We're building a platform that's more modular than Workday, more automated than Rippling, and more developer-friendly than anything else on the market. Our goal is to make HR, payroll, and IT operations so seamless that they become invisible.\n            </p>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                  </svg>\n                </div>\n                <h4 className=\"font-semibold text-navy mb-2\">Innovation First</h4>\n                <p className=\"text-gray-600 text-sm\">We leverage cutting-edge technology to solve age-old problems in people operations.</p>\n              </div>\n              \n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <div className=\"w-12 h-12 bg-teal/10 rounded-lg flex items-center justify-center mb-4\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                  </svg>\n                </div>\n                <h4 className=\"font-semibold text-navy mb-2\">People Focused</h4>\n                <p className=\"text-gray-600 text-sm\">Every feature we build is designed to improve the employee experience and reduce administrative burden.</p>\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"relative\">\n            <div className=\"absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full\"></div>\n            <div className=\"absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full\"></div>\n            <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden\">\n              <div className=\"p-8\">\n                <div className=\"grid grid-cols-2 gap-6 mb-8\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple mb-2\">1000+</div>\n                    <div className=\"text-gray-600 text-sm\">Companies Trust Us</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-teal mb-2\">50K+</div>\n                    <div className=\"text-gray-600 text-sm\">Employees Managed</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-navy mb-2\">40+</div>\n                    <div className=\"text-gray-600 text-sm\">Countries Supported</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple mb-2\">99.9%</div>\n                    <div className=\"text-gray-600 text-sm\">Uptime SLA</div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-gray-50 p-6 rounded-xl\">\n                  <h4 className=\"font-semibold text-navy mb-3\">Founded in 2023</h4>\n                  <p className=\"text-gray-600 text-sm mb-4\">\n                    Started by a team of engineers and HR professionals who experienced firsthand the pain of managing people operations at scale.\n                  </p>\n                  <div className=\"flex items-center\">\n                    <div className=\"flex -space-x-2 mr-4\">\n                      <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%238B5CF6'/%3E%3Cpath d='M16 10C14.3431 10 13 11.3431 13 13C13 14.6569 14.3431 16 16 16C17.6569 16 19 14.6569 19 13C19 11.3431 17.6569 10 16 10ZM12 21C12 18.7909 13.7909 17 16 17C18.2091 17 20 18.7909 20 21V22H12V21Z' fill='white'/%3E%3C/svg%3E\" alt=\"Team\" className=\"w-8 h-8 rounded-full border-2 border-white\" />\n                      <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%232DD4BF'/%3E%3Cpath d='M16 10C14.3431 10 13 11.3431 13 13C13 14.6569 14.3431 16 16 16C17.6569 16 19 14.6569 19 13C19 11.3431 17.6569 10 16 10ZM12 21C12 18.7909 13.7909 17 16 17C18.2091 17 20 18.7909 20 21V22H12V21Z' fill='white'/%3E%3C/svg%3E\" alt=\"Team\" className=\"w-8 h-8 rounded-full border-2 border-white\" />\n                      <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%230A2342'/%3E%3Cpath d='M16 10C14.3431 10 13 11.3431 13 13C13 14.6569 14.3431 16 16 16C17.6569 16 19 14.6569 19 13C19 11.3431 17.6569 10 16 10ZM12 21C12 18.7909 13.7909 17 16 17C18.2091 17 20 18.7909 20 21V22H12V21Z' fill='white'/%3E%3C/svg%3E\" alt=\"Team\" className=\"w-8 h-8 rounded-full border-2 border-white\" />\n                    </div>\n                    <span className=\"text-sm text-gray-600\">Remote-first team of 25+ experts</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Team Section */}\n        <div className=\"mb-20\">\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl font-bold text-navy mb-4\">Meet Our Leadership</h3>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">Our team combines deep expertise in HR technology, enterprise software, and developer tools.</p>\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <div className=\"bg-white p-6 rounded-xl shadow-lg text-center\">\n              <div className=\"w-20 h-20 bg-purple/20 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-purple font-bold text-xl\">AJ</span>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-2\">Alex Johnson</h4>\n              <p className=\"text-purple font-medium mb-3\">CEO & Co-founder</p>\n              <p className=\"text-gray-600 text-sm mb-4\">Former VP of Engineering at Workday. 15+ years building enterprise HR software.</p>\n              <div className=\"flex justify-center space-x-3\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-purple transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-purple transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-lg text-center\">\n              <div className=\"w-20 h-20 bg-teal/20 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-teal font-bold text-xl\">SC</span>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-2\">Sarah Chen</h4>\n              <p className=\"text-teal font-medium mb-3\">CTO & Co-founder</p>\n              <p className=\"text-gray-600 text-sm mb-4\">Former Principal Engineer at Stripe. Expert in building scalable financial systems.</p>\n              <div className=\"flex justify-center space-x-3\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-teal transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-teal transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-lg text-center\">\n              <div className=\"w-20 h-20 bg-navy/20 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-navy font-bold text-xl\">MR</span>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-2\">Marcus Rodriguez</h4>\n              <p className=\"text-navy font-medium mb-3\">VP of Product</p>\n              <p className=\"text-gray-600 text-sm mb-4\">Former Product Lead at Rippling. 10+ years designing HR and payroll experiences.</p>\n              <div className=\"flex justify-center space-x-3\">\n                <a href=\"#\" className=\"text-gray-400 hover:text-navy transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"text-gray-400 hover:text-navy transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n        \n        {/* Values Section */}\n        <div className=\"text-center\">\n          <h3 className=\"text-2xl font-bold text-navy mb-12\">Our Values</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n            <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n              <div className=\"w-16 h-16 bg-purple/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-3\">Move Fast</h4>\n              <p className=\"text-gray-600 text-sm\">We ship quickly and iterate based on real user feedback.</p>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n              <div className=\"w-16 h-16 bg-teal/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-3\">Quality First</h4>\n              <p className=\"text-gray-600 text-sm\">We never compromise on security, reliability, or user experience.</p>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n              <div className=\"w-16 h-16 bg-navy/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-3\">People First</h4>\n              <p className=\"text-gray-600 text-sm\">Every decision we make prioritizes the human experience.</p>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n              <div className=\"w-16 h-16 bg-purple/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n              </div>\n              <h4 className=\"font-semibold text-navy mb-3\">Transparency</h4>\n              <p className=\"text-gray-600 text-sm\">We believe in open communication and honest feedback.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAC3E,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAGjD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAIlD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAsB,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEAC7G,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAGvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAoB,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEAC3G,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAA+B;;;;;;8DAC7C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsC;;;;;;0EACrD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAoC;;;;;;0EACnD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAsC;;;;;;0EACrD,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAI3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAG1C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,KAAI;wEAAwY,KAAI;wEAAO,WAAU;;;;;;kFACta,6LAAC;wEAAI,KAAI;wEAAwY,KAAI;wEAAO,WAAU;;;;;;kFACta,6LAAC;wEAAI,KAAI;wEAAwY,KAAI;wEAAO,WAAU;;;;;;;;;;;;0EAExa,6LAAC;gEAAK,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAGjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;sDAElD,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA+B;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;sDAEhD,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;sDAEhD,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;KA9MwB", "debugId": null}}, {"offset": {"line": 3147, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/components/ContactSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    company: '',\n    employees: '',\n    message: ''\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle form submission\n    console.log('Form submitted:', formData);\n    // Reset form\n    setFormData({\n      name: '',\n      email: '',\n      company: '',\n      employees: '',\n      message: ''\n    });\n    alert('Thank you for your message! We\\'ll get back to you soon.');\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-16\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">Get in Touch</h2>\n          <p className=\"text-gray-600 max-w-2xl mx-auto\">Ready to transform your HR operations? Let's talk about how Naboria can help your organization.</p>\n        </div>\n        \n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 max-w-6xl mx-auto\">\n          {/* Contact Form */}\n          <div className=\"bg-white p-8 rounded-2xl shadow-lg\">\n            <h3 className=\"text-2xl font-semibold text-navy mb-6\">Send us a message</h3>\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Full Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    required\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent transition-colors\"\n                    placeholder=\"John Doe\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    required\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label htmlFor=\"company\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Company Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"company\"\n                    name=\"company\"\n                    required\n                    value={formData.company}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent transition-colors\"\n                    placeholder=\"Acme Corp\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"employees\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    Company Size\n                  </label>\n                  <select\n                    id=\"employees\"\n                    name=\"employees\"\n                    value={formData.employees}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent transition-colors\"\n                  >\n                    <option value=\"\">Select size</option>\n                    <option value=\"1-10\">1-10 employees</option>\n                    <option value=\"11-50\">11-50 employees</option>\n                    <option value=\"51-200\">51-200 employees</option>\n                    <option value=\"201-500\">201-500 employees</option>\n                    <option value=\"500+\">500+ employees</option>\n                  </select>\n                </div>\n              </div>\n              \n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Message *\n                </label>\n                <textarea\n                  id=\"message\"\n                  name=\"message\"\n                  required\n                  rows={5}\n                  value={formData.message}\n                  onChange={handleChange}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent transition-colors resize-none\"\n                  placeholder=\"Tell us about your needs and how we can help...\"\n                ></textarea>\n              </div>\n              \n              <button\n                type=\"submit\"\n                className=\"w-full gradient-bg text-white py-3 px-6 rounded-lg font-medium transition-all transform hover:-translate-y-1 hover:shadow-lg\"\n              >\n                Send Message\n              </button>\n            </form>\n          </div>\n          \n          {/* Contact Information */}\n          <div className=\"space-y-8\">\n            <div className=\"bg-gray-50 p-8 rounded-2xl\">\n              <h3 className=\"text-2xl font-semibold text-navy mb-6\">Let's start a conversation</h3>\n              <p className=\"text-gray-600 mb-8\">\n                We're here to help you streamline your HR operations. Whether you have questions about our platform, need a custom demo, or want to discuss your specific requirements, our team is ready to assist.\n              </p>\n              \n              <div className=\"space-y-6\">\n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-1\">Email Us</h4>\n                    <p className=\"text-gray-600\"><EMAIL></p>\n                    <p className=\"text-gray-500 text-sm\">We typically respond within 24 hours</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-teal/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-1\">Live Chat</h4>\n                    <p className=\"text-gray-600\">Available 9 AM - 6 PM PST</p>\n                    <p className=\"text-gray-500 text-sm\">Get instant answers to your questions</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex items-start\">\n                  <div className=\"w-12 h-12 bg-navy/10 rounded-lg flex items-center justify-center mr-4 flex-shrink-0\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    </svg>\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-1\">Office</h4>\n                    <p className=\"text-gray-600\">San Francisco, CA</p>\n                    <p className=\"text-gray-500 text-sm\">Remote-first company</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"bg-gradient-to-br from-purple to-teal p-8 rounded-2xl text-white\">\n              <h3 className=\"text-xl font-semibold mb-4\">Ready to get started?</h3>\n              <p className=\"mb-6 opacity-90\">\n                Book a personalized demo and see how Naboria can transform your HR operations in just 30 minutes.\n              </p>\n              <div className=\"space-y-3\">\n                <a href=\"#\" className=\"block w-full bg-white text-purple py-3 px-6 rounded-lg font-medium text-center hover:bg-gray-100 transition-colors\">\n                  Schedule Demo\n                </a>\n                <a href=\"#\" className=\"block w-full border-2 border-white text-white py-3 px-6 rounded-lg font-medium text-center hover:bg-white hover:text-purple transition-colors\">\n                  Start Free Trial\n                </a>\n              </div>\n            </div>\n            \n            <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n              <h4 className=\"font-semibold text-navy mb-3\">Follow Us</h4>\n              <div className=\"flex space-x-4\">\n                <a href=\"#\" className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-purple hover:text-white transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-purple hover:text-white transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-purple hover:text-white transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-gray-600 hover:bg-purple hover:text-white transition-colors\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,yBAAyB;QACzB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,aAAa;QACb,YAAY;YACV,MAAM;YACN,OAAO;YACP,SAAS;YACT,WAAW;YACX,SAAS;QACX;QACA,MAAM;IACR;IAEA,MAAM,eAAe,CAAC;QACpB,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAC3E,6LAAC;4BAAE,WAAU;sCAAkC;;;;;;;;;;;;8BAGjD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,6LAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA+C;;;;;;sEAG/E,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAQ,WAAU;sEAA+C;;;;;;sEAGhF,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA+C;;;;;;sEAGlF,6LAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAGhB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAA+C;;;;;;sEAGpF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,SAAS;4DACzB,UAAU;4DACV,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,6LAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,6LAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,6LAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,6LAAC;oEAAO,OAAM;8EAAO;;;;;;;;;;;;;;;;;;;;;;;;sDAK3B,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAU,WAAU;8DAA+C;;;;;;8DAGlF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,MAAM;oDACN,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,6LAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7G,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B;;;;;;8EAC7C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAoB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC3G,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B;;;;;;8EAC7C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAoB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;;kFAC3G,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;kFACrE,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;;;;;;;sEAGzE,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B;;;;;;8EAC7C,6LAAC;oEAAE,WAAU;8EAAgB;;;;;;8EAC7B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAM7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAkB;;;;;;sDAG/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAqH;;;;;;8DAG3I,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAgJ;;;;;;;;;;;;;;;;;;8CAM1K,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DACpB,cAAA,6LAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACtF,cAAA,6LAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU5B;GA3OwB;KAAA", "debugId": null}}, {"offset": {"line": 3946, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-navy text-white\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\">\n          {/* Company Info */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center mb-6\">\n              <svg className=\"h-10 w-10 text-purple\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n                <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n              </svg>\n              <span className=\"ml-2 text-2xl font-bold font-poppins\">Naboria</span>\n            </div>\n            <p className=\"text-gray-300 mb-6 max-w-md\">\n              The next-generation HR, payroll, and IT automation platform that outperforms Workday and Rippling by being more modular, automated, and developer-friendly.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:bg-purple hover:text-white transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:bg-purple hover:text-white transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:bg-purple hover:text-white transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/10 rounded-lg flex items-center justify-center text-gray-300 hover:bg-purple hover:text-white transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n          \n          {/* Product */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Product</h3>\n            <ul className=\"space-y-3\">\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Features</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Integrations</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">API Documentation</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Security</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Compliance</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Roadmap</a></li>\n            </ul>\n          </div>\n          \n          {/* Solutions */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Solutions</h3>\n            <ul className=\"space-y-3\">\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">For Startups</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">For Enterprises</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Global Teams</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Remote Work</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">HR Automation</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Payroll Management</a></li>\n            </ul>\n          </div>\n          \n          {/* Company */}\n          <div>\n            <h3 className=\"text-lg font-semibold mb-6\">Company</h3>\n            <ul className=\"space-y-3\">\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">About Us</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Careers</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Blog</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Press</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Partners</a></li>\n              <li><a href=\"#\" className=\"text-gray-300 hover:text-white transition-colors\">Contact</a></li>\n            </ul>\n          </div>\n        </div>\n        \n        {/* Newsletter Signup */}\n        <div className=\"border-t border-gray-700 mt-12 pt-12\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            <div>\n              <h3 className=\"text-xl font-semibold mb-2\">Stay updated</h3>\n              <p className=\"text-gray-300\">Get the latest news, updates, and insights from Naboria.</p>\n            </div>\n            <div className=\"flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-3 bg-white/10 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple focus:border-transparent transition-colors\"\n              />\n              <button className=\"bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors whitespace-nowrap\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n        \n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-700 mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-300 text-sm\">\n              © 2024 Naboria. All rights reserved.\n            </div>\n            <div className=\"flex flex-wrap justify-center md:justify-end space-x-6 text-sm\">\n              <Link href=\"/privacy\" className=\"text-gray-300 hover:text-white transition-colors\">Privacy Policy</Link>\n              <Link href=\"/terms\" className=\"text-gray-300 hover:text-white transition-colors\">Terms of Service</Link>\n              <Link href=\"/cookies\" className=\"text-gray-300 hover:text-white transition-colors\">Cookie Policy</Link>\n              <Link href=\"/gdpr\" className=\"text-gray-300 hover:text-white transition-colors\">GDPR</Link>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Back to Top Button */}\n      <div className=\"fixed bottom-8 right-8\">\n        <button\n          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n          className=\"w-12 h-12 bg-purple hover:bg-darkpurple text-white rounded-full shadow-lg flex items-center justify-center transition-all transform hover:-translate-y-1 hover:shadow-xl\"\n        >\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n          </svg>\n        </button>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;gDAAwB,SAAQ;gDAAY,MAAK;gDAAO,OAAM;;kEAC3E,6LAAC;wDAAK,GAAE;wDAAsC,MAAK;wDAAe,aAAY;;;;;;kEAC9E,6LAAC;wDAAK,GAAE;wDAA8C,MAAK;;;;;;;;;;;;0DAE7D,6LAAC;gDAAK,WAAU;0DAAuC;;;;;;;;;;;;kDAEzD,6LAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAG3C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACtF,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACtF,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACtF,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACtF,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;;;;;;;;;;;;;0CAKjF,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;;;;;;;;;;;;;0CAKjF,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;0DAC7E,6LAAC;0DAAG,cAAA,6LAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CAAO,WAAU;sDAAgH;;;;;;;;;;;;;;;;;;;;;;;kCAQxI,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmD;;;;;;sDACnF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAmD;;;;;;sDACjF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmD;;;;;;sDACnF,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;sDAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,SAAS,IAAM,OAAO,QAAQ,CAAC;4BAAE,KAAK;4BAAG,UAAU;wBAAS;oBAC5D,WAAU;8BAEV,cAAA,6LAAC;wBAAI,OAAM;wBAA6B,WAAU;wBAAU,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACjG,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAY;4BAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;KAlIwB", "debugId": null}}, {"offset": {"line": 4685, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport IntegrationsSection from '@/components/IntegrationsSection';\nimport PricingSection from '@/components/PricingSection';\nimport AboutSection from '@/components/AboutSection';\nimport ContactSection from '@/components/ContactSection';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('startups');\n\n  return (\n    <div className=\"bg-white\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-10 w-10 text-purple\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n              <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n            </svg>\n            <span className=\"ml-2 text-2xl font-bold text-navy font-poppins\">Naboria</span>\n          </div>\n\n          <div className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"nav-link active-nav text-navy font-medium hover:text-purple transition-colors\">Home</a>\n            <a href=\"#features\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Features</a>\n            <a href=\"#solutions\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Solutions</a>\n            <a href=\"#integrations\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Integrations</a>\n            <a href=\"#pricing\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Pricing</a>\n            <a href=\"#about\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">About</a>\n            <a href=\"#contact\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Contact</a>\n          </div>\n\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link href=\"/login\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</Link>\n            <Link href=\"/signup\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg\">Get Started</Link>\n          </div>\n\n          <button\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            className=\"md:hidden text-navy\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden bg-white shadow-lg absolute w-full\">\n            <div className=\"container mx-auto px-4 py-3 flex flex-col space-y-3\">\n              <a href=\"#home\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Home</a>\n              <a href=\"#features\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Features</a>\n              <a href=\"#solutions\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Solutions</a>\n              <a href=\"#integrations\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Integrations</a>\n              <a href=\"#pricing\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Pricing</a>\n              <a href=\"#about\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">About</a>\n              <a href=\"#contact\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Contact</a>\n              <div className=\"flex flex-col space-y-3 pt-3 border-t border-gray-200\">\n                <Link href=\"/login\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</Link>\n                <Link href=\"/signup\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md\">Get Started</Link>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Home Section */}\n      <section id=\"home\" className=\"pt-28 pb-20 md:pt-32 md:pb-24\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row items-center\">\n            <div className=\"md:w-1/2 mb-10 md:mb-0\">\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins\">\n                The Future of Work <span className=\"gradient-text\">Starts Here</span>\n              </h1>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-lg\">\n                Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.\n              </p>\n              <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                <Link href=\"/signup\" className=\"gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1\">\n                  Start Free Trial\n                </Link>\n                <Link href=\"/dashboard\" className=\"border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors\">\n                  View Demo\n                </Link>\n              </div>\n              <div className=\"mt-10 flex items-center\">\n                <div className=\"flex -space-x-2\">\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <div className=\"w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium\">+5</div>\n                </div>\n                <p className=\"ml-4 text-gray-600\">Trusted by 1000+ companies worldwide</p>\n              </div>\n            </div>\n            <div className=\"md:w-1/2\">\n              <div className=\"relative\">\n                <div className=\"absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full\"></div>\n                <div className=\"absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full\"></div>\n                <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100\">\n                  <div className=\"bg-navy p-4 flex justify-between items-center\">\n                    <div className=\"flex space-x-2\">\n                      <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                    </div>\n                    <div className=\"text-white text-sm\">Naboria Dashboard</div>\n                    <div></div>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex justify-between items-center mb-6\">\n                      <div>\n                        <h3 className=\"text-navy font-semibold\">Welcome back, Sarah</h3>\n                        <p className=\"text-gray-500 text-sm\">Monday, June 12</p>\n                      </div>\n                      <div className=\"bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium\">\n                        Admin\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-purple text-xl font-bold\">24</div>\n                        <div className=\"text-gray-500 text-sm\">New Hires</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-teal text-xl font-bold\">98%</div>\n                        <div className=\"text-gray-500 text-sm\">Onboarding</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-navy text-xl font-bold\">12</div>\n                        <div className=\"text-gray-500 text-sm\">Approvals</div>\n                      </div>\n                    </div>\n                    <div className=\"mb-6\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <h4 className=\"font-medium text-navy\">Onboarding Progress</h4>\n                        <span className=\"text-sm text-purple\">View All</span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Emily Johnson</span>\n                            <span>75%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-purple h-2 rounded-full\" style={{width: '75%'}}></div>\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Michael Chen</span>\n                            <span>90%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-teal h-2 rounded-full\" style={{width: '90%'}}></div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <button className=\"text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                        New Task\n                      </button>\n                      <button className=\"text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                        Run Payroll\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section id=\"features\" className=\"py-20 bg-gray-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">Powerful Features</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">Our comprehensive platform streamlines your HR, payroll, and IT operations with powerful automation and intuitive workflows.</p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Feature 1 */}\n            <div className=\"bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300\">\n              <div className=\"w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-3\">Seamless Onboarding</h3>\n              <p className=\"text-gray-600 mb-4\">Automate employee onboarding with customizable workflows, digital paperwork, and integrated background checks.</p>\n              <ul className=\"space-y-2 mb-5\">\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Digital document signing\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Automated task assignment\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Progress tracking dashboard\n                </li>\n              </ul>\n              <a href=\"#\" className=\"text-purple font-medium flex items-center hover:text-darkpurple transition-colors\">\n                Learn more\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </a>\n            </div>\n\n            {/* Feature 2 */}\n            <div className=\"bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300\">\n              <div className=\"w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-3\">Smart Payroll</h3>\n              <p className=\"text-gray-600 mb-4\">Process payroll with precision and compliance across multiple countries, currencies, and tax jurisdictions.</p>\n              <ul className=\"space-y-2 mb-5\">\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Automatic tax calculations\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Multi-currency support\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Compliance monitoring\n                </li>\n              </ul>\n              <a href=\"#\" className=\"text-teal font-medium flex items-center hover:text-darkpurple transition-colors\">\n                Learn more\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </a>\n            </div>\n\n            {/* Feature 3 */}\n            <div className=\"bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300\">\n              <div className=\"w-14 h-14 bg-purple/10 rounded-lg flex items-center justify-center mb-5\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-3\">IT Provisioning</h3>\n              <p className=\"text-gray-600 mb-4\">Automate device setup, software licensing, and access management for new and existing employees.</p>\n              <ul className=\"space-y-2 mb-5\">\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  One-click app provisioning\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Device management\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Security compliance\n                </li>\n              </ul>\n              <a href=\"#\" className=\"text-purple font-medium flex items-center hover:text-darkpurple transition-colors\">\n                Learn more\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </a>\n            </div>\n\n            {/* Feature 4 */}\n            <div className=\"bg-white p-6 rounded-xl shadow-lg feature-card transition-all duration-300\">\n              <div className=\"w-14 h-14 bg-teal/10 rounded-lg flex items-center justify-center mb-5\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-3\">Finance Management</h3>\n              <p className=\"text-gray-600 mb-4\">Streamline expense tracking, budgeting, and financial reporting with powerful automation tools.</p>\n              <ul className=\"space-y-2 mb-5\">\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Expense approval workflows\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Budget tracking\n                </li>\n                <li className=\"flex items-center text-sm text-gray-600\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                  </svg>\n                  Financial reporting\n                </li>\n              </ul>\n              <a href=\"#\" className=\"text-teal font-medium flex items-center hover:text-darkpurple transition-colors\">\n                Learn more\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Solutions Section */}\n      <section id=\"solutions\" className=\"py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-navy mb-4 font-poppins\">Tailored Solutions</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">Our platform adapts to your organization's unique needs, whether you're a startup or a global enterprise.</p>\n          </div>\n\n          <div className=\"flex flex-col md:flex-row space-y-6 md:space-y-0 md:space-x-6\">\n            <div className=\"w-full md:w-1/4\">\n              <div className=\"bg-navy text-white p-6 rounded-xl h-full\">\n                <h3 className=\"text-xl font-semibold mb-4\">Choose Your Solution</h3>\n                <p className=\"text-gray-300 mb-6\">Select the solution that best fits your organization's size and needs.</p>\n\n                <div className=\"space-y-3\">\n                  <button\n                    onClick={() => setActiveTab('startups')}\n                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${\n                      activeTab === 'startups'\n                        ? 'bg-purple/20 border-l-4 border-purple'\n                        : 'hover:bg-white/10'\n                    }`}\n                  >\n                    Startups\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('enterprises')}\n                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${\n                      activeTab === 'enterprises'\n                        ? 'bg-purple/20 border-l-4 border-purple'\n                        : 'hover:bg-white/10'\n                    }`}\n                  >\n                    Enterprises\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('global')}\n                    className={`w-full text-left py-3 px-4 rounded-lg transition-colors font-medium ${\n                      activeTab === 'global'\n                        ? 'bg-purple/20 border-l-4 border-purple'\n                        : 'hover:bg-white/10'\n                    }`}\n                  >\n                    Global Teams\n                  </button>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"w-full md:w-3/4\">\n              {/* Startups Tab */}\n              {activeTab === 'startups' && (\n                <div className=\"tab-content active bg-white p-8 rounded-xl shadow-lg h-full\">\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 mb-6 md:mb-0 md:pr-8\">\n                      <span className=\"text-sm font-medium text-purple bg-purple/10 px-3 py-1 rounded-full\">For Startups</span>\n                      <h3 className=\"text-2xl font-bold text-navy mt-4 mb-4\">Scale your team without scaling overhead</h3>\n                      <p className=\"text-gray-600 mb-6\">Perfect for growing teams that need to establish efficient HR and IT processes without dedicated departments.</p>\n\n                      <ul className=\"space-y-4 mb-8\">\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Quick Implementation</h4>\n                            <p className=\"text-gray-600 text-sm\">Get up and running in days, not months</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Scalable Pricing</h4>\n                            <p className=\"text-gray-600 text-sm\">Pay only for what you need as you grow</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Essential Integrations</h4>\n                            <p className=\"text-gray-600 text-sm\">Connect with the tools startups love</p>\n                          </div>\n                        </li>\n                      </ul>\n\n                      <a href=\"#\" className=\"inline-block bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n                        Learn More\n                      </a>\n                    </div>\n                    <div className=\"md:w-1/2\">\n                      <div className=\"bg-gray-50 rounded-xl p-6 h-full\">\n                        <div className=\"flex justify-between items-center mb-6\">\n                          <h4 className=\"font-semibold text-navy\">Startup Success Story</h4>\n                          <div className=\"text-purple\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                            </svg>\n                          </div>\n                        </div>\n\n                        <div className=\"flex items-center mb-4\">\n                          <div className=\"w-12 h-12 bg-teal/20 rounded-full flex items-center justify-center mr-4\">\n                            <span className=\"text-teal font-bold\">TL</span>\n                          </div>\n                          <div>\n                            <h5 className=\"font-medium\">TechLaunch</h5>\n                            <p className=\"text-sm text-gray-500\">SaaS Startup, 45 employees</p>\n                          </div>\n                        </div>\n\n                        <blockquote className=\"text-gray-600 italic mb-6\">\n                          \"Naboria helped us scale from 10 to 45 employees in just 8 months without hiring a dedicated HR team. The automated onboarding and IT provisioning saved us countless hours.\"\n                        </blockquote>\n\n                        <div className=\"flex items-center\">\n                          <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-8 h-8 rounded-full\" />\n                          <div className=\"ml-3\">\n                            <h6 className=\"font-medium text-sm\">Sarah Chen</h6>\n                            <p className=\"text-xs text-gray-500\">Co-founder & COO</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Enterprises Tab */}\n              {activeTab === 'enterprises' && (\n                <div className=\"tab-content active bg-white p-8 rounded-xl shadow-lg h-full\">\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 mb-6 md:mb-0 md:pr-8\">\n                      <span className=\"text-sm font-medium text-teal bg-teal/10 px-3 py-1 rounded-full\">For Enterprises</span>\n                      <h3 className=\"text-2xl font-bold text-navy mt-4 mb-4\">Enterprise-grade security and compliance</h3>\n                      <p className=\"text-gray-600 mb-6\">Comprehensive solutions for large organizations with complex requirements and strict compliance needs.</p>\n\n                      <ul className=\"space-y-4 mb-8\">\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">SOC 2 Type II Compliance</h4>\n                            <p className=\"text-gray-600 text-sm\">Enterprise-grade security and audit trails</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Advanced Analytics</h4>\n                            <p className=\"text-gray-600 text-sm\">Deep insights and custom reporting</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-teal mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Dedicated Support</h4>\n                            <p className=\"text-gray-600 text-sm\">24/7 support with dedicated CSM</p>\n                          </div>\n                        </li>\n                      </ul>\n\n                      <a href=\"#\" className=\"inline-block bg-teal hover:bg-teal/80 text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n                        Learn More\n                      </a>\n                    </div>\n                    <div className=\"md:w-1/2\">\n                      <div className=\"bg-gray-50 rounded-xl p-6 h-full\">\n                        <div className=\"flex justify-between items-center mb-6\">\n                          <h4 className=\"font-semibold text-navy\">Enterprise Metrics</h4>\n                          <div className=\"text-teal\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                            </svg>\n                          </div>\n                        </div>\n\n                        <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                          <div className=\"bg-white p-4 rounded-lg\">\n                            <div className=\"text-2xl font-bold text-teal\">99.9%</div>\n                            <div className=\"text-sm text-gray-500\">Uptime SLA</div>\n                          </div>\n                          <div className=\"bg-white p-4 rounded-lg\">\n                            <div className=\"text-2xl font-bold text-purple\">50K+</div>\n                            <div className=\"text-sm text-gray-500\">Employees</div>\n                          </div>\n                          <div className=\"bg-white p-4 rounded-lg\">\n                            <div className=\"text-2xl font-bold text-navy\">85%</div>\n                            <div className=\"text-sm text-gray-500\">Time Saved</div>\n                          </div>\n                          <div className=\"bg-white p-4 rounded-lg\">\n                            <div className=\"text-2xl font-bold text-teal\">24/7</div>\n                            <div className=\"text-sm text-gray-500\">Support</div>\n                          </div>\n                        </div>\n\n                        <div className=\"bg-white p-4 rounded-lg\">\n                          <h5 className=\"font-medium mb-2\">Fortune 500 Client</h5>\n                          <p className=\"text-sm text-gray-600 mb-3\">\"Naboria transformed our global HR operations, reducing onboarding time by 75% across 40+ countries.\"</p>\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 bg-navy/20 rounded-full flex items-center justify-center mr-3\">\n                              <span className=\"text-navy font-bold text-sm\">GC</span>\n                            </div>\n                            <div>\n                              <h6 className=\"font-medium text-sm\">Global Corp</h6>\n                              <p className=\"text-xs text-gray-500\">50,000+ employees</p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Global Teams Tab */}\n              {activeTab === 'global' && (\n                <div className=\"tab-content active bg-white p-8 rounded-xl shadow-lg h-full\">\n                  <div className=\"flex flex-col md:flex-row\">\n                    <div className=\"md:w-1/2 mb-6 md:mb-0 md:pr-8\">\n                      <span className=\"text-sm font-medium text-navy bg-navy/10 px-3 py-1 rounded-full\">For Global Teams</span>\n                      <h3 className=\"text-2xl font-bold text-navy mt-4 mb-4\">Seamless global operations</h3>\n                      <p className=\"text-gray-600 mb-6\">Manage distributed teams across multiple countries with localized compliance and unified workflows.</p>\n\n                      <ul className=\"space-y-4 mb-8\">\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-navy mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Multi-Country Payroll</h4>\n                            <p className=\"text-gray-600 text-sm\">Local compliance in 40+ countries</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-navy mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Currency Management</h4>\n                            <p className=\"text-gray-600 text-sm\">Real-time exchange rates and hedging</p>\n                          </div>\n                        </li>\n                        <li className=\"flex items-start\">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-navy mr-3 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                          </svg>\n                          <div>\n                            <h4 className=\"font-medium text-navy\">Time Zone Coordination</h4>\n                            <p className=\"text-gray-600 text-sm\">Automated scheduling and workflows</p>\n                          </div>\n                        </li>\n                      </ul>\n\n                      <a href=\"#\" className=\"inline-block bg-navy hover:bg-navy/80 text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n                        Learn More\n                      </a>\n                    </div>\n                    <div className=\"md:w-1/2\">\n                      <div className=\"bg-gray-50 rounded-xl p-6 h-full\">\n                        <div className=\"flex justify-between items-center mb-6\">\n                          <h4 className=\"font-semibold text-navy\">Global Coverage</h4>\n                          <div className=\"text-navy\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM4.332 8.027a6.012 6.012 0 011.912-2.706C6.512 5.73 6.974 6 7.5 6A1.5 1.5 0 019 7.5V8a2 2 0 004 0 2 2 0 011.523-1.943A5.977 5.977 0 0116 10c0 .34-.028.675-.083 1H15a2 2 0 00-2 2v2.197A5.973 5.973 0 0110 16v-2a2 2 0 00-2-2 2 2 0 01-2-2 2 2 0 00-1.668-1.973z\" clipRule=\"evenodd\" />\n                            </svg>\n                          </div>\n                        </div>\n\n                        <div className=\"mb-6\">\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <span className=\"text-sm font-medium\">Countries Supported</span>\n                            <span className=\"text-sm text-navy font-bold\">40+</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-navy h-2 rounded-full\" style={{width: '85%'}}></div>\n                          </div>\n                        </div>\n\n                        <div className=\"grid grid-cols-2 gap-3 mb-6\">\n                          <div className=\"bg-white p-3 rounded-lg text-center\">\n                            <div className=\"text-lg font-bold text-purple\">🇺🇸</div>\n                            <div className=\"text-xs text-gray-500\">North America</div>\n                          </div>\n                          <div className=\"bg-white p-3 rounded-lg text-center\">\n                            <div className=\"text-lg font-bold text-teal\">🇪🇺</div>\n                            <div className=\"text-xs text-gray-500\">Europe</div>\n                          </div>\n                          <div className=\"bg-white p-3 rounded-lg text-center\">\n                            <div className=\"text-lg font-bold text-navy\">🇦🇺</div>\n                            <div className=\"text-xs text-gray-500\">Asia Pacific</div>\n                          </div>\n                          <div className=\"bg-white p-3 rounded-lg text-center\">\n                            <div className=\"text-lg font-bold text-purple\">🌍</div>\n                            <div className=\"text-xs text-gray-500\">Global</div>\n                          </div>\n                        </div>\n\n                        <div className=\"bg-white p-4 rounded-lg\">\n                          <h5 className=\"font-medium mb-2\">Remote-First Company</h5>\n                          <p className=\"text-sm text-gray-600 mb-3\">\"Managing our 200+ remote employees across 25 countries is now effortless with Naboria's global platform.\"</p>\n                          <div className=\"flex items-center\">\n                            <div className=\"w-8 h-8 bg-purple/20 rounded-full flex items-center justify-center mr-3\">\n                              <span className=\"text-purple font-bold text-sm\">RF</span>\n                            </div>\n                            <div>\n                              <h6 className=\"font-medium text-sm\">RemoteFirst Inc</h6>\n                              <p className=\"text-xs text-gray-500\">25 countries, 200+ employees</p>\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Additional Sections */}\n      <IntegrationsSection />\n      <PricingSection />\n      <AboutSection />\n      <ContactSection />\n      <Footer />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAwB,SAAQ;wCAAY,MAAK;wCAAO,OAAM;;0DAC3E,6LAAC;gDAAK,GAAE;gDAAsC,MAAK;gDAAe,aAAY;;;;;;0DAC9E,6LAAC;gDAAK,GAAE;gDAA8C,MAAK;;;;;;;;;;;;kDAE7D,6LAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAgF;;;;;;kDAC1G,6LAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAqE;;;;;;kDACnG,6LAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAqE;;;;;;kDACpG,6LAAC;wCAAE,MAAK;wCAAgB,WAAU;kDAAqE;;;;;;kDACvG,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;kDAClG,6LAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqE;;;;;;kDAChG,6LAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;;;;;;;0CAGpG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA4D;;;;;;kDAC1F,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAA0H;;;;;;;;;;;;0CAG3J,6LAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACjG,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAM1E,gCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAiE;;;;;;8CAC3F,6LAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAiE;;;;;;8CAC/F,6LAAC;oCAAE,MAAK;oCAAa,WAAU;8CAAiE;;;;;;8CAChG,6LAAC;oCAAE,MAAK;oCAAgB,WAAU;8CAAiE;;;;;;8CACnG,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,6LAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAiE;;;;;;8CAC5F,6LAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA4D;;;;;;sDAC1F,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAU,WAAU;sDAAsH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/J,6LAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAuF;0DAChF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAErD,6LAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAGnD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAgJ;;;;;;0DAG/K,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAuI;;;;;;;;;;;;kDAI3K,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,6LAAC;wDAAI,WAAU;kEAAkH;;;;;;;;;;;;0DAEnI,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAGtC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;8EACf,6LAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,6LAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,6LAAC;;;;;;;;;;;8DAEH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,6LAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,6LAAC;oEAAI,WAAU;8EAAsE;;;;;;;;;;;;sEAIvF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAAgC;;;;;;sFAC/C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,6LAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAwB;;;;;;sFACtC,6LAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;sGAAK;;;;;;sGACN,6LAAC;sGAAK;;;;;;;;;;;;8FAER,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;wFAA6B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;sFAGpE,6LAAC;;8FACC,6LAAC;oFAAI,WAAU;;sGACb,6LAAC;sGAAK;;;;;;sGACN,6LAAC;sGAAK;;;;;;;;;;;;8FAER,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAI,WAAU;wFAA2B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAKtE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;8EAGR,6LAAC;oEAAO,WAAU;;sFAChB,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAaxB,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6D;;;;;;8CAC3E,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAGjD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;sDAIV,6LAAC;4CAAE,MAAK;4CAAI,WAAU;;gDAAoF;8DAExG,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACtG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;sDAIV,6LAAC;4CAAE,MAAK;4CAAI,WAAU;;gDAAkF;8DAEtG,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACtG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAsB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC7G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAyB,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAChH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;sDAIV,6LAAC;4CAAE,MAAK;4CAAI,WAAU;;gDAAoF;8DAExG,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACtG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;8CAM3E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAoB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC3G,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;sDAGzE,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAClC,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2B,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClH,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;wDACjE;;;;;;;;;;;;;sDAIV,6LAAC;4CAAE,MAAK;4CAAI,WAAU;;gDAAkF;8DAEtG,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACtG,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6D;;;;;;8CAC3E,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAGjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAElC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,aAAa;wDAC5B,WAAW,AAAC,uEAIX,OAHC,cAAc,aACV,0CACA;kEAEP;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,aAAa;wDAC5B,WAAW,AAAC,uEAIX,OAHC,cAAc,gBACV,0CACA;kEAEP;;;;;;kEAGD,6LAAC;wDACC,SAAS,IAAM,aAAa;wDAC5B,WAAW,AAAC,uEAIX,OAHC,cAAc,WACV,0CACA;kEAEP;;;;;;;;;;;;;;;;;;;;;;;8CAOP,6LAAC;oCAAI,WAAU;;wCAEZ,cAAc,4BACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAsE;;;;;;0EACtF,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAElC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAkC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACzH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAkC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACzH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAkC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACzH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;;;;;;;0EAK3C,6LAAC;gEAAE,MAAK;gEAAI,WAAU;0EAA2G;;;;;;;;;;;;kEAInI,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAU,SAAQ;gFAAY,MAAK;0FACnF,cAAA,6LAAC;oFAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;8EAKd,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;0FAAsB;;;;;;;;;;;sFAExC,6LAAC;;8FACC,6LAAC;oFAAG,WAAU;8FAAc;;;;;;8FAC5B,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;8EAIzC,6LAAC;oEAAW,WAAU;8EAA4B;;;;;;8EAIlD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,KAAI;4EAAwY,KAAI;4EAAO,WAAU;;;;;;sFACta,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAG,WAAU;8FAAsB;;;;;;8FACpC,6LAAC;oFAAE,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAUlD,cAAc,+BACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAkE;;;;;;0EAClF,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAElC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;;;;;;;0EAK3C,6LAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAsG;;;;;;;;;;;;kEAI9H,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAU,SAAQ;gFAAY,MAAK;0FACnF,cAAA,6LAAC;oFAAK,UAAS;oFAAU,GAAE;oFAAiQ,UAAS;;;;;;;;;;;;;;;;;;;;;;8EAK3S,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA+B;;;;;;8FAC9C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAiC;;;;;;8FAChD,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA+B;;;;;;8FAC9C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA+B;;;;;;8FAC9C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;8EAI3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAmB;;;;;;sFACjC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAK,WAAU;kGAA8B;;;;;;;;;;;8FAEhD,6LAAC;;sGACC,6LAAC;4FAAG,WAAU;sGAAsB;;;;;;sGACpC,6LAAC;4FAAE,WAAU;sGAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAWpD,cAAc,0BACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAkE;;;;;;0EAClF,6LAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,6LAAC;gEAAE,WAAU;0EAAqB;;;;;;0EAElC,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;kFAGzC,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAgC,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FACvH,cAAA,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,6LAAC;;kGACC,6LAAC;wFAAG,WAAU;kGAAwB;;;;;;kGACtC,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;;;;;;;0EAK3C,6LAAC;gEAAE,MAAK;gEAAI,WAAU;0EAAsG;;;;;;;;;;;;kEAI9H,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,OAAM;gFAA6B,WAAU;gFAAU,SAAQ;gFAAY,MAAK;0FACnF,cAAA,6LAAC;oFAAK,UAAS;oFAAU,GAAE;oFAAoS,UAAS;;;;;;;;;;;;;;;;;;;;;;8EAK9U,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;8FAAsB;;;;;;8FACtC,6LAAC;oFAAK,WAAU;8FAA8B;;;;;;;;;;;;sFAEhD,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAI,WAAU;gFAA2B,OAAO;oFAAC,OAAO;gFAAK;;;;;;;;;;;;;;;;;8EAIlE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAgC;;;;;;8FAC/C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA8B;;;;;;8FAC7C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAA8B;;;;;;8FAC7C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;sFAEzC,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FAAgC;;;;;;8FAC/C,6LAAC;oFAAI,WAAU;8FAAwB;;;;;;;;;;;;;;;;;;8EAI3C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAmB;;;;;;sFACjC,6LAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAC1C,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;8FACb,cAAA,6LAAC;wFAAK,WAAU;kGAAgC;;;;;;;;;;;8FAElD,6LAAC;;sGACC,6LAAC;4FAAG,WAAU;sGAAsB;;;;;;sGACpC,6LAAC;4FAAE,WAAU;sGAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAe7D,6LAAC,4IAAA,CAAA,UAAmB;;;;;0BACpB,6LAAC,uIAAA,CAAA,UAAc;;;;;0BACf,6LAAC,qIAAA,CAAA,UAAY;;;;;0BACb,6LAAC,uIAAA,CAAA,UAAc;;;;;0BACf,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GAlqBwB;KAAA", "debugId": null}}]}