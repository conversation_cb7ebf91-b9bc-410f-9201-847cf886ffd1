{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function Dashboard() {\n  const [activeModule, setActiveModule] = useState('overview');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Dashboard Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <svg className=\"h-8 w-8 text-purple mr-3\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n                <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n              </svg>\n              <h1 className=\"text-2xl font-bold text-navy\">Naboria Dashboard</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z\" />\n                </svg>\n              </button>\n              <button className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5z\" />\n                </svg>\n              </button>\n              <div className=\"flex items-center space-x-3\">\n                <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%238B5CF6'/%3E%3Cpath d='M16 10C14.3431 10 13 11.3431 13 13C13 14.6569 14.3431 16 16 16C17.6569 16 19 14.6569 19 13C19 11.3431 17.6569 10 16 10ZM12 21C12 18.7909 13.7909 17 16 17C18.2091 17 20 18.7909 20 21V22H12V21Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-8 h-8 rounded-full\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">Sarah Chen</div>\n                  <div className=\"text-xs text-gray-500\">Admin</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        <aside className=\"w-64 bg-white shadow-sm h-screen sticky top-0\">\n          <nav className=\"p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveModule('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              \n              <button\n                onClick={() => setActiveModule('hr')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'hr' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n                HR Management\n              </button>\n              \n              <button\n                onClick={() => setActiveModule('payroll')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'payroll' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Payroll\n              </button>\n              \n              <button\n                onClick={() => setActiveModule('it')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'it' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n                IT Management\n              </button>\n              \n              <button\n                onClick={() => setActiveModule('finance')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'finance' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Finance\n              </button>\n              \n              <button\n                onClick={() => setActiveModule('automation')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'automation' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n                Automation\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-8\">\n          {activeModule === 'overview' && (\n            <div>\n              <div className=\"mb-8\">\n                <h2 className=\"text-3xl font-bold text-navy mb-2\">Welcome back, Sarah!</h2>\n                <p className=\"text-gray-600\">Here's what's happening with your organization today.</p>\n              </div>\n              \n              {/* Stats Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Employees</p>\n                      <p className=\"text-3xl font-bold text-navy\">247</p>\n                      <p className=\"text-sm text-green-600\">+12 this month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Pending Approvals</p>\n                      <p className=\"text-3xl font-bold text-navy\">8</p>\n                      <p className=\"text-sm text-orange-600\">Requires attention</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Onboarding Progress</p>\n                      <p className=\"text-3xl font-bold text-navy\">94%</p>\n                      <p className=\"text-sm text-green-600\">Above target</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Monthly Payroll</p>\n                      <p className=\"text-3xl font-bold text-navy\">$1.2M</p>\n                      <p className=\"text-sm text-blue-600\">Processing</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Recent Activity */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Recent Activity</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-green/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">New employee onboarded</p>\n                        <p className=\"text-xs text-gray-500\">Emily Johnson joined the Engineering team</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">2h ago</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-blue/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">Payroll processed</p>\n                        <p className=\"text-xs text-gray-500\">December payroll completed successfully</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">4h ago</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-purple/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">IT equipment provisioned</p>\n                        <p className=\"text-xs text-gray-500\">MacBook Pro assigned to Michael Chen</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">6h ago</span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Quick Actions</h3>\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Add Employee</p>\n                      <p className=\"text-xs text-gray-500\">Start onboarding process</p>\n                    </button>\n                    \n                    <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Run Payroll</p>\n                      <p className=\"text-xs text-gray-500\">Process monthly payroll</p>\n                    </button>\n                    \n                    <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-navy/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Generate Report</p>\n                      <p className=\"text-xs text-gray-500\">Create custom reports</p>\n                    </button>\n                    \n                    <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Settings</p>\n                      <p className=\"text-xs text-gray-500\">Configure system</p>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          {activeModule !== 'overview' && (\n            <div className=\"text-center py-16\">\n              <div className=\"w-16 h-16 bg-purple/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-2\">\n                {activeModule.charAt(0).toUpperCase() + activeModule.slice(1)} Module\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                This module is under development. Full functionality coming soon!\n              </p>\n              <button className=\"bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n                Learn More\n              </button>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAA2B,SAAQ;wCAAY,MAAK;wCAAO,OAAM;;0DAC9E,6LAAC;gDAAK,GAAE;gDAAsC,MAAK;gDAAe,aAAY;;;;;;0DAC9E,6LAAC;gDAAK,GAAE;gDAA8C,MAAK;;;;;;;;;;;;kDAE7D,6LAAC;wCAAG,WAAU;kDAA+B;;;;;;;;;;;;0CAE/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAO,WAAU;kDAChB,cAAA,6LAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,KAAI;gDAAwY,KAAI;gDAAO,WAAU;;;;;;0DACta,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,aACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,OACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,YACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,OACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,YACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,6LAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,AAAC,6EAIX,OAHC,iBAAiB,eACb,sDACA;;0DAGN,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,6LAAC;wBAAK,WAAU;;4BACb,iBAAiB,4BAChB,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7G,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAA0B;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjH,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAChH,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/G,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAyB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAChH,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAG1C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAwB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC/G,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAG1C,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAsB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC7G,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAK9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAsB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC7G,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAoB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC3G,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAoB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC3G,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,6LAAC;gEAAO,WAAU;;kFAChB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAA0B,MAAK;4EAAO,SAAQ;4EAAY,QAAO;;8FACjH,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;8FACrE,6LAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;;;;;;;kFAGzE,6LAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQhD,iBAAiB,4BAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAsB,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC7G,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;;4CACX,aAAa,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,KAAK,CAAC;4CAAG;;;;;;;kDAEhE,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,6LAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9H;GAhUwB;KAAA", "debugId": null}}]}