{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/finance/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function FinanceManagement() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedPeriod, setSelectedPeriod] = useState('current-month');\n\n  const expenses = [\n    {\n      id: 1,\n      description: 'Office Supplies',\n      amount: '$1,250.00',\n      category: 'Operations',\n      date: '2024-01-15',\n      status: 'Approved',\n      employee: '<PERSON>'\n    },\n    {\n      id: 2,\n      description: 'Software Licenses',\n      amount: '$5,400.00',\n      category: 'Technology',\n      date: '2024-01-14',\n      status: 'Pending',\n      employee: 'IT Department'\n    },\n    {\n      id: 3,\n      description: 'Travel Expenses',\n      amount: '$2,800.00',\n      category: 'Travel',\n      date: '2024-01-13',\n      status: 'Approved',\n      employee: '<PERSON>'\n    },\n    {\n      id: 4,\n      description: 'Marketing Campaign',\n      amount: '$8,500.00',\n      category: 'Marketing',\n      date: '2024-01-12',\n      status: 'Approved',\n      employee: 'Marketing Team'\n    }\n  ];\n\n  const budgets = [\n    { category: 'Payroll', allocated: 1200000, spent: 1150000, remaining: 50000 },\n    { category: 'Technology', allocated: 150000, spent: 89000, remaining: 61000 },\n    { category: 'Marketing', allocated: 80000, spent: 67000, remaining: 13000 },\n    { category: 'Operations', allocated: 60000, spent: 45000, remaining: 15000 },\n    { category: 'Travel', allocated: 40000, spent: 28000, remaining: 12000 }\n  ];\n\n  const invoices = [\n    { id: 'INV-001', vendor: 'Microsoft Corporation', amount: '$12,350', dueDate: '2024-01-25', status: 'Pending' },\n    { id: 'INV-002', vendor: 'Adobe Systems', amount: '$2,250', dueDate: '2024-01-20', status: 'Overdue' },\n    { id: 'INV-003', vendor: 'Slack Technologies', amount: '$1,975', dueDate: '2024-01-30', status: 'Paid' },\n    { id: 'INV-004', vendor: 'Amazon Web Services', amount: '$4,800', dueDate: '2024-02-05', status: 'Pending' }\n  ];\n\n  const financialMetrics = {\n    totalRevenue: '$2,450,000',\n    totalExpenses: '$1,890,000',\n    netProfit: '$560,000',\n    profitMargin: '22.9%',\n    cashFlow: '+$340,000',\n    burnRate: '$157,500'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Finance Management</h1>\n                <p className=\"text-gray-600 text-sm\">Track expenses, budgets, and financial performance</p>\n              </div>\n            </div>\n            <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto\">\n              <Link\n                href=\"/dashboard/finance/add-expense\"\n                className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors text-center\"\n              >\n                Add Expense\n              </Link>\n              <button className=\"border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors\">\n                Generate Report\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              <button\n                onClick={() => setActiveTab('expenses')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'expenses' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n                Expenses\n                <span className=\"ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full\">\n                  {expenses.filter(e => e.status === 'Pending').length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('budgets')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'budgets' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                </svg>\n                Budgets\n              </button>\n              <button\n                onClick={() => setActiveTab('invoices')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'invoices' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Invoices\n                <span className=\"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full\">\n                  {invoices.filter(i => i.status === 'Overdue').length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('reports')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'reports' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                Reports\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Financial Metrics */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6\">\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">{financialMetrics.totalRevenue}</p>\n                      <p className=\"text-sm text-green-600\">+12.5% from last month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Expenses</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">{financialMetrics.totalExpenses}</p>\n                      <p className=\"text-sm text-orange-600\">+3.2% from last month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Net Profit</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">{financialMetrics.netProfit}</p>\n                      <p className=\"text-sm text-green-600\">Margin: {financialMetrics.profitMargin}</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Cash Flow</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">{financialMetrics.cashFlow}</p>\n                      <p className=\"text-sm text-green-600\">Positive trend</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Monthly Burn Rate</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">{financialMetrics.burnRate}</p>\n                      <p className=\"text-sm text-blue-600\">18 months runway</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-red/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Transactions */}\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Recent Transactions</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {expenses.slice(0, 5).map((expense) => (\n                    <div key={expense.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{expense.description}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {expense.employee} • {expense.category} • {expense.date}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className=\"text-lg font-semibold text-navy\">{expense.amount}</span>\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            expense.status === 'Approved' \n                              ? 'bg-green-100 text-green-800' \n                              : 'bg-orange-100 text-orange-800'\n                          }`}>\n                            {expense.status}\n                          </span>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'expenses' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n                    <h3 className=\"text-lg font-semibold text-navy\">Expense Reports</h3>\n                    <select\n                      value={selectedPeriod}\n                      onChange={(e) => setSelectedPeriod(e.target.value)}\n                      className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    >\n                      <option value=\"current-month\">Current Month</option>\n                      <option value=\"last-month\">Last Month</option>\n                      <option value=\"quarter\">This Quarter</option>\n                      <option value=\"year\">This Year</option>\n                    </select>\n                  </div>\n                </div>\n                \n                {/* Mobile Cards */}\n                <div className=\"lg:hidden divide-y divide-gray-200\">\n                  {expenses.map((expense) => (\n                    <div key={expense.id} className=\"p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{expense.description}</h4>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          expense.status === 'Approved' \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-orange-100 text-orange-800'\n                        }`}>\n                          {expense.status}\n                        </span>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div>\n                          <span className=\"text-gray-500\">Amount:</span>\n                          <p className=\"font-medium\">{expense.amount}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Category:</span>\n                          <p className=\"font-medium\">{expense.category}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Employee:</span>\n                          <p className=\"font-medium\">{expense.employee}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Date:</span>\n                          <p className=\"font-medium\">{expense.date}</p>\n                        </div>\n                      </div>\n                      <div className=\"mt-3 flex space-x-2\">\n                        <button className=\"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium\">\n                          Review\n                        </button>\n                        <button className=\"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium\">\n                          View Receipt\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Description\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Amount\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Category\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Employee\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Date\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {expenses.map((expense) => (\n                        <tr key={expense.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {expense.description}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {expense.amount}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {expense.category}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {expense.employee}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {expense.date}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              expense.status === 'Approved' \n                                ? 'bg-green-100 text-green-800' \n                                : 'bg-orange-100 text-orange-800'\n                            }`}>\n                              {expense.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <button className=\"text-purple hover:text-darkpurple mr-3\">Review</button>\n                            <button className=\"text-gray-400 hover:text-gray-600\">View</button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'budgets' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Budget Overview</h3>\n                <div className=\"space-y-6\">\n                  {budgets.map((budget, index) => (\n                    <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex flex-col sm:flex-row sm:items-center justify-between mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{budget.category}</h4>\n                        <div className=\"text-sm text-gray-500\">\n                          ${budget.spent.toLocaleString()} of ${budget.allocated.toLocaleString()}\n                        </div>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-3 mb-2\">\n                        <div \n                          className={`h-3 rounded-full ${\n                            (budget.spent / budget.allocated) > 0.9 ? 'bg-red-500' :\n                            (budget.spent / budget.allocated) > 0.7 ? 'bg-orange-500' : 'bg-green-500'\n                          }`}\n                          style={{ width: `${Math.min((budget.spent / budget.allocated) * 100, 100)}%` }}\n                        ></div>\n                      </div>\n                      <div className=\"flex justify-between text-sm\">\n                        <span className=\"text-gray-600\">\n                          Remaining: ${budget.remaining.toLocaleString()}\n                        </span>\n                        <span className={`font-medium ${\n                          (budget.spent / budget.allocated) > 0.9 ? 'text-red-600' :\n                          (budget.spent / budget.allocated) > 0.7 ? 'text-orange-600' : 'text-green-600'\n                        }`}>\n                          {Math.round((budget.spent / budget.allocated) * 100)}% used\n                        </span>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'invoices' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Vendor Invoices</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {invoices.map((invoice) => (\n                    <div key={invoice.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{invoice.vendor}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {invoice.id} • Due: {invoice.dueDate}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-4\">\n                          <span className=\"text-lg font-semibold text-navy\">{invoice.amount}</span>\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            invoice.status === 'Paid' ? 'bg-green-100 text-green-800' :\n                            invoice.status === 'Overdue' ? 'bg-red-100 text-red-800' :\n                            'bg-orange-100 text-orange-800'\n                          }`}>\n                            {invoice.status}\n                          </span>\n                          <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                            {invoice.status === 'Paid' ? 'View' : 'Pay Now'}\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'reports' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Financial Reports</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Profit & Loss</h4>\n                    <p className=\"text-sm text-gray-500\">Monthly P&L statement</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Cash Flow</h4>\n                    <p className=\"text-sm text-gray-500\">Cash flow analysis</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Budget vs Actual</h4>\n                    <p className=\"text-sm text-gray-500\">Budget performance report</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Expense Analysis</h4>\n                    <p className=\"text-sm text-gray-500\">Detailed expense breakdown</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Tax Summary</h4>\n                    <p className=\"text-sm text-gray-500\">Tax preparation report</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Vendor Analysis</h4>\n                    <p className=\"text-sm text-gray-500\">Vendor spending analysis</p>\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,aAAa;YACb,QAAQ;YACR,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,aAAa;YACb,QAAQ;YACR,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,aAAa;YACb,QAAQ;YACR,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA;YACE,IAAI;YACJ,aAAa;YACb,QAAQ;YACR,UAAU;YACV,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;KACD;IAED,MAAM,UAAU;QACd;YAAE,UAAU;YAAW,WAAW;YAAS,OAAO;YAAS,WAAW;QAAM;QAC5E;YAAE,UAAU;YAAc,WAAW;YAAQ,OAAO;YAAO,WAAW;QAAM;QAC5E;YAAE,UAAU;YAAa,WAAW;YAAO,OAAO;YAAO,WAAW;QAAM;QAC1E;YAAE,UAAU;YAAc,WAAW;YAAO,OAAO;YAAO,WAAW;QAAM;QAC3E;YAAE,UAAU;YAAU,WAAW;YAAO,OAAO;YAAO,WAAW;QAAM;KACxE;IAED,MAAM,WAAW;QACf;YAAE,IAAI;YAAW,QAAQ;YAAyB,QAAQ;YAAW,SAAS;YAAc,QAAQ;QAAU;QAC9G;YAAE,IAAI;YAAW,QAAQ;YAAiB,QAAQ;YAAU,SAAS;YAAc,QAAQ;QAAU;QACrG;YAAE,IAAI;YAAW,QAAQ;YAAsB,QAAQ;YAAU,SAAS;YAAc,QAAQ;QAAO;QACvG;YAAE,IAAI;YAAW,QAAQ;YAAuB,QAAQ;YAAU,SAAS;YAAc,QAAQ;QAAU;KAC5G;IAED,MAAM,mBAAmB;QACvB,cAAc;QACd,eAAe;QACf,WAAW;QACX,cAAc;QACd,UAAU;QACV,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,6LAAC;wCAAO,WAAU;kDAAuH;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjJ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,aACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,aACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,6LAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;kDAGxD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,YACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,aACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,6LAAC;gDAAK,WAAU;0DACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;kDAGxD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,6EAIX,OAHC,cAAc,YACV,sDACA;;0DAGN,6LAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,6LAAC;wBAAK,WAAU;;4BACb,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA4C,iBAAiB,YAAY;;;;;;8EACtF,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA4C,iBAAiB,aAAa;;;;;;8EACvF,6LAAC;oEAAE,WAAU;8EAA0B;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC9E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA4C,iBAAiB,SAAS;;;;;;8EACnF,6LAAC;oEAAE,WAAU;;wEAAyB;wEAAS,iBAAiB,YAAY;;;;;;;;;;;;;sEAE9E,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC1E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA4C,iBAAiB,QAAQ;;;;;;8EAClF,6LAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC5E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,6LAAC;oEAAE,WAAU;8EAA4C,iBAAiB,QAAQ;;;;;;8EAClF,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;gEAAuB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC3E,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAG,WAAU;8DAAkC;;;;;;;;;;;0DAElD,6LAAC;gDAAI,WAAU;0DACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACzB,6LAAC;wDAAqB,WAAU;kEAC9B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA6B,QAAQ,WAAW;;;;;;sFAC9D,6LAAC;4EAAE,WAAU;;gFACV,QAAQ,QAAQ;gFAAC;gFAAI,QAAQ,QAAQ;gFAAC;gFAAI,QAAQ,IAAI;;;;;;;;;;;;;8EAG3D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAmC,QAAQ,MAAM;;;;;;sFACjE,6LAAC;4EAAK,WAAW,AAAC,kCAIjB,OAHC,QAAQ,MAAM,KAAK,aACf,gCACA;sFAEH,QAAQ,MAAM;;;;;;;;;;;;;;;;;;uDAfb,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;4BA0B7B,cAAc,4BACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAkC;;;;;;kEAChD,6LAAC;wDACC,OAAO;wDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDACjD,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAgB;;;;;;0EAC9B,6LAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,6LAAC;gEAAO,OAAM;0EAAU;;;;;;0EACxB,6LAAC;gEAAO,OAAM;0EAAO;;;;;;;;;;;;;;;;;;;;;;;sDAM3B,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,QAAQ,WAAW;;;;;;8EAC9D,6LAAC;oEAAK,WAAW,AAAC,kCAIjB,OAHC,QAAQ,MAAM,KAAK,aACf,gCACA;8EAEH,QAAQ,MAAM;;;;;;;;;;;;sEAGnB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,QAAQ,MAAM;;;;;;;;;;;;8EAE5C,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,QAAQ,QAAQ;;;;;;;;;;;;8EAE9C,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,QAAQ,QAAQ;;;;;;;;;;;;8EAE9C,6LAAC;;sFACC,6LAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,6LAAC;4EAAE,WAAU;sFAAe,QAAQ,IAAI;;;;;;;;;;;;;;;;;;sEAG5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAAwE;;;;;;8EAG1F,6LAAC;oEAAO,WAAU;8EAAyE;;;;;;;;;;;;;mDAjCrF,QAAQ,EAAE;;;;;;;;;;sDA0CxB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAM,WAAU;;kEACf,6LAAC;wDAAM,WAAU;kEACf,cAAA,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,6LAAC;oEAAG,WAAU;8EAAiF;;;;;;;;;;;;;;;;;kEAKnG,6LAAC;wDAAM,WAAU;kEACd,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gEAAoB,WAAU;;kFAC7B,6LAAC;wEAAG,WAAU;kFACX,QAAQ,WAAW;;;;;;kFAEtB,6LAAC;wEAAG,WAAU;kFACX,QAAQ,MAAM;;;;;;kFAEjB,6LAAC;wEAAG,WAAU;kFACX,QAAQ,QAAQ;;;;;;kFAEnB,6LAAC;wEAAG,WAAU;kFACX,QAAQ,QAAQ;;;;;;kFAEnB,6LAAC;wEAAG,WAAU;kFACX,QAAQ,IAAI;;;;;;kFAEf,6LAAC;wEAAG,WAAU;kFACZ,cAAA,6LAAC;4EAAK,WAAW,AAAC,kCAIjB,OAHC,QAAQ,MAAM,KAAK,aACf,gCACA;sFAEH,QAAQ,MAAM;;;;;;;;;;;kFAGnB,6LAAC;wEAAG,WAAU;;0FACZ,6LAAC;gFAAO,WAAU;0FAAyC;;;;;;0FAC3D,6LAAC;gFAAO,WAAU;0FAAoC;;;;;;;;;;;;;+DA3BjD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAsChC,cAAc,2BACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA6B,OAAO,QAAQ;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;;wEAAwB;wEACnC,OAAO,KAAK,CAAC,cAAc;wEAAG;wEAAM,OAAO,SAAS,CAAC,cAAc;;;;;;;;;;;;;sEAGzE,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,WAAW,AAAC,oBAGX,OAFC,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI,MAAM,eAC1C,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI,MAAM,kBAAkB;gEAE9D,OAAO;oEAAE,OAAO,AAAC,GAAyD,OAAvD,KAAK,GAAG,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI,KAAK,MAAK;gEAAG;;;;;;;;;;;sEAGjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAgB;wEACjB,OAAO,SAAS,CAAC,cAAc;;;;;;;8EAE9C,6LAAC;oEAAK,WAAW,AAAC,eAGjB,OAFC,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI,MAAM,iBAC1C,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI,MAAM,oBAAoB;;wEAE7D,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,SAAS,GAAI;wEAAK;;;;;;;;;;;;;;mDAxBjD;;;;;;;;;;;;;;;;;;;;;4BAkCnB,cAAc,4BACb,6LAAC;0CACC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,6LAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;oDAAqB,WAAU;8DAC9B,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA6B,QAAQ,MAAM;;;;;;kFACzD,6LAAC;wEAAE,WAAU;;4EACV,QAAQ,EAAE;4EAAC;4EAAS,QAAQ,OAAO;;;;;;;;;;;;;0EAGxC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAmC,QAAQ,MAAM;;;;;;kFACjE,6LAAC;wEAAK,WAAW,AAAC,kCAIjB,OAHC,QAAQ,MAAM,KAAK,SAAS,gCAC5B,QAAQ,MAAM,KAAK,YAAY,4BAC/B;kFAEC,QAAQ,MAAM;;;;;;kFAEjB,6LAAC;wEAAO,WAAU;kFACf,QAAQ,MAAM,KAAK,SAAS,SAAS;;;;;;;;;;;;;;;;;;mDAlBpC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;4BA6B7B,cAAc,2BACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzD;GA3hBwB;KAAA", "debugId": null}}]}