(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{3506:(e,s,r)=>{Promise.resolve().then(r.bind(r,9690))},9690:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var l=r(5155),t=r(2115),a=r(6874),n=r.n(a);function i(){let[e,s]=(0,t.useState)({email:"",password:"",rememberMe:!1}),r=r=>{let{name:l,value:t,type:a,checked:n}=r.target;s({...e,[l]:"checkbox"===a?n:t})};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)("svg",{className:"h-12 w-12 text-purple",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("path",{d:"M20 5L30 10V30L20 35L10 30V10L20 5Z",fill:"currentColor",fillOpacity:"0.8"}),(0,l.jsx)("path",{d:"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z",fill:"white"})]})}),(0,l.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-navy",children:"Sign in to Naboria"}),(0,l.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Or"," ",(0,l.jsx)(n(),{href:"/signup",className:"font-medium text-purple hover:text-darkpurple",children:"create a new account"})]})]}),(0,l.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[(0,l.jsxs)("div",{className:"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10",children:[(0,l.jsxs)("form",{className:"space-y-6",onSubmit:s=>{s.preventDefault(),console.log("Login attempt:",e),window.location.href="/dashboard"},children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Enter your email"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,value:e.password,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Enter your password"})})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{id:"rememberMe",name:"rememberMe",type:"checkbox",checked:e.rememberMe,onChange:r,className:"h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"}),(0,l.jsx)("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,l.jsx)("div",{className:"text-sm",children:(0,l.jsx)("a",{href:"#",className:"font-medium text-purple hover:text-darkpurple",children:"Forgot your password?"})})]}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple transition-all",children:"Sign in"})})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]}),(0,l.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,l.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[(0,l.jsxs)("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",children:[(0,l.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,l.jsx)("span",{className:"ml-2",children:"Google"})]}),(0,l.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[(0,l.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),(0,l.jsx)("span",{className:"ml-2",children:"LinkedIn"})]})]})]}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"Demo Credentials"}),(0,l.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Email:"})," <EMAIL>"]}),(0,l.jsxs)("p",{children:[(0,l.jsx)("strong",{children:"Password:"})," demo123"]}),(0,l.jsx)("p",{className:"text-purple",children:"Use these credentials to explore the dashboard"})]})]})})]}),(0,l.jsx)("div",{className:"mt-8 text-center",children:(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Need help?"," ",(0,l.jsx)(n(),{href:"/contact",className:"font-medium text-purple hover:text-darkpurple",children:"Contact support"})]})})]})]})}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=3506)),_N_E=e.O()}]);