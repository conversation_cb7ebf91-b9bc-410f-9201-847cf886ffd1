(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[673],{103:(e,s,t)=>{Promise.resolve().then(t.bind(t,7111))},7111:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(5155),a=t(2115),n=t(6874),l=t.n(n);function i(){let[e,s]=(0,a.useState)("overview"),t=[{id:1,name:"Employee Onboarding",description:"Automated new hire setup and account creation",status:"Active",triggers:12,lastRun:"2024-01-15 09:30",success:98.5},{id:2,name:"Expense Approval",description:"Auto-approve expenses under $500",status:"Active",triggers:45,lastRun:"2024-01-15 14:22",success:100},{id:3,name:"IT Equipment Request",description:"Automated equipment provisioning workflow",status:"Active",triggers:8,lastRun:"2024-01-14 16:45",success:95.2},{id:4,name:"Payroll Processing",description:"Monthly payroll calculation and distribution",status:"Scheduled",triggers:1,lastRun:"2023-12-31 23:59",success:100}],n=[{name:"Slack",status:"Connected",type:"Communication",lastSync:"2024-01-15 15:30"},{name:"Google Workspace",status:"Connected",type:"Productivity",lastSync:"2024-01-15 15:25"},{name:"Microsoft 365",status:"Connected",type:"Productivity",lastSync:"2024-01-15 15:20"},{name:"Salesforce",status:"Connected",type:"CRM",lastSync:"2024-01-15 15:15"},{name:"QuickBooks",status:"Connected",type:"Finance",lastSync:"2024-01-15 15:10"},{name:"Jira",status:"Error",type:"Project Management",lastSync:"2024-01-15 12:00"}],i=[{id:1,workflow:"Employee Onboarding",action:"Created accounts for Emily Johnson",timestamp:"2024-01-15 09:30",status:"Success"},{id:2,workflow:"Expense Approval",action:"Auto-approved $245 office supplies expense",timestamp:"2024-01-15 14:22",status:"Success"},{id:3,workflow:"IT Equipment Request",action:"Provisioned MacBook for new hire",timestamp:"2024-01-14 16:45",status:"Success"},{id:4,workflow:"Security Alert",action:"Detected suspicious login attempt",timestamp:"2024-01-14 11:20",status:"Warning"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(l(),{href:"/dashboard",className:"flex items-center mr-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-purple mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-navy",children:"Automation Center"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Manage workflows, integrations, and automated processes"})]})]}),(0,r.jsx)("button",{className:"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto",children:"Create Workflow"})]})})}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,r.jsx)("aside",{className:"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0",children:(0,r.jsx)("nav",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("button",{onClick:()=>s("overview"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("overview"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Overview"]}),(0,r.jsxs)("button",{onClick:()=>s("workflows"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("workflows"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Workflows",(0,r.jsx)("span",{className:"ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:t.filter(e=>"Active"===e.status).length})]}),(0,r.jsxs)("button",{onClick:()=>s("integrations"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("integrations"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Integrations",(0,r.jsx)("span",{className:"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full",children:n.filter(e=>"Error"===e.status).length})]}),(0,r.jsxs)("button",{onClick:()=>s("rules"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("rules"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})}),"Rules"]}),(0,r.jsxs)("button",{onClick:()=>s("activity"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("activity"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Activity Log"]})]})})}),(0,r.jsxs)("main",{className:"flex-1 p-4 lg:p-8",children:["overview"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Workflows"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:t.filter(e=>"Active"===e.status).length}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"All running smoothly"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tasks Automated"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"1,247"}),(0,r.jsx)("p",{className:"text-sm text-blue-600",children:"This month"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Time Saved"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"156h"}),(0,r.jsx)("p",{className:"text-sm text-purple",children:"This month"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Success Rate"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"98.4%"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"Excellent"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"Quick Actions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Create Workflow"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Build new automation"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 9l3 3-3 3m5 0h3"})})}),(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Add Integration"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Connect new service"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})})}),(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Create Rule"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Set up automation rule"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"View Reports"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Automation analytics"})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Recent Activity"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:i.slice(0,5).map(e=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("Success"===e.status?"bg-green/10":"Warning"===e.status?"bg-orange/10":"bg-red/10"),children:(0,r.jsx)("svg",{className:"h-4 w-4 ".concat("Success"===e.status?"text-green-500":"Warning"===e.status?"text-orange-500":"text-red-500"),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:"Success"===e.status?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.workflow," • ",e.timestamp]})]}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Success"===e.status?"bg-green-100 text-green-800":"Warning"===e.status?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"),children:e.status})]})},e.id))})]})]}),"workflows"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Automation Workflows"})}),(0,r.jsx)("div",{className:"lg:hidden divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Active"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Triggers:"}),(0,r.jsx)("p",{className:"font-medium",children:e.triggers})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Success Rate:"}),(0,r.jsxs)("p",{className:"font-medium",children:[e.success,"%"]})]}),(0,r.jsxs)("div",{className:"col-span-2",children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Last Run:"}),(0,r.jsx)("p",{className:"font-medium",children:e.lastRun})]})]}),(0,r.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,r.jsx)("button",{className:"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium",children:"Edit"}),(0,r.jsx)("button",{className:"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium",children:"View Logs"})]})]},e.id))}),(0,r.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Workflow"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Triggers"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Success Rate"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Run"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:t.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Active"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.triggers}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[e.success,"%"]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.lastRun}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)("button",{className:"text-purple hover:text-darkpurple mr-3",children:"Edit"}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:"View Logs"})]})]},e.id))})]})})]})}),"integrations"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"System Integrations"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:n.map((e,s)=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.type," • Last sync: ",e.lastSync]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Connected"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.status}),(0,r.jsx)("button",{className:"text-purple hover:text-darkpurple text-sm font-medium",children:"Connected"===e.status?"Configure":"Reconnect"})]})]})},s))})]})}),"rules"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Automation Rules"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:[{id:1,name:"Auto-assign IT equipment",condition:"New employee in Engineering department",action:"Create equipment request for laptop and monitor",enabled:!0},{id:2,name:"Expense auto-approval",condition:"Expense amount < $500 AND category = Office Supplies",action:"Automatically approve and process payment",enabled:!0},{id:3,name:"Time-off notification",condition:"Time-off request submitted",action:"Notify manager and update calendar",enabled:!0},{id:4,name:"Security alert",condition:"Failed login attempts > 5",action:"Lock account and notify IT security",enabled:!1}].map(e=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start justify-between space-y-3 lg:space-y-0",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,r.jsx)("input",{type:"checkbox",checked:e.enabled,className:"sr-only peer",readOnly:!0}),(0,r.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple"})]})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mb-1",children:[(0,r.jsx)("strong",{children:"When:"})," ",e.condition]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,r.jsx)("strong",{children:"Then:"})," ",e.action]})]}),(0,r.jsx)("button",{className:"text-purple hover:text-darkpurple text-sm font-medium",children:"Edit Rule"})]})},e.id))})]})}),"activity"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Activity Log"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("Success"===e.status?"bg-green/10":"Warning"===e.status?"bg-orange/10":"bg-red/10"),children:(0,r.jsx)("svg",{className:"h-4 w-4 ".concat("Success"===e.status?"text-green-500":"Warning"===e.status?"text-orange-500":"text-red-500"),fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:"Success"===e.status?(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"}):(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.action}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[e.workflow," • ",e.timestamp]})]}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Success"===e.status?"bg-green-100 text-green-800":"Warning"===e.status?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"),children:e.status})]})},e.id))})]})})]})]})]})}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=103)),_N_E=e.O()}]);