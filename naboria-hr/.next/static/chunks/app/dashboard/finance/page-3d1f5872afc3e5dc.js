(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[312],{2946:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(5155),a=t(2115),l=t(6874),n=t.n(l);function d(){let[e,s]=(0,a.useState)("overview"),[t,l]=(0,a.useState)("current-month"),d=[{id:1,description:"Office Supplies",amount:"$1,250.00",category:"Operations",date:"2024-01-15",status:"Approved",employee:"<PERSON>"},{id:2,description:"Software Licenses",amount:"$5,400.00",category:"Technology",date:"2024-01-14",status:"Pending",employee:"IT Department"},{id:3,description:"Travel Expenses",amount:"$2,800.00",category:"Travel",date:"2024-01-13",status:"Approved",employee:"<PERSON>"},{id:4,description:"Marketing Campaign",amount:"$8,500.00",category:"Marketing",date:"2024-01-12",status:"Approved",employee:"Marketing Team"}],i=[{id:"INV-001",vendor:"Microsoft Corporation",amount:"$12,350",dueDate:"2024-01-25",status:"Pending"},{id:"INV-002",vendor:"Adobe Systems",amount:"$2,250",dueDate:"2024-01-20",status:"Overdue"},{id:"INV-003",vendor:"Slack Technologies",amount:"$1,975",dueDate:"2024-01-30",status:"Paid"},{id:"INV-004",vendor:"Amazon Web Services",amount:"$4,800",dueDate:"2024-02-05",status:"Pending"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n(),{href:"/dashboard",className:"flex items-center mr-4",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-purple mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-navy",children:"Finance Management"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Track expenses, budgets, and financial performance"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full sm:w-auto",children:[(0,r.jsx)("button",{className:"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Add Expense"}),(0,r.jsx)("button",{className:"border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Generate Report"})]})]})})}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,r.jsx)("aside",{className:"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0",children:(0,r.jsx)("nav",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("button",{onClick:()=>s("overview"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("overview"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Overview"]}),(0,r.jsxs)("button",{onClick:()=>s("expenses"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("expenses"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})}),"Expenses",(0,r.jsx)("span",{className:"ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full",children:d.filter(e=>"Pending"===e.status).length})]}),(0,r.jsxs)("button",{onClick:()=>s("budgets"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("budgets"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Budgets"]}),(0,r.jsxs)("button",{onClick:()=>s("invoices"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("invoices"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Invoices",(0,r.jsx)("span",{className:"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full",children:i.filter(e=>"Overdue"===e.status).length})]}),(0,r.jsxs)("button",{onClick:()=>s("reports"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("reports"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Reports"]})]})})}),(0,r.jsxs)("main",{className:"flex-1 p-4 lg:p-8",children:["overview"===e&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6",children:[(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Revenue"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$2,450,000"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"+12.5% from last month"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Expenses"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$1,890,000"}),(0,r.jsx)("p",{className:"text-sm text-orange-600",children:"+3.2% from last month"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Net Profit"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$560,000"}),(0,r.jsxs)("p",{className:"text-sm text-green-600",children:["Margin: ","22.9%"]})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Cash Flow"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"+$340,000"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"Positive trend"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})})})]})}),(0,r.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Monthly Burn Rate"}),(0,r.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$157,500"}),(0,r.jsx)("p",{className:"text-sm text-blue-600",children:"18 months runway"})]}),(0,r.jsx)("div",{className:"w-12 h-12 bg-red/10 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-6 w-6 text-red-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z"})})})]})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Recent Transactions"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:d.slice(0,5).map(e=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.description}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.employee," • ",e.category," • ",e.date]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-lg font-semibold text-navy",children:e.amount}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Approved"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})]})]})},e.id))})]})]}),"expenses"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Expense Reports"}),(0,r.jsxs)("select",{value:t,onChange:e=>l(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent",children:[(0,r.jsx)("option",{value:"current-month",children:"Current Month"}),(0,r.jsx)("option",{value:"last-month",children:"Last Month"}),(0,r.jsx)("option",{value:"quarter",children:"This Quarter"}),(0,r.jsx)("option",{value:"year",children:"This Year"})]})]})}),(0,r.jsx)("div",{className:"lg:hidden divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.description}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Approved"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Amount:"}),(0,r.jsx)("p",{className:"font-medium",children:e.amount})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Category:"}),(0,r.jsx)("p",{className:"font-medium",children:e.category})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Employee:"}),(0,r.jsx)("p",{className:"font-medium",children:e.employee})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-500",children:"Date:"}),(0,r.jsx)("p",{className:"font-medium",children:e.date})]})]}),(0,r.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,r.jsx)("button",{className:"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium",children:"Review"}),(0,r.jsx)("button",{className:"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium",children:"View Receipt"})]})]},e.id))}),(0,r.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Description"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Employee"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.description}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.amount}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.employee}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.date}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Approved"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,r.jsx)("button",{className:"text-purple hover:text-darkpurple mr-3",children:"Review"}),(0,r.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:"View"})]})]},e.id))})]})})]})}),"budgets"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy mb-6",children:"Budget Overview"}),(0,r.jsx)("div",{className:"space-y-6",children:[{category:"Payroll",allocated:12e5,spent:115e4,remaining:5e4},{category:"Technology",allocated:15e4,spent:89e3,remaining:61e3},{category:"Marketing",allocated:8e4,spent:67e3,remaining:13e3},{category:"Operations",allocated:6e4,spent:45e3,remaining:15e3},{category:"Travel",allocated:4e4,spent:28e3,remaining:12e3}].map((e,s)=>(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between mb-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.category}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["$",e.spent.toLocaleString()," of $",e.allocated.toLocaleString()]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 mb-2",children:(0,r.jsx)("div",{className:"h-3 rounded-full ".concat(e.spent/e.allocated>.9?"bg-red-500":e.spent/e.allocated>.7?"bg-orange-500":"bg-green-500"),style:{width:"".concat(Math.min(e.spent/e.allocated*100,100),"%")}})}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:["Remaining: $",e.remaining.toLocaleString()]}),(0,r.jsxs)("span",{className:"font-medium ".concat(e.spent/e.allocated>.9?"text-red-600":e.spent/e.allocated>.7?"text-orange-600":"text-green-600"),children:[Math.round(e.spent/e.allocated*100),"% used"]})]})]},s))})]})}),"invoices"===e&&(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,r.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Vendor Invoices"})}),(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:i.map(e=>(0,r.jsx)("div",{className:"p-4 lg:p-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:e.vendor}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[e.id," • Due: ",e.dueDate]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"text-lg font-semibold text-navy",children:e.amount}),(0,r.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Paid"===e.status?"bg-green-100 text-green-800":"Overdue"===e.status?"bg-red-100 text-red-800":"bg-orange-100 text-orange-800"),children:e.status}),(0,r.jsx)("button",{className:"text-purple hover:text-darkpurple text-sm font-medium",children:"Paid"===e.status?"View":"Pay Now"})]})]})},e.id))})]})}),"reports"===e&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-navy mb-6",children:"Financial Reports"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Profit & Loss"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Monthly P&L statement"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Cash Flow"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Cash flow analysis"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Budget vs Actual"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Budget performance report"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Expense Analysis"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Detailed expense breakdown"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tax Summary"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Tax preparation report"})]}),(0,r.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Vendor Analysis"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Vendor spending analysis"})]})]})]})})]})]})]})}},7632:(e,s,t)=>{Promise.resolve().then(t.bind(t,2946))}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=7632)),_N_E=e.O()}]);