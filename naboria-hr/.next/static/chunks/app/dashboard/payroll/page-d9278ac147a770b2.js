(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[827],{3845:(e,s,t)=>{Promise.resolve().then(t.bind(t,4209))},4209:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(5155),r=t(2115),l=t(6874),n=t.n(l);function i(){let[e,s]=(0,r.useState)("overview"),[t,l]=(0,r.useState)("current"),i=[{id:1,period:"December 2024",status:"Completed",employees:247,totalAmount:"$1,234,567",runDate:"2024-12-31",payDate:"2025-01-05"},{id:2,period:"November 2024",status:"Completed",employees:245,totalAmount:"$1,198,432",runDate:"2024-11-30",payDate:"2024-12-05"},{id:3,period:"January 2025",status:"In Progress",employees:250,totalAmount:"$1,267,890",runDate:"2025-01-31",payDate:"2025-02-05"}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n(),{href:"/dashboard",className:"flex items-center mr-4",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-purple mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-navy",children:"Payroll Management"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Process payroll, manage taxes, and track payments"})]})]}),(0,a.jsx)("button",{className:"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto",children:"Run Payroll"})]})})}),(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,a.jsx)("aside",{className:"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0",children:(0,a.jsx)("nav",{className:"p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("button",{onClick:()=>s("overview"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("overview"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,a.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Overview"]}),(0,a.jsxs)("button",{onClick:()=>s("runs"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("runs"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,a.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Payroll Runs"]}),(0,a.jsxs)("button",{onClick:()=>s("taxes"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("taxes"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,a.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Tax Management"]}),(0,a.jsxs)("button",{onClick:()=>s("reports"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("reports"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,a.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Reports"]}),(0,a.jsxs)("button",{onClick:()=>s("compliance"),className:"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ".concat("compliance"===e?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"),children:[(0,a.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})}),"Compliance"]})]})})}),(0,a.jsxs)("main",{className:"flex-1 p-4 lg:p-8",children:["overview"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Monthly Payroll"}),(0,a.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$1.2M"}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:"+5.2% from last month"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,a.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Employees Paid"}),(0,a.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"247"}),(0,a.jsx)("p",{className:"text-sm text-blue-600",children:"100% processed"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})})]})}),(0,a.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tax Withholdings"}),(0,a.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"$352K"}),(0,a.jsx)("p",{className:"text-sm text-purple",children:"29.3% of gross"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"})})})]})}),(0,a.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Next Payroll"}),(0,a.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"Jan 31"}),(0,a.jsx)("p",{className:"text-sm text-orange-600",children:"12 days remaining"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)("svg",{className:"h-6 w-6 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Upcoming Payments"})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:[{employee:"Sarah Chen",amount:"$5,200",date:"2025-01-15",type:"Salary"},{employee:"Michael Rodriguez",amount:"$3,800",date:"2025-01-15",type:"Salary"},{employee:"Emily Johnson",amount:"$4,100",date:"2025-01-15",type:"Salary"},{employee:"David Kim",amount:"$1,200",date:"2025-01-20",type:"Bonus"}].map((e,s)=>(0,a.jsx)("div",{className:"p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.employee}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[e.type," • Due ",e.date]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("p",{className:"text-lg font-semibold text-navy",children:e.amount})})]})},s))})]})]}),"runs"===e&&(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,a.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Payroll Runs"})}),(0,a.jsx)("div",{className:"lg:hidden divide-y divide-gray-200",children:i.map(e=>(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.period}),(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Completed"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Employees:"}),(0,a.jsx)("p",{className:"font-medium",children:e.employees})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Total:"}),(0,a.jsx)("p",{className:"font-medium",children:e.totalAmount})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Run Date:"}),(0,a.jsx)("p",{className:"font-medium",children:e.runDate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Pay Date:"}),(0,a.jsx)("p",{className:"font-medium",children:e.payDate})]})]}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,a.jsx)("button",{className:"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium",children:"View Details"}),(0,a.jsx)("button",{className:"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium",children:"Download"})]})]},e.id))}),(0,a.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Period"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Employees"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pay Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.period}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs rounded-full ".concat("Completed"===e.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"),children:e.status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.employees}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.totalAmount}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.payDate}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{className:"text-purple hover:text-darkpurple mr-3",children:"View"}),(0,a.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:"Download"})]})]},e.id))})]})})]})}),"taxes"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-6",children:"Tax Summary (YTD)"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Federal Tax"}),(0,a.jsx)("p",{className:"text-xl font-bold text-navy",children:"$156,789"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"State Tax"}),(0,a.jsx)("p",{className:"text-xl font-bold text-navy",children:"$89,234"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Social Security"}),(0,a.jsx)("p",{className:"text-xl font-bold text-navy",children:"$76,543"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Medicare"}),(0,a.jsx)("p",{className:"text-xl font-bold text-navy",children:"$17,890"})]}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Unemployment"}),(0,a.jsx)("p",{className:"text-xl font-bold text-navy",children:"$12,345"})]})]})]})}),"reports"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-6",children:"Payroll Reports"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Payroll Summary"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Detailed payroll breakdown by period"})]}),(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Tax Reports"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Federal and state tax filings"})]}),(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Employee Earnings"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Individual employee pay statements"})]})]})]})}),"compliance"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-xl shadow-sm",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-6",children:"Compliance Status"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{className:"font-medium",children:"Federal Tax Filing"})]}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Up to date"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-green-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-green-500 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{className:"font-medium",children:"State Compliance"})]}),(0,a.jsx)("span",{className:"text-green-600 text-sm",children:"Compliant"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-orange-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-5 w-5 text-orange-500 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("span",{className:"font-medium",children:"Quarterly Filing"})]}),(0,a.jsx)("span",{className:"text-orange-600 text-sm",children:"Due in 15 days"})]})]})]})})]})]})]})}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=3845)),_N_E=e.O()}]);