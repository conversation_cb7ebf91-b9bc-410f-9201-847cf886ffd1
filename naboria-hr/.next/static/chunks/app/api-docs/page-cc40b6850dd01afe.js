(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[287],{531:(e,s,l)=>{Promise.resolve().then(l.bind(l,4757))},4757:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>r});var a=l(5155),t=l(2115),n=l(6874),i=l.n(n);function r(){let[e,s]=(0,t.useState)("getting-started");return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(i(),{href:"/",className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"h-8 w-8 text-purple mr-3",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M20 5L30 10V30L20 35L10 30V10L20 5Z",fill:"currentColor",fillOpacity:"0.8"}),(0,a.jsx)("path",{d:"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z",fill:"white"})]}),(0,a.jsx)("span",{className:"text-xl font-bold text-navy",children:"Naboria API"})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(i(),{href:"/login",className:"text-navy font-medium hover:text-purple transition-colors",children:"Login"}),(0,a.jsx)(i(),{href:"/signup",className:"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors",children:"Get API Key"})]})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("aside",{className:"w-64 mr-8",children:(0,a.jsx)("nav",{className:"sticky top-8",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("button",{onClick:()=>s("getting-started"),className:"w-full text-left px-4 py-2 rounded-lg transition-colors ".concat("getting-started"===e?"bg-purple/10 text-purple font-medium":"text-gray-600 hover:bg-gray-100"),children:"Getting Started"}),(0,a.jsx)("button",{onClick:()=>s("authentication"),className:"w-full text-left px-4 py-2 rounded-lg transition-colors ".concat("authentication"===e?"bg-purple/10 text-purple font-medium":"text-gray-600 hover:bg-gray-100"),children:"Authentication"}),(0,a.jsx)("button",{onClick:()=>s("employees"),className:"w-full text-left px-4 py-2 rounded-lg transition-colors ".concat("employees"===e?"bg-purple/10 text-purple font-medium":"text-gray-600 hover:bg-gray-100"),children:"Employees API"}),(0,a.jsx)("button",{onClick:()=>s("payroll"),className:"w-full text-left px-4 py-2 rounded-lg transition-colors ".concat("payroll"===e?"bg-purple/10 text-purple font-medium":"text-gray-600 hover:bg-gray-100"),children:"Payroll API"}),(0,a.jsx)("button",{onClick:()=>s("webhooks"),className:"w-full text-left px-4 py-2 rounded-lg transition-colors ".concat("webhooks"===e?"bg-purple/10 text-purple font-medium":"text-gray-600 hover:bg-gray-100"),children:"Webhooks"})]})})}),(0,a.jsxs)("main",{className:"flex-1",children:["getting-started"===e&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6",children:"Getting Started with Naboria API"}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The Naboria API allows you to integrate your applications with our HR, payroll, and IT management platform. Our RESTful API is designed to be simple, predictable, and developer-friendly."}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Base URL"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg mb-6",children:(0,a.jsx)("code",{className:"text-sm",children:"https://api.naboria.com/v1"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Quick Start"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 mb-6",children:[(0,a.jsx)("li",{children:"Sign up for a Naboria account"}),(0,a.jsx)("li",{children:"Generate an API key from your dashboard"}),(0,a.jsx)("li",{children:"Make your first API call"})]}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Example Request"}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'curl -X GET "https://api.naboria.com/v1/employees" \\\n  -H "Authorization: Bearer YOUR_API_KEY" \\\n  -H "Content-Type: application/json"'})})}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"Rate Limiting"}),(0,a.jsx)("div",{className:"mt-1 text-sm text-blue-700",children:(0,a.jsx)("p",{children:"API requests are limited to 1000 requests per hour per API key."})})]})]})})]})]}),"authentication"===e&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6",children:"Authentication"}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Naboria API uses API keys for authentication. Include your API key in the Authorization header of every request."}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"API Key Authentication"}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:"Authorization: Bearer YOUR_API_KEY"})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Getting Your API Key"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 mb-6",children:[(0,a.jsx)("li",{children:"Log in to your Naboria dashboard"}),(0,a.jsx)("li",{children:"Navigate to Settings → API Keys"}),(0,a.jsx)("li",{children:'Click "Generate New API Key"'}),(0,a.jsx)("li",{children:"Copy and securely store your API key"})]}),(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Keep Your API Key Secure"}),(0,a.jsx)("div",{className:"mt-1 text-sm text-red-700",children:(0,a.jsx)("p",{children:"Never expose your API key in client-side code or public repositories."})})]})]})})]})]}),"employees"===e&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6",children:"Employees API"}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Manage employee data, including creating, updating, and retrieving employee information."}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"List Employees"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg mb-4",children:(0,a.jsx)("code",{className:"text-sm",children:"GET /employees"})}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'curl -X GET "https://api.naboria.com/v1/employees" \\\n  -H "Authorization: Bearer YOUR_API_KEY"'})})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-navy mb-2",children:"Response"}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'{\n  "data": [\n    {\n      "id": "emp_123",\n      "first_name": "John",\n      "last_name": "Doe",\n      "email": "<EMAIL>",\n      "department": "Engineering",\n      "position": "Software Engineer",\n      "start_date": "2023-01-15",\n      "status": "active"\n    }\n  ],\n  "pagination": {\n    "page": 1,\n    "per_page": 25,\n    "total": 1\n  }\n}'})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Create Employee"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg mb-4",children:(0,a.jsx)("code",{className:"text-sm",children:"POST /employees"})}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'curl -X POST "https://api.naboria.com/v1/employees" \\\n  -H "Authorization: Bearer YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "first_name": "Jane",\n    "last_name": "Smith",\n    "email": "<EMAIL>",\n    "department": "Marketing",\n    "position": "Marketing Manager",\n    "start_date": "2024-01-01"\n  }\''})})})]})]}),"payroll"===e&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6",children:"Payroll API"}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Access payroll data, run payroll processes, and manage compensation information."}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Get Payroll Runs"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg mb-4",children:(0,a.jsx)("code",{className:"text-sm",children:"GET /payroll/runs"})}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'curl -X GET "https://api.naboria.com/v1/payroll/runs" \\\n  -H "Authorization: Bearer YOUR_API_KEY"'})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Create Payroll Run"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded-lg mb-4",children:(0,a.jsx)("code",{className:"text-sm",children:"POST /payroll/runs"})}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'curl -X POST "https://api.naboria.com/v1/payroll/runs" \\\n  -H "Authorization: Bearer YOUR_API_KEY" \\\n  -H "Content-Type: application/json" \\\n  -d \'{\n    "pay_period_start": "2024-01-01",\n    "pay_period_end": "2024-01-15",\n    "pay_date": "2024-01-20"\n  }\''})})}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 p-4 rounded-lg",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Payroll Operations"}),(0,a.jsx)("div",{className:"mt-1 text-sm text-yellow-700",children:(0,a.jsx)("p",{children:"Payroll operations are sensitive. Always test in sandbox mode first."})})]})]})})]})]}),"webhooks"===e&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6",children:"Webhooks"}),(0,a.jsxs)("div",{className:"prose max-w-none",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Webhooks allow you to receive real-time notifications when events occur in your Naboria account."}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Supported Events"}),(0,a.jsxs)("ul",{className:"list-disc list-inside space-y-2 mb-6",children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{children:"employee.created"})," - New employee added"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{children:"employee.updated"})," - Employee information changed"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{children:"payroll.run.completed"})," - Payroll run finished"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("code",{children:"onboarding.completed"})," - Employee onboarding finished"]})]}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Webhook Payload Example"}),(0,a.jsx)("div",{className:"bg-gray-900 text-white p-4 rounded-lg mb-6 overflow-x-auto",children:(0,a.jsx)("pre",{children:(0,a.jsx)("code",{children:'{\n  "event": "employee.created",\n  "data": {\n    "id": "emp_123",\n    "first_name": "John",\n    "last_name": "Doe",\n    "email": "<EMAIL>"\n  },\n  "timestamp": "2024-01-15T10:30:00Z"\n}'})})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-navy mb-4",children:"Configuring Webhooks"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 mb-6",children:[(0,a.jsx)("li",{children:"Go to Settings → Webhooks in your dashboard"}),(0,a.jsx)("li",{children:'Click "Add Webhook Endpoint"'}),(0,a.jsx)("li",{children:"Enter your endpoint URL"}),(0,a.jsx)("li",{children:"Select the events you want to receive"}),(0,a.jsx)("li",{children:"Save your configuration"})]})]})]})]})]})})]})}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=531)),_N_E=e.O()}]);