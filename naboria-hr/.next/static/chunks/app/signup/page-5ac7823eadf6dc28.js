(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{6467:(e,s,r)=>{Promise.resolve().then(r.bind(r,9349))},9349:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var l=r(5155),a=r(2115),t=r(6874),o=r.n(t);function n(){let[e,s]=(0,a.useState)({firstName:"",lastName:"",email:"",company:"",employees:"",password:"",confirmPassword:"",agreeToTerms:!1}),r=r=>{let{name:l,value:a,type:t}=r.target,o=r.target.checked;s({...e,[l]:"checkbox"===t?o:a})};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)("svg",{className:"h-12 w-12 text-purple",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,l.jsx)("path",{d:"M20 5L30 10V30L20 35L10 30V10L20 5Z",fill:"currentColor",fillOpacity:"0.8"}),(0,l.jsx)("path",{d:"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z",fill:"white"})]})}),(0,l.jsx)("h2",{className:"mt-6 text-center text-3xl font-bold text-navy",children:"Create your Naboria account"}),(0,l.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Already have an account?"," ",(0,l.jsx)(o(),{href:"/login",className:"font-medium text-purple hover:text-darkpurple",children:"Sign in here"})]})]}),(0,l.jsxs)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:[(0,l.jsxs)("div",{className:"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10",children:[(0,l.jsxs)("form",{className:"space-y-6",onSubmit:s=>(s.preventDefault(),e.password!==e.confirmPassword)?void alert("Passwords do not match"):e.agreeToTerms?void(console.log("Signup attempt:",e),alert("Account created successfully! Please check your email to verify your account.")):void alert("Please agree to the terms and conditions"),children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700",children:"First name"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:e.firstName,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"John"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700",children:"Last name"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:e.lastName,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Doe"})})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"Email address"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"<EMAIL>"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"company",className:"block text-sm font-medium text-gray-700",children:"Company name"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"company",name:"company",type:"text",required:!0,value:e.company,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Acme Corp"})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"employees",className:"block text-sm font-medium text-gray-700",children:"Company size"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsxs)("select",{id:"employees",name:"employees",required:!0,value:e.employees,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",children:[(0,l.jsx)("option",{value:"",children:"Select company size"}),(0,l.jsx)("option",{value:"1-10",children:"1-10 employees"}),(0,l.jsx)("option",{value:"11-50",children:"11-50 employees"}),(0,l.jsx)("option",{value:"51-200",children:"51-200 employees"}),(0,l.jsx)("option",{value:"201-500",children:"201-500 employees"}),(0,l.jsx)("option",{value:"500+",children:"500+ employees"})]})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"Password"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:e.password,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Create a strong password"})}),(0,l.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"Must be at least 8 characters with uppercase, lowercase, and numbers"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"Confirm password"}),(0,l.jsx)("div",{className:"mt-1",children:(0,l.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:"password",required:!0,value:e.confirmPassword,onChange:r,className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm",placeholder:"Confirm your password"})})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("input",{id:"agreeToTerms",name:"agreeToTerms",type:"checkbox",required:!0,checked:e.agreeToTerms,onChange:r,className:"h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded"}),(0,l.jsxs)("label",{htmlFor:"agreeToTerms",className:"ml-2 block text-sm text-gray-900",children:["I agree to the"," ",(0,l.jsx)("a",{href:"#",className:"text-purple hover:text-darkpurple",children:"Terms of Service"})," ","and"," ",(0,l.jsx)("a",{href:"#",className:"text-purple hover:text-darkpurple",children:"Privacy Policy"})]})]}),(0,l.jsx)("div",{children:(0,l.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple transition-all",children:"Create account"})})]}),(0,l.jsxs)("div",{className:"mt-6",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,l.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,l.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,l.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or sign up with"})})]}),(0,l.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,l.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[(0,l.jsxs)("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",children:[(0,l.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,l.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),(0,l.jsx)("span",{className:"ml-2",children:"Google"})]}),(0,l.jsxs)("button",{className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[(0,l.jsx)("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),(0,l.jsx)("span",{className:"ml-2",children:"LinkedIn"})]})]})]}),(0,l.jsx)("div",{className:"mt-6",children:(0,l.jsx)("div",{className:"bg-purple/5 border border-purple/20 p-4 rounded-lg",children:(0,l.jsxs)("div",{className:"flex",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("svg",{className:"h-5 w-5 text-purple",fill:"currentColor",viewBox:"0 0 20 20",children:(0,l.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,l.jsxs)("div",{className:"ml-3",children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-purple",children:"30-day free trial"}),(0,l.jsx)("div",{className:"mt-1 text-sm text-gray-600",children:(0,l.jsx)("p",{children:"No credit card required. Full access to all features."})})]})]})})})]}),(0,l.jsx)("div",{className:"mt-8 text-center",children:(0,l.jsxs)("p",{className:"text-sm text-gray-600",children:["Questions about getting started?"," ",(0,l.jsx)(o(),{href:"/contact",className:"font-medium text-purple hover:text-darkpurple",children:"Contact our team"})]})})]})]})}}},e=>{e.O(0,[874,441,964,358],()=>e(e.s=6467)),_N_E=e.O()}]);