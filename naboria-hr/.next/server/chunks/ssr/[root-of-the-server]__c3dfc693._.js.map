{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/it/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function ITManagement() {\n  const [activeTab, setActiveTab] = useState('overview');\n\n  const devices = [\n    {\n      id: 1,\n      name: 'MacBook Pro 16\"',\n      type: 'Laptop',\n      assignedTo: '<PERSON>',\n      status: 'Active',\n      serialNumber: 'MBP2023001',\n      purchaseDate: '2023-01-15',\n      warranty: 'Active until 2026'\n    },\n    {\n      id: 2,\n      name: 'iPhone 14 Pro',\n      type: 'Mobile',\n      assignedTo: '<PERSON>',\n      status: 'Active',\n      serialNumber: 'IP14P2023002',\n      purchaseDate: '2023-03-20',\n      warranty: 'Active until 2025'\n    },\n    {\n      id: 3,\n      name: 'Dell Monitor 27\"',\n      type: 'Monitor',\n      assignedTo: '<PERSON>',\n      status: 'Active',\n      serialNumber: 'DM27-2023003',\n      purchaseDate: '2023-06-10',\n      warranty: 'Active until 2026'\n    },\n    {\n      id: 4,\n      name: 'Surface Laptop',\n      type: 'Laptop',\n      assignedTo: 'Unassigned',\n      status: 'Available',\n      serialNumber: '*********',\n      purchaseDate: '2023-08-05',\n      warranty: 'Active until 2026'\n    }\n  ];\n\n  const softwareLicenses = [\n    { name: 'Microsoft 365', users: 247, totalLicenses: 250, cost: '$12,350/month', renewal: '2024-12-31' },\n    { name: 'Adobe Creative Suite', users: 45, totalLicenses: 50, cost: '$2,250/month', renewal: '2024-06-30' },\n    { name: 'Slack Pro', users: 247, totalLicenses: 250, cost: '$1,975/month', renewal: '2024-09-15' },\n    { name: 'Zoom Pro', users: 150, totalLicenses: 200, cost: '$3,000/month', renewal: '2024-11-20' }\n  ];\n\n  const supportTickets = [\n    { id: 1, title: 'Laptop not connecting to WiFi', user: 'David Kim', priority: 'High', status: 'Open', created: '2024-01-15' },\n    { id: 2, title: 'Software installation request', user: 'Lisa Wang', priority: 'Medium', status: 'In Progress', created: '2024-01-14' },\n    { id: 3, title: 'Email sync issues', user: 'John Smith', priority: 'Low', status: 'Resolved', created: '2024-01-13' }\n  ];\n\n  const securityAlerts = [\n    { id: 1, type: 'Failed Login Attempts', severity: 'Medium', count: 5, user: '<EMAIL>' },\n    { id: 2, type: 'Suspicious Download', severity: 'High', count: 1, user: '<EMAIL>' },\n    { id: 3, type: 'VPN Connection from New Location', severity: 'Low', count: 2, user: '<EMAIL>' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">IT Management</h1>\n                <p className=\"text-gray-600 text-sm\">Manage devices, software, and IT infrastructure</p>\n              </div>\n            </div>\n            <button className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto\">\n              Add Device\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              <button\n                onClick={() => setActiveTab('devices')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'devices' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n                Devices\n              </button>\n              <button\n                onClick={() => setActiveTab('software')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'software' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n                Software\n              </button>\n              <button\n                onClick={() => setActiveTab('support')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'support' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\" />\n                </svg>\n                Support\n                <span className=\"ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full\">\n                  {supportTickets.filter(t => t.status !== 'Resolved').length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('security')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'security' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n                Security\n                <span className=\"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full\">\n                  {securityAlerts.filter(a => a.severity === 'High').length}\n                </span>\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Stats Cards */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Devices</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">324</p>\n                      <p className=\"text-sm text-green-600\">+12 this month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Software Licenses</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">892</p>\n                      <p className=\"text-sm text-blue-600\">89% utilized</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Open Tickets</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">8</p>\n                      <p className=\"text-sm text-orange-600\">2 high priority</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Security Score</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">94%</p>\n                      <p className=\"text-sm text-green-600\">Excellent</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Recent Activity */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Recent Device Activity</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-green/10 rounded-full flex items-center justify-center\">\n                        <svg className=\"h-4 w-4 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">New MacBook assigned</p>\n                        <p className=\"text-xs text-gray-500\">Emily Johnson • 2h ago</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-blue/10 rounded-full flex items-center justify-center\">\n                        <svg className=\"h-4 w-4 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">Software update completed</p>\n                        <p className=\"text-xs text-gray-500\">Microsoft 365 • 4h ago</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-orange/10 rounded-full flex items-center justify-center\">\n                        <svg className=\"h-4 w-4 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">License renewal required</p>\n                        <p className=\"text-xs text-gray-500\">Adobe Creative Suite • 6h ago</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">System Health</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Server Uptime</span>\n                      <span className=\"text-sm font-medium text-green-600\">99.9%</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Network Performance</span>\n                      <span className=\"text-sm font-medium text-green-600\">Excellent</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Backup Status</span>\n                      <span className=\"text-sm font-medium text-green-600\">Current</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-gray-600\">Security Updates</span>\n                      <span className=\"text-sm font-medium text-orange-600\">2 pending</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'devices' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Device Inventory</h3>\n                </div>\n                \n                {/* Mobile Cards */}\n                <div className=\"lg:hidden divide-y divide-gray-200\">\n                  {devices.map((device) => (\n                    <div key={device.id} className=\"p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{device.name}</h4>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          device.status === 'Active' \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-blue-100 text-blue-800'\n                        }`}>\n                          {device.status}\n                        </span>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div>\n                          <span className=\"text-gray-500\">Type:</span>\n                          <p className=\"font-medium\">{device.type}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Assigned To:</span>\n                          <p className=\"font-medium\">{device.assignedTo}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Serial:</span>\n                          <p className=\"font-medium\">{device.serialNumber}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Purchase:</span>\n                          <p className=\"font-medium\">{device.purchaseDate}</p>\n                        </div>\n                      </div>\n                      <div className=\"mt-3 flex space-x-2\">\n                        <button className=\"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium\">\n                          Edit\n                        </button>\n                        <button className=\"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium\">\n                          View\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Device\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Type\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Assigned To\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Serial Number\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {devices.map((device) => (\n                        <tr key={device.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {device.name}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {device.type}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {device.assignedTo}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              device.status === 'Active' \n                                ? 'bg-green-100 text-green-800' \n                                : 'bg-blue-100 text-blue-800'\n                            }`}>\n                              {device.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {device.serialNumber}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <button className=\"text-purple hover:text-darkpurple mr-3\">Edit</button>\n                            <button className=\"text-gray-400 hover:text-gray-600\">View</button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'software' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Software Licenses</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {softwareLicenses.map((license, index) => (\n                    <div key={index} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900\">{license.name}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {license.users} of {license.totalLicenses} licenses used • Renewal: {license.renewal}\n                          </p>\n                          <div className=\"mt-2 w-full bg-gray-200 rounded-full h-2\">\n                            <div \n                              className=\"bg-purple h-2 rounded-full\" \n                              style={{ width: `${(license.users / license.totalLicenses) * 100}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-lg font-semibold text-navy\">{license.cost}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'support' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Support Tickets</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {supportTickets.map((ticket) => (\n                    <div key={ticket.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{ticket.title}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {ticket.user} • {ticket.created} • \n                            <span className={`ml-1 px-2 py-1 text-xs rounded-full ${\n                              ticket.priority === 'High' ? 'bg-red-100 text-red-800' :\n                              ticket.priority === 'Medium' ? 'bg-orange-100 text-orange-800' :\n                              'bg-green-100 text-green-800'\n                            }`}>\n                              {ticket.priority}\n                            </span>\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            ticket.status === 'Open' ? 'bg-red-100 text-red-800' :\n                            ticket.status === 'In Progress' ? 'bg-orange-100 text-orange-800' :\n                            'bg-green-100 text-green-800'\n                          }`}>\n                            {ticket.status}\n                          </span>\n                          <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                            View\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'security' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Security Alerts</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {securityAlerts.map((alert) => (\n                    <div key={alert.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{alert.type}</h4>\n                          <p className=\"text-sm text-gray-500\">\n                            {alert.user} • {alert.count} occurrence{alert.count > 1 ? 's' : ''}\n                          </p>\n                        </div>\n                        <div className=\"flex items-center space-x-3\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            alert.severity === 'High' ? 'bg-red-100 text-red-800' :\n                            alert.severity === 'Medium' ? 'bg-orange-100 text-orange-800' :\n                            'bg-green-100 text-green-800'\n                          }`}>\n                            {alert.severity}\n                          </span>\n                          <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                            Investigate\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,UAAU;QACd;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,cAAc;YACd,cAAc;YACd,UAAU;QACZ;KACD;IAED,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAiB,OAAO;YAAK,eAAe;YAAK,MAAM;YAAiB,SAAS;QAAa;QACtG;YAAE,MAAM;YAAwB,OAAO;YAAI,eAAe;YAAI,MAAM;YAAgB,SAAS;QAAa;QAC1G;YAAE,MAAM;YAAa,OAAO;YAAK,eAAe;YAAK,MAAM;YAAgB,SAAS;QAAa;QACjG;YAAE,MAAM;YAAY,OAAO;YAAK,eAAe;YAAK,MAAM;YAAgB,SAAS;QAAa;KACjG;IAED,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAG,OAAO;YAAiC,MAAM;YAAa,UAAU;YAAQ,QAAQ;YAAQ,SAAS;QAAa;QAC5H;YAAE,IAAI;YAAG,OAAO;YAAiC,MAAM;YAAa,UAAU;YAAU,QAAQ;YAAe,SAAS;QAAa;QACrI;YAAE,IAAI;YAAG,OAAO;YAAqB,MAAM;YAAc,UAAU;YAAO,QAAQ;YAAY,SAAS;QAAa;KACrH;IAED,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAG,MAAM;YAAyB,UAAU;YAAU,OAAO;YAAG,MAAM;QAAyB;QACrG;YAAE,IAAI;YAAG,MAAM;YAAuB,UAAU;YAAQ,OAAO;YAAG,MAAM;QAAwB;QAChG;YAAE,IAAI;YAAG,MAAM;YAAoC,UAAU;YAAO,OAAO;YAAG,MAAM;QAAsB;KAC3G;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAO,WAAU;0CAA+G;;;;;;;;;;;;;;;;;;;;;;0BAOvI,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,YACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,YACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,8OAAC;gDAAK,WAAU;0DACb,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;kDAG/D,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,8OAAC;gDAAK,WAAU;0DACb,eAAe,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnE,8OAAC;wBAAK,WAAU;;4BACb,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC5E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAA0B;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC9E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC1E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAAyB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC7E,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAGzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAAwB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC5E,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAGzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;4EAA0B,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC9E,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAqC;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAqC;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAqC;;;;;;;;;;;;0EAEvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,8OAAC;wEAAK,WAAU;kFAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQjE,cAAc,2BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oDAAoB,WAAU;;sEAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6B,OAAO,IAAI;;;;;;8EACtD,8OAAC;oEAAK,WAAW,CAAC,+BAA+B,EAC/C,OAAO,MAAM,KAAK,WACd,gCACA,6BACJ;8EACC,OAAO,MAAM;;;;;;;;;;;;sEAGlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,OAAO,IAAI;;;;;;;;;;;;8EAEzC,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,OAAO,UAAU;;;;;;;;;;;;8EAE/C,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,OAAO,YAAY;;;;;;;;;;;;8EAEjD,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,OAAO,YAAY;;;;;;;;;;;;;;;;;;sEAGnD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAO,WAAU;8EAAwE;;;;;;8EAG1F,8OAAC;oEAAO,WAAU;8EAAyE;;;;;;;;;;;;;mDAjCrF,OAAO,EAAE;;;;;;;;;;sDA0CvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAM,WAAU;kEACf,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;;;;;;;;;;;;kEAKnG,8OAAC;wDAAM,WAAU;kEACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gEAAmB,WAAU;;kFAC5B,8OAAC;wEAAG,WAAU;kFACX,OAAO,IAAI;;;;;;kFAEd,8OAAC;wEAAG,WAAU;kFACX,OAAO,IAAI;;;;;;kFAEd,8OAAC;wEAAG,WAAU;kFACX,OAAO,UAAU;;;;;;kFAEpB,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAK,WAAW,CAAC,+BAA+B,EAC/C,OAAO,MAAM,KAAK,WACd,gCACA,6BACJ;sFACC,OAAO,MAAM;;;;;;;;;;;kFAGlB,8OAAC;wEAAG,WAAU;kFACX,OAAO,YAAY;;;;;;kFAEtB,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAO,WAAU;0FAAyC;;;;;;0FAC3D,8OAAC;gFAAO,WAAU;0FAAoC;;;;;;;;;;;;;+DAxBjD,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAmC/B,cAAc,4BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;oDAAgB,WAAU;8DACzB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACvD,8OAAC;wEAAE,WAAU;;4EACV,QAAQ,KAAK;4EAAC;4EAAK,QAAQ,aAAa;4EAAC;4EAA2B,QAAQ,OAAO;;;;;;;kFAEtF,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EACC,WAAU;4EACV,OAAO;gFAAE,OAAO,GAAG,AAAC,QAAQ,KAAK,GAAG,QAAQ,aAAa,GAAI,IAAI,CAAC,CAAC;4EAAC;;;;;;;;;;;;;;;;;0EAI1E,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;8EAAmC,QAAQ,IAAI;;;;;;;;;;;;;;;;;mDAfxD;;;;;;;;;;;;;;;;;;;;;4BAyBnB,cAAc,2BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;oDAAoB,WAAU;8DAC7B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,OAAO,KAAK;;;;;;kFACvD,8OAAC;wEAAE,WAAU;;4EACV,OAAO,IAAI;4EAAC;4EAAI,OAAO,OAAO;4EAAC;0FAChC,8OAAC;gFAAK,WAAW,CAAC,oCAAoC,EACpD,OAAO,QAAQ,KAAK,SAAS,4BAC7B,OAAO,QAAQ,KAAK,WAAW,kCAC/B,+BACA;0FACC,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0EAItB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,OAAO,MAAM,KAAK,SAAS,4BAC3B,OAAO,MAAM,KAAK,gBAAgB,kCAClC,+BACA;kFACC,OAAO,MAAM;;;;;;kFAEhB,8OAAC;wEAAO,WAAU;kFAAwD;;;;;;;;;;;;;;;;;;mDAvBtE,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;4BAmC5B,cAAc,4BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oDAAmB,WAAU;8DAC5B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,MAAM,IAAI;;;;;;kFACrD,8OAAC;wEAAE,WAAU;;4EACV,MAAM,IAAI;4EAAC;4EAAI,MAAM,KAAK;4EAAC;4EAAY,MAAM,KAAK,GAAG,IAAI,MAAM;;;;;;;;;;;;;0EAGpE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,MAAM,QAAQ,KAAK,SAAS,4BAC5B,MAAM,QAAQ,KAAK,WAAW,kCAC9B,+BACA;kFACC,MAAM,QAAQ;;;;;;kFAEjB,8OAAC;wEAAO,WAAU;kFAAwD;;;;;;;;;;;;;;;;;;mDAhBtE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BtC", "debugId": null}}]}