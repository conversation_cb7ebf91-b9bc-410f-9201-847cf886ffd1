{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Dashboard() {\n  const [activeModule, setActiveModule] = useState('overview');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Dashboard Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-6 py-4\">\n          <div className=\"flex justify-between items-center\">\n            <div className=\"flex items-center\">\n              <svg className=\"h-8 w-8 text-purple mr-3\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n                <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n              </svg>\n              <h1 className=\"text-2xl font-bold text-navy\">Naboria Dashboard</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <button className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z\" />\n                </svg>\n              </button>\n              <button className=\"p-2 text-gray-400 hover:text-gray-600 transition-colors\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 17h5l-5 5v-5z\" />\n                </svg>\n              </button>\n              <div className=\"flex items-center space-x-3\">\n                <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Ccircle cx='16' cy='16' r='16' fill='%238B5CF6'/%3E%3Cpath d='M16 10C14.3431 10 13 11.3431 13 13C13 14.6569 14.3431 16 16 16C17.6569 16 19 14.6569 19 13C19 11.3431 17.6569 10 16 10ZM12 21C12 18.7909 13.7909 17 16 17C18.2091 17 20 18.7909 20 21V22H12V21Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-8 h-8 rounded-full\" />\n                <div>\n                  <div className=\"text-sm font-medium text-gray-900\">Sarah Chen</div>\n                  <div className=\"text-xs text-gray-500\">Admin</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveModule('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeModule === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              \n              <Link\n                href=\"/dashboard/hr\"\n                className=\"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n                HR Management\n              </Link>\n              \n              <Link\n                href=\"/dashboard/payroll\"\n                className=\"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Payroll\n              </Link>\n              \n              <Link\n                href=\"/dashboard/it\"\n                className=\"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n                IT Management\n              </Link>\n              \n              <Link\n                href=\"/dashboard/finance\"\n                className=\"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n                Finance\n              </Link>\n              \n              <Link\n                href=\"/dashboard/automation\"\n                className=\"w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50\"\n              >\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                </svg>\n                Automation\n              </Link>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeModule === 'overview' && (\n            <div>\n              <div className=\"mb-8\">\n                <h2 className=\"text-3xl font-bold text-navy mb-2\">Welcome back, Sarah!</h2>\n                <p className=\"text-gray-600\">Here's what's happening with your organization today.</p>\n              </div>\n              \n              {/* Stats Grid */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Total Employees</p>\n                      <p className=\"text-3xl font-bold text-navy\">247</p>\n                      <p className=\"text-sm text-green-600\">+12 this month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Pending Approvals</p>\n                      <p className=\"text-3xl font-bold text-navy\">8</p>\n                      <p className=\"text-sm text-orange-600\">Requires attention</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Onboarding Progress</p>\n                      <p className=\"text-3xl font-bold text-navy\">94%</p>\n                      <p className=\"text-sm text-green-600\">Above target</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Monthly Payroll</p>\n                      <p className=\"text-3xl font-bold text-navy\">$1.2M</p>\n                      <p className=\"text-sm text-blue-600\">Processing</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Recent Activity */}\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Recent Activity</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-green/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">New employee onboarded</p>\n                        <p className=\"text-xs text-gray-500\">Emily Johnson joined the Engineering team</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">2h ago</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-blue/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">Payroll processed</p>\n                        <p className=\"text-xs text-gray-500\">December payroll completed successfully</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">4h ago</span>\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-8 h-8 bg-purple/10 rounded-full flex items-center justify-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                        </svg>\n                      </div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium text-gray-900\">IT equipment provisioned</p>\n                        <p className=\"text-xs text-gray-500\">MacBook Pro assigned to Michael Chen</p>\n                      </div>\n                      <span className=\"text-xs text-gray-400\">6h ago</span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm border border-gray-100\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Quick Actions</h3>\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <Link href=\"/dashboard/hr\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">HR Management</p>\n                      <p className=\"text-xs text-gray-500\">Manage employees & HR</p>\n                    </Link>\n\n                    <Link href=\"/dashboard/payroll\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-teal\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Payroll</p>\n                      <p className=\"text-xs text-gray-500\">Process monthly payroll</p>\n                    </Link>\n\n                    <Link href=\"/dashboard/it\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-navy/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-navy\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">IT Management</p>\n                      <p className=\"text-xs text-gray-500\">Devices & software</p>\n                    </Link>\n\n                    <Link href=\"/dashboard/finance\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Finance</p>\n                      <p className=\"text-xs text-gray-500\">Expenses & budgets</p>\n                    </Link>\n\n                    <Link href=\"/dashboard/automation\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">Automation</p>\n                      <p className=\"text-xs text-gray-500\">Workflows & rules</p>\n                    </Link>\n\n                    <Link href=\"/api-docs\" className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                      <div className=\"w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                        </svg>\n                      </div>\n                      <p className=\"font-medium text-gray-900\">API Docs</p>\n                      <p className=\"text-xs text-gray-500\">Developer resources</p>\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          {activeModule !== 'overview' && (\n            <div className=\"text-center py-16\">\n              <div className=\"w-16 h-16 bg-purple/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl font-semibold text-navy mb-2\">\n                {activeModule.charAt(0).toUpperCase() + activeModule.slice(1)} Module\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                This module is under development. Full functionality coming soon!\n              </p>\n              <button className=\"bg-purple hover:bg-darkpurple text-white px-6 py-3 rounded-lg font-medium transition-colors\">\n                Learn More\n              </button>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA2B,SAAQ;wCAAY,MAAK;wCAAO,OAAM;;0DAC9E,8OAAC;gDAAK,GAAE;gDAAsC,MAAK;gDAAe,aAAY;;;;;;0DAC9E,8OAAC;gDAAK,GAAE;gDAA8C,MAAK;;;;;;;;;;;;kDAE7D,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,KAAI;gDAAwY,KAAI;gDAAO,WAAU;;;;;;0DACta,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,0EAA0E,EACpF,iBAAiB,aACb,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACtG,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,8OAAC;wBAAK,WAAU;;4BACb,iBAAiB,4BAChB,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAI/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7G,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAA0B;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjH,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAChH,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA+B;;;;;;8EAC5C,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,OAAM;gEAA6B,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC/G,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAyB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAChH,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAG1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAwB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC/G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;0EAG1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAsB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC7G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAE,WAAU;0FAAoC;;;;;;0FACjD,8OAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;kFAEvC,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;0DAK9C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAgB,WAAU;;kFACnC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAsB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC7G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAqB,WAAU;;kFACxC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAoB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC3G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAgB,WAAU;;kFACnC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAoB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC3G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAqB,WAAU;;kFACxC,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAA0B,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACjH,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAwB,WAAU;;kFAC3C,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAyB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAChH,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;0EAGvC,8OAAC,4JAAA,CAAA,UAAI;gEAAC,MAAK;gEAAY,WAAU;;kFAC/B,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAwB,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC/G,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;kFAGzE,8OAAC;wEAAE,WAAU;kFAA4B;;;;;;kFACzC,8OAAC;wEAAE,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQhD,iBAAiB,4BAChB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAsB,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC7G,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;;4CACX,aAAa,MAAM,CAAC,GAAG,WAAW,KAAK,aAAa,KAAK,CAAC;4CAAG;;;;;;;kDAEhE,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9H", "debugId": null}}]}