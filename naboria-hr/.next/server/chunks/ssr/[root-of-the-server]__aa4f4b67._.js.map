{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/payroll/generate-report/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function GeneratePayrollReport() {\n  const [reportConfig, setReportConfig] = useState({\n    reportType: 'payroll-summary',\n    dateRange: 'current-month',\n    startDate: '',\n    endDate: '',\n    department: 'all',\n    format: 'pdf',\n    includeDetails: true,\n    includeTaxBreakdown: true,\n    includeDeductions: true,\n    groupBy: 'department'\n  });\n\n  const [isGenerating, setIsGenerating] = useState(false);\n\n  const reportTypes = [\n    { id: 'payroll-summary', name: 'Payroll Summary Report', description: 'Overview of payroll costs and employee payments' },\n    { id: 'tax-report', name: 'Tax Liability Report', description: 'Federal, state, and local tax obligations' },\n    { id: 'employee-earnings', name: 'Employee Earnings Report', description: 'Detailed earnings breakdown by employee' },\n    { id: 'deductions-report', name: 'Deductions Report', description: 'All payroll deductions and benefits' },\n    { id: 'overtime-report', name: 'Overtime Report', description: 'Overtime hours and payments analysis' },\n    { id: 'year-end-report', name: 'Year-End Report', description: 'Annual payroll summary for tax filing' },\n    { id: 'cost-center', name: 'Cost Center Report', description: 'Payroll costs by department/cost center' },\n    { id: 'compliance-report', name: 'Compliance Report', description: 'Regulatory compliance and audit trail' }\n  ];\n\n  const dateRanges = [\n    { id: 'current-month', name: 'Current Month' },\n    { id: 'last-month', name: 'Last Month' },\n    { id: 'current-quarter', name: 'Current Quarter' },\n    { id: 'last-quarter', name: 'Last Quarter' },\n    { id: 'current-year', name: 'Current Year' },\n    { id: 'last-year', name: 'Last Year' },\n    { id: 'custom', name: 'Custom Date Range' }\n  ];\n\n  const departments = [\n    { id: 'all', name: 'All Departments' },\n    { id: 'engineering', name: 'Engineering' },\n    { id: 'marketing', name: 'Marketing' },\n    { id: 'sales', name: 'Sales' },\n    { id: 'finance', name: 'Finance' },\n    { id: 'hr', name: 'Human Resources' },\n    { id: 'operations', name: 'Operations' }\n  ];\n\n  const handleGenerateReport = async () => {\n    setIsGenerating(true);\n    \n    // Simulate report generation\n    await new Promise(resolve => setTimeout(resolve, 3000));\n    \n    setIsGenerating(false);\n    \n    // In a real app, this would download the report\n    const reportName = reportTypes.find(r => r.id === reportConfig.reportType)?.name || 'Report';\n    alert(`${reportName} generated successfully! Download will start automatically.`);\n  };\n\n  const getReportPreview = () => {\n    const reportType = reportTypes.find(r => r.id === reportConfig.reportType);\n    const dateRange = dateRanges.find(r => r.id === reportConfig.dateRange);\n    const department = departments.find(d => d.id === reportConfig.department);\n    \n    return {\n      title: reportType?.name || 'Report',\n      description: reportType?.description || '',\n      period: dateRange?.name || 'Custom Range',\n      scope: department?.name || 'All Departments',\n      format: reportConfig.format.toUpperCase()\n    };\n  };\n\n  const preview = getReportPreview();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/payroll\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Generate Payroll Report</h1>\n                <p className=\"text-gray-600 text-sm\">Create detailed payroll and tax reports</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-6xl\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Report Configuration */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Report Type Selection */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Report Type</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {reportTypes.map((type) => (\n                  <div\n                    key={type.id}\n                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      reportConfig.reportType === type.id\n                        ? 'border-purple bg-purple/5'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setReportConfig({...reportConfig, reportType: type.id})}\n                  >\n                    <h3 className=\"font-medium text-gray-900 mb-1\">{type.name}</h3>\n                    <p className=\"text-sm text-gray-500\">{type.description}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Date Range */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Date Range</h2>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3 mb-6\">\n                {dateRanges.map((range) => (\n                  <button\n                    key={range.id}\n                    onClick={() => setReportConfig({...reportConfig, dateRange: range.id})}\n                    className={`p-3 text-sm font-medium rounded-lg transition-colors ${\n                      reportConfig.dateRange === range.id\n                        ? 'bg-purple text-white'\n                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n                    }`}\n                  >\n                    {range.name}\n                  </button>\n                ))}\n              </div>\n\n              {reportConfig.dateRange === 'custom' && (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Start Date</label>\n                    <input\n                      type=\"date\"\n                      value={reportConfig.startDate}\n                      onChange={(e) => setReportConfig({...reportConfig, startDate: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">End Date</label>\n                    <input\n                      type=\"date\"\n                      value={reportConfig.endDate}\n                      onChange={(e) => setReportConfig({...reportConfig, endDate: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Filters */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Filters & Options</h2>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Department</label>\n                  <select\n                    value={reportConfig.department}\n                    onChange={(e) => setReportConfig({...reportConfig, department: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    {departments.map((dept) => (\n                      <option key={dept.id} value={dept.id}>{dept.name}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Group By</label>\n                  <select\n                    value={reportConfig.groupBy}\n                    onChange={(e) => setReportConfig({...reportConfig, groupBy: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"department\">Department</option>\n                    <option value=\"employee\">Employee</option>\n                    <option value=\"pay-period\">Pay Period</option>\n                    <option value=\"cost-center\">Cost Center</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Format</label>\n                  <select\n                    value={reportConfig.format}\n                    onChange={(e) => setReportConfig({...reportConfig, format: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"pdf\">PDF</option>\n                    <option value=\"excel\">Excel (XLSX)</option>\n                    <option value=\"csv\">CSV</option>\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-6 space-y-3\">\n                <h3 className=\"text-sm font-medium text-gray-700\">Include in Report:</h3>\n                \n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={reportConfig.includeDetails}\n                    onChange={(e) => setReportConfig({...reportConfig, includeDetails: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Detailed employee breakdown</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={reportConfig.includeTaxBreakdown}\n                    onChange={(e) => setReportConfig({...reportConfig, includeTaxBreakdown: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Tax breakdown and liabilities</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={reportConfig.includeDeductions}\n                    onChange={(e) => setReportConfig({...reportConfig, includeDeductions: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Deductions and benefits</span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Report Preview & Actions */}\n          <div className=\"space-y-6\">\n            {/* Preview */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Report Preview</h2>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Report Type</h3>\n                  <p className=\"text-lg font-medium text-gray-900\">{preview.title}</p>\n                  <p className=\"text-sm text-gray-600\">{preview.description}</p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Period</h3>\n                  <p className=\"text-gray-900\">{preview.period}</p>\n                  {reportConfig.dateRange === 'custom' && reportConfig.startDate && reportConfig.endDate && (\n                    <p className=\"text-sm text-gray-600\">\n                      {reportConfig.startDate} to {reportConfig.endDate}\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Scope</h3>\n                  <p className=\"text-gray-900\">{preview.scope}</p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Format</h3>\n                  <p className=\"text-gray-900\">{preview.format}</p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Options</h3>\n                  <div className=\"space-y-1\">\n                    {reportConfig.includeDetails && (\n                      <p className=\"text-sm text-gray-600\">✓ Detailed breakdown</p>\n                    )}\n                    {reportConfig.includeTaxBreakdown && (\n                      <p className=\"text-sm text-gray-600\">✓ Tax breakdown</p>\n                    )}\n                    {reportConfig.includeDeductions && (\n                      <p className=\"text-sm text-gray-600\">✓ Deductions</p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Report Statistics</h2>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Employees</span>\n                  <span className=\"font-medium\">156</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Pay Periods</span>\n                  <span className=\"font-medium\">4</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Total Gross Pay</span>\n                  <span className=\"font-medium text-green-600\">$1,247,850</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Total Taxes</span>\n                  <span className=\"font-medium text-red-600\">$374,355</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Net Payroll</span>\n                  <span className=\"font-medium text-blue-600\">$873,495</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Actions */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Actions</h2>\n              \n              <div className=\"space-y-3\">\n                <button\n                  onClick={handleGenerateReport}\n                  disabled={isGenerating}\n                  className=\"w-full bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center\"\n                >\n                  {isGenerating ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Generating Report...\n                    </>\n                  ) : (\n                    <>\n                      <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                      Generate Report\n                    </>\n                  )}\n                </button>\n\n                <button className=\"w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors\">\n                  Save Configuration\n                </button>\n\n                <button className=\"w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors\">\n                  Schedule Report\n                </button>\n              </div>\n            </div>\n\n            {/* Recent Reports */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Recent Reports</h2>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Payroll Summary</p>\n                    <p className=\"text-xs text-gray-500\">December 2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Tax Report</p>\n                    <p className=\"text-xs text-gray-500\">Q4 2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Year-End Report</p>\n                    <p className=\"text-xs text-gray-500\">2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,gBAAgB;QAChB,qBAAqB;QACrB,mBAAmB;QACnB,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,cAAc;QAClB;YAAE,IAAI;YAAmB,MAAM;YAA0B,aAAa;QAAkD;QACxH;YAAE,IAAI;YAAc,MAAM;YAAwB,aAAa;QAA4C;QAC3G;YAAE,IAAI;YAAqB,MAAM;YAA4B,aAAa;QAA0C;QACpH;YAAE,IAAI;YAAqB,MAAM;YAAqB,aAAa;QAAsC;QACzG;YAAE,IAAI;YAAmB,MAAM;YAAmB,aAAa;QAAuC;QACtG;YAAE,IAAI;YAAmB,MAAM;YAAmB,aAAa;QAAwC;QACvG;YAAE,IAAI;YAAe,MAAM;YAAsB,aAAa;QAA0C;QACxG;YAAE,IAAI;YAAqB,MAAM;YAAqB,aAAa;QAAwC;KAC5G;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAiB,MAAM;QAAgB;QAC7C;YAAE,IAAI;YAAc,MAAM;QAAa;QACvC;YAAE,IAAI;YAAmB,MAAM;QAAkB;QACjD;YAAE,IAAI;YAAgB,MAAM;QAAe;QAC3C;YAAE,IAAI;YAAgB,MAAM;QAAe;QAC3C;YAAE,IAAI;YAAa,MAAM;QAAY;QACrC;YAAE,IAAI;YAAU,MAAM;QAAoB;KAC3C;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAO,MAAM;QAAkB;QACrC;YAAE,IAAI;YAAe,MAAM;QAAc;QACzC;YAAE,IAAI;YAAa,MAAM;QAAY;QACrC;YAAE,IAAI;YAAS,MAAM;QAAQ;QAC7B;YAAE,IAAI;YAAW,MAAM;QAAU;QACjC;YAAE,IAAI;YAAM,MAAM;QAAkB;QACpC;YAAE,IAAI;YAAc,MAAM;QAAa;KACxC;IAED,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAEhB,gDAAgD;QAChD,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,UAAU,GAAG,QAAQ;QACpF,MAAM,GAAG,WAAW,2DAA2D,CAAC;IAClF;IAEA,MAAM,mBAAmB;QACvB,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,UAAU;QACzE,MAAM,YAAY,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,SAAS;QACtE,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,UAAU;QAEzE,OAAO;YACL,OAAO,YAAY,QAAQ;YAC3B,aAAa,YAAY,eAAe;YACxC,QAAQ,WAAW,QAAQ;YAC3B,OAAO,YAAY,QAAQ;YAC3B,QAAQ,aAAa,MAAM,CAAC,WAAW;QACzC;IACF;IAEA,MAAM,UAAU;IAEhB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAqB,WAAU;8CACxC,cAAA,8OAAC;wCAAI,WAAU;wCAA2B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oDAEC,WAAW,CAAC,yDAAyD,EACnE,aAAa,UAAU,KAAK,KAAK,EAAE,GAC/B,8BACA,yCACJ;oDACF,SAAS,IAAM,gBAAgB;4DAAC,GAAG,YAAY;4DAAE,YAAY,KAAK,EAAE;wDAAA;;sEAEpE,8OAAC;4DAAG,WAAU;sEAAkC,KAAK,IAAI;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,WAAW;;;;;;;mDATjD,KAAK,EAAE;;;;;;;;;;;;;;;;8CAgBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;oDAEC,SAAS,IAAM,gBAAgB;4DAAC,GAAG,YAAY;4DAAE,WAAW,MAAM,EAAE;wDAAA;oDACpE,WAAW,CAAC,qDAAqD,EAC/D,aAAa,SAAS,KAAK,MAAM,EAAE,GAC/B,yBACA,+CACJ;8DAED,MAAM,IAAI;mDARN,MAAM,EAAE;;;;;;;;;;wCAalB,aAAa,SAAS,KAAK,0BAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,MAAK;4DACL,OAAO,aAAa,SAAS;4DAC7B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC5E,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,MAAK;4DACL,OAAO,aAAa,OAAO;4DAC3B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC1E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAQpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,UAAU;4DAC9B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC7E,WAAU;sEAET,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oEAAqB,OAAO,KAAK,EAAE;8EAAG,KAAK,IAAI;mEAAnC,KAAK,EAAE;;;;;;;;;;;;;;;;8DAK1B,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,OAAO;4DAC3B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC1E,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAW;;;;;;8EACzB,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAc;;;;;;;;;;;;;;;;;;8DAIhC,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,MAAM;4DAC1B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACzE,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;sDAK1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAElD,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,cAAc;4DACpC,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;gEAAA;4DACnF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAG1C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,mBAAmB;4DACzC,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,qBAAqB,EAAE,MAAM,CAAC,OAAO;gEAAA;4DACxF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAG1C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,iBAAiB;4DACvC,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;gEAAA;4DACtF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAqC,QAAQ,KAAK;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;sEAAyB,QAAQ,WAAW;;;;;;;;;;;;8DAG3D,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAiB,QAAQ,MAAM;;;;;;wDAC3C,aAAa,SAAS,KAAK,YAAY,aAAa,SAAS,IAAI,aAAa,OAAO,kBACpF,8OAAC;4DAAE,WAAU;;gEACV,aAAa,SAAS;gEAAC;gEAAK,aAAa,OAAO;;;;;;;;;;;;;8DAKvD,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAiB,QAAQ,KAAK;;;;;;;;;;;;8DAG7C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAiB,QAAQ,MAAM;;;;;;;;;;;;8DAG9C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;;gEACZ,aAAa,cAAc,kBAC1B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;gEAEtC,aAAa,mBAAmB,kBAC/B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;gEAEtC,aAAa,iBAAiB,kBAC7B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEhC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;8CAMlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;4DAC/C;;qFAIR;;0EACE,8OAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACnE,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DACjE;;;;;;;;8DAMZ,8OAAC;oDAAO,WAAU;8DAAkH;;;;;;8DAIpI,8OAAC;oDAAO,WAAU;8DAAkH;;;;;;;;;;;;;;;;;;8CAOxI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;8DAK5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;8DAK5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5F", "debugId": null}}]}