{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/finance/add-expense/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function AddExpense() {\n  const [expenseData, setExpenseData] = useState({\n    // Basic Information\n    title: '',\n    description: '',\n    amount: '',\n    currency: 'USD',\n    date: '',\n    \n    // Categorization\n    category: '',\n    subcategory: '',\n    department: '',\n    project: '',\n    \n    // Details\n    vendor: '',\n    paymentMethod: '',\n    reference: '',\n    taxAmount: '',\n    \n    // Approval\n    approver: '',\n    businessJustification: '',\n    \n    // Receipts\n    receipts: [],\n    \n    // Status\n    status: 'Draft',\n    recurring: false,\n    recurringFrequency: '',\n    recurringEndDate: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [receiptFiles, setReceiptFiles] = useState<File[]>([]);\n\n  const categories = [\n    { id: 'travel', name: 'Travel & Transportation', subcategories: ['Flights', 'Hotels', 'Car Rental', 'Meals', 'Taxi/Uber', 'Parking'] },\n    { id: 'office', name: 'Office Supplies', subcategories: ['Stationery', 'Equipment', 'Furniture', 'Software', 'Maintenance'] },\n    { id: 'marketing', name: 'Marketing & Advertising', subcategories: ['Digital Ads', 'Print Materials', 'Events', 'Sponsorships', 'Content Creation'] },\n    { id: 'professional', name: 'Professional Services', subcategories: ['Legal', 'Consulting', 'Accounting', 'Training', 'Recruitment'] },\n    { id: 'utilities', name: 'Utilities & Communications', subcategories: ['Internet', 'Phone', 'Electricity', 'Water', 'Gas'] },\n    { id: 'entertainment', name: 'Entertainment & Meals', subcategories: ['Client Meals', 'Team Events', 'Conference Meals', 'Office Catering'] },\n    { id: 'technology', name: 'Technology', subcategories: ['Software Licenses', 'Hardware', 'Cloud Services', 'IT Support', 'Security'] },\n    { id: 'other', name: 'Other', subcategories: ['Miscellaneous', 'One-time', 'Emergency'] }\n  ];\n\n  const departments = [\n    'Engineering', 'Marketing', 'Sales', 'Finance', 'HR', 'Operations', 'IT', 'Legal'\n  ];\n\n  const projects = [\n    'Website Redesign', 'Mobile App Development', 'Q1 Marketing Campaign', 'Office Expansion', \n    'Security Upgrade', 'Training Program', 'Product Launch', 'General Operations'\n  ];\n\n  const paymentMethods = [\n    'Corporate Credit Card', 'Bank Transfer', 'Check', 'Cash', 'Personal (Reimbursement)', 'Petty Cash'\n  ];\n\n  const approvers = [\n    'Sarah Johnson - CFO', 'Michael Rodriguez - VP Marketing', 'Emily Davis - VP Engineering', \n    'David Kim - VP Sales', 'Lisa Wang - Finance Manager'\n  ];\n\n  const currencies = [\n    { code: 'USD', symbol: '$', name: 'US Dollar' },\n    { code: 'EUR', symbol: '€', name: 'Euro' },\n    { code: 'GBP', symbol: '£', name: 'British Pound' },\n    { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' }\n  ];\n\n  const selectedCategory = categories.find(cat => cat.id === expenseData.category);\n\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(e.target.files || []);\n    setReceiptFiles(prev => [...prev, ...files]);\n  };\n\n  const removeFile = (index: number) => {\n    setReceiptFiles(prev => prev.filter((_, i) => i !== index));\n  };\n\n  const calculateTotalAmount = () => {\n    const amount = parseFloat(expenseData.amount) || 0;\n    const tax = parseFloat(expenseData.taxAmount) || 0;\n    return amount + tax;\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    alert('Expense submitted successfully!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/finance\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Add New Expense</h1>\n                <p className=\"text-gray-600 text-sm\">Submit a new expense for approval and reimbursement</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-sm text-gray-600\">Total Amount</p>\n              <p className=\"text-2xl font-bold text-purple\">\n                {currencies.find(c => c.code === expenseData.currency)?.symbol}\n                {calculateTotalAmount().toFixed(2)}\n              </p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Basic Information */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Basic Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Expense Title *</label>\n                <input\n                  type=\"text\"\n                  value={expenseData.title}\n                  onChange={(e) => setExpenseData({...expenseData, title: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., Client dinner at Restaurant ABC\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Amount *</label>\n                <div className=\"relative\">\n                  <select\n                    value={expenseData.currency}\n                    onChange={(e) => setExpenseData({...expenseData, currency: e.target.value})}\n                    className=\"absolute left-0 top-0 h-full w-20 px-2 border border-gray-300 rounded-l-lg bg-gray-50 text-sm\"\n                  >\n                    {currencies.map(currency => (\n                      <option key={currency.code} value={currency.code}>{currency.symbol}</option>\n                    ))}\n                  </select>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={expenseData.amount}\n                    onChange={(e) => setExpenseData({...expenseData, amount: e.target.value})}\n                    className=\"w-full pl-20 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"0.00\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Date *</label>\n                <input\n                  type=\"date\"\n                  value={expenseData.date}\n                  onChange={(e) => setExpenseData({...expenseData, date: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Vendor/Merchant</label>\n                <input\n                  type=\"text\"\n                  value={expenseData.vendor}\n                  onChange={(e) => setExpenseData({...expenseData, vendor: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., Amazon, Starbucks, Delta Airlines\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Payment Method</label>\n                <select\n                  value={expenseData.paymentMethod}\n                  onChange={(e) => setExpenseData({...expenseData, paymentMethod: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Payment Method</option>\n                  {paymentMethods.map(method => (\n                    <option key={method} value={method}>{method}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description</label>\n                <textarea\n                  value={expenseData.description}\n                  onChange={(e) => setExpenseData({...expenseData, description: e.target.value})}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"Provide details about the expense...\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Categorization */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Categorization</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Category *</label>\n                <select\n                  value={expenseData.category}\n                  onChange={(e) => setExpenseData({...expenseData, category: e.target.value, subcategory: ''})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  required\n                >\n                  <option value=\"\">Select Category</option>\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>{category.name}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Subcategory</label>\n                <select\n                  value={expenseData.subcategory}\n                  onChange={(e) => setExpenseData({...expenseData, subcategory: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  disabled={!selectedCategory}\n                >\n                  <option value=\"\">Select Subcategory</option>\n                  {selectedCategory?.subcategories.map(sub => (\n                    <option key={sub} value={sub}>{sub}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Department</label>\n                <select\n                  value={expenseData.department}\n                  onChange={(e) => setExpenseData({...expenseData, department: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Department</option>\n                  {departments.map(dept => (\n                    <option key={dept} value={dept}>{dept}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Project</label>\n                <select\n                  value={expenseData.project}\n                  onChange={(e) => setExpenseData({...expenseData, project: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Project</option>\n                  {projects.map(project => (\n                    <option key={project} value={project}>{project}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Tax & Reference */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Additional Details</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Tax Amount</label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">\n                    {currencies.find(c => c.code === expenseData.currency)?.symbol}\n                  </span>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    value={expenseData.taxAmount}\n                    onChange={(e) => setExpenseData({...expenseData, taxAmount: e.target.value})}\n                    className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"0.00\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Reference Number</label>\n                <input\n                  type=\"text\"\n                  value={expenseData.reference}\n                  onChange={(e) => setExpenseData({...expenseData, reference: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"Invoice #, Receipt #, etc.\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Approver</label>\n                <select\n                  value={expenseData.approver}\n                  onChange={(e) => setExpenseData({...expenseData, approver: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Auto-assign based on amount</option>\n                  {approvers.map(approver => (\n                    <option key={approver} value={approver}>{approver}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div className=\"flex items-center\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={expenseData.recurring}\n                    onChange={(e) => setExpenseData({...expenseData, recurring: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm font-medium text-gray-700\">Recurring Expense</span>\n                </label>\n              </div>\n\n              {expenseData.recurring && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Frequency</label>\n                    <select\n                      value={expenseData.recurringFrequency}\n                      onChange={(e) => setExpenseData({...expenseData, recurringFrequency: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    >\n                      <option value=\"\">Select Frequency</option>\n                      <option value=\"weekly\">Weekly</option>\n                      <option value=\"monthly\">Monthly</option>\n                      <option value=\"quarterly\">Quarterly</option>\n                      <option value=\"annually\">Annually</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">End Date</label>\n                    <input\n                      type=\"date\"\n                      value={expenseData.recurringEndDate}\n                      onChange={(e) => setExpenseData({...expenseData, recurringEndDate: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                </>\n              )}\n\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Business Justification</label>\n                <textarea\n                  value={expenseData.businessJustification}\n                  onChange={(e) => setExpenseData({...expenseData, businessJustification: e.target.value})}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"Explain the business purpose of this expense...\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Receipt Upload */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Receipt Upload</h2>\n            \n            <div className=\"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center mb-4\">\n              <svg className=\"mx-auto h-12 w-12 text-gray-400\" stroke=\"currentColor\" fill=\"none\" viewBox=\"0 0 48 48\">\n                <path d=\"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\" />\n              </svg>\n              <div className=\"mt-4\">\n                <label htmlFor=\"receipt-upload\" className=\"cursor-pointer\">\n                  <span className=\"mt-2 block text-sm font-medium text-gray-900\">\n                    Upload Receipt(s)\n                  </span>\n                  <span className=\"mt-1 block text-sm text-gray-500\">\n                    PNG, JPG, PDF up to 10MB each\n                  </span>\n                </label>\n                <input\n                  id=\"receipt-upload\"\n                  name=\"receipt-upload\"\n                  type=\"file\"\n                  className=\"sr-only\"\n                  multiple\n                  accept=\".png,.jpg,.jpeg,.pdf\"\n                  onChange={handleFileUpload}\n                />\n              </div>\n            </div>\n\n            {receiptFiles.length > 0 && (\n              <div className=\"space-y-2\">\n                <h3 className=\"text-sm font-medium text-gray-700\">Uploaded Files:</h3>\n                {receiptFiles.map((file, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <svg className=\"h-5 w-5 text-gray-400 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                      <span className=\"text-sm text-gray-900\">{file.name}</span>\n                      <span className=\"text-xs text-gray-500 ml-2\">({(file.size / 1024 / 1024).toFixed(2)} MB)</span>\n                    </div>\n                    <button\n                      type=\"button\"\n                      onClick={() => removeFile(index)}\n                      className=\"text-red-500 hover:text-red-700\"\n                    >\n                      <svg className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                      </svg>\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Summary */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Expense Summary</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Expense Amount:</span>\n                  <span className=\"font-medium\">\n                    {currencies.find(c => c.code === expenseData.currency)?.symbol}\n                    {parseFloat(expenseData.amount || '0').toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Tax Amount:</span>\n                  <span className=\"font-medium\">\n                    {currencies.find(c => c.code === expenseData.currency)?.symbol}\n                    {parseFloat(expenseData.taxAmount || '0').toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between border-t pt-3\">\n                  <span className=\"text-lg font-medium text-gray-900\">Total Amount:</span>\n                  <span className=\"text-lg font-bold text-purple\">\n                    {currencies.find(c => c.code === expenseData.currency)?.symbol}\n                    {calculateTotalAmount().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Category:</span>\n                  <span className=\"ml-2 font-medium\">\n                    {categories.find(c => c.id === expenseData.category)?.name || 'Not selected'}\n                  </span>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Department:</span>\n                  <span className=\"ml-2 font-medium\">{expenseData.department || 'Not selected'}</span>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Payment Method:</span>\n                  <span className=\"ml-2 font-medium\">{expenseData.paymentMethod || 'Not selected'}</span>\n                </div>\n                <div className=\"text-sm\">\n                  <span className=\"text-gray-600\">Receipts:</span>\n                  <span className=\"ml-2 font-medium\">{receiptFiles.length} file(s)</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Submit Buttons */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n            <Link\n              href=\"/dashboard/finance\"\n              className=\"border border-gray-300 text-gray-600 hover:bg-gray-50 px-6 py-2 rounded-lg font-medium transition-colors\"\n            >\n              Cancel\n            </Link>\n            \n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                className=\"border border-purple text-purple hover:bg-purple hover:text-white px-6 py-2 rounded-lg font-medium transition-colors\"\n              >\n                Save as Draft\n              </button>\n              \n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Submitting...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    Submit Expense\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,oBAAoB;QACpB,OAAO;QACP,aAAa;QACb,QAAQ;QACR,UAAU;QACV,MAAM;QAEN,iBAAiB;QACjB,UAAU;QACV,aAAa;QACb,YAAY;QACZ,SAAS;QAET,UAAU;QACV,QAAQ;QACR,eAAe;QACf,WAAW;QACX,WAAW;QAEX,WAAW;QACX,UAAU;QACV,uBAAuB;QAEvB,WAAW;QACX,UAAU,EAAE;QAEZ,SAAS;QACT,QAAQ;QACR,WAAW;QACX,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,MAAM,aAAa;QACjB;YAAE,IAAI;YAAU,MAAM;YAA2B,eAAe;gBAAC;gBAAW;gBAAU;gBAAc;gBAAS;gBAAa;aAAU;QAAC;QACrI;YAAE,IAAI;YAAU,MAAM;YAAmB,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;gBAAY;aAAc;QAAC;QAC5H;YAAE,IAAI;YAAa,MAAM;YAA2B,eAAe;gBAAC;gBAAe;gBAAmB;gBAAU;gBAAgB;aAAmB;QAAC;QACpJ;YAAE,IAAI;YAAgB,MAAM;YAAyB,eAAe;gBAAC;gBAAS;gBAAc;gBAAc;gBAAY;aAAc;QAAC;QACrI;YAAE,IAAI;YAAa,MAAM;YAA8B,eAAe;gBAAC;gBAAY;gBAAS;gBAAe;gBAAS;aAAM;QAAC;QAC3H;YAAE,IAAI;YAAiB,MAAM;YAAyB,eAAe;gBAAC;gBAAgB;gBAAe;gBAAoB;aAAkB;QAAC;QAC5I;YAAE,IAAI;YAAc,MAAM;YAAc,eAAe;gBAAC;gBAAqB;gBAAY;gBAAkB;gBAAc;aAAW;QAAC;QACrI;YAAE,IAAI;YAAS,MAAM;YAAS,eAAe;gBAAC;gBAAiB;gBAAY;aAAY;QAAC;KACzF;IAED,MAAM,cAAc;QAClB;QAAe;QAAa;QAAS;QAAW;QAAM;QAAc;QAAM;KAC3E;IAED,MAAM,WAAW;QACf;QAAoB;QAA0B;QAAyB;QACvE;QAAoB;QAAoB;QAAkB;KAC3D;IAED,MAAM,iBAAiB;QACrB;QAAyB;QAAiB;QAAS;QAAQ;QAA4B;KACxF;IAED,MAAM,YAAY;QAChB;QAAuB;QAAoC;QAC3D;QAAwB;KACzB;IAED,MAAM,aAAa;QACjB;YAAE,MAAM;YAAO,QAAQ;YAAK,MAAM;QAAY;QAC9C;YAAE,MAAM;YAAO,QAAQ;YAAK,MAAM;QAAO;QACzC;YAAE,MAAM;YAAO,QAAQ;YAAK,MAAM;QAAgB;QAClD;YAAE,MAAM;YAAO,QAAQ;YAAM,MAAM;QAAkB;KACtD;IAED,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY,QAAQ;IAE/E,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;QAC7C,gBAAgB,CAAA,OAAQ;mBAAI;mBAAS;aAAM;IAC7C;IAEA,MAAM,aAAa,CAAC;QAClB,gBAAgB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACtD;IAEA,MAAM,uBAAuB;QAC3B,MAAM,SAAS,WAAW,YAAY,MAAM,KAAK;QACjD,MAAM,MAAM,WAAW,YAAY,SAAS,KAAK;QACjD,OAAO,SAAS;IAClB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAqB,WAAU;kDACxC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;;4CACV,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;4CACvD,uBAAuB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,KAAK;oDACxB,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACtE,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAO,YAAY,QAAQ;4DAC3B,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACzE,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oEAA2B,OAAO,SAAS,IAAI;8EAAG,SAAS,MAAM;mEAArD,SAAS,IAAI;;;;;;;;;;sEAG9B,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,YAAY,MAAM;4DACzB,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACvE,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,IAAI;oDACvB,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACrE,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,MAAM;oDACzB,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACvE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,aAAa;oDAChC,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC9E,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,eAAe,GAAG,CAAC,CAAA,uBAClB,8OAAC;gEAAoB,OAAO;0EAAS;+DAAxB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC5E,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,QAAQ;oDAC3B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAE,aAAa;wDAAE;oDAC1F,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;gEAAyB,OAAO,SAAS,EAAE;0EAAG,SAAS,IAAI;+DAA/C,SAAS,EAAE;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC5E,WAAU;oDACV,UAAU,CAAC;;sEAEX,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,kBAAkB,cAAc,IAAI,CAAA,oBACnC,8OAAC;gEAAiB,OAAO;0EAAM;+DAAlB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,UAAU;oDAC7B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC3E,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gEAAkB,OAAO;0EAAO;+DAApB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,OAAO;oDAC1B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACxE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;gEAAqB,OAAO;0EAAU;+DAA1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;;;;;;sEAE1D,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,YAAY,SAAS;4DAC5B,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC1E,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,YAAY,SAAS;oDAC5B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC1E,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,QAAQ;oDAC3B,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;gEAAsB,OAAO;0EAAW;+DAA5B;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,YAAY,SAAS;wDAC9B,UAAU,CAAC,IAAM,eAAe;gEAAC,GAAG,WAAW;gEAAE,WAAW,EAAE,MAAM,CAAC,OAAO;4DAAA;wDAC5E,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;;;;;;;;;;;;wCAIvD,YAAY,SAAS,kBACpB;;8DACE,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,YAAY,kBAAkB;4DACrC,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACnF,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;8EACjB,8OAAC;oEAAO,OAAM;8EAAS;;;;;;8EACvB,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAW;;;;;;;;;;;;;;;;;;8DAI7B,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,MAAK;4DACL,OAAO,YAAY,gBAAgB;4DACnC,UAAU,CAAC,IAAM,eAAe;oEAAC,GAAG,WAAW;oEAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACjF,WAAU;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,YAAY,qBAAqB;oDACxC,UAAU,CAAC,IAAM,eAAe;4DAAC,GAAG,WAAW;4DAAE,uBAAuB,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACtF,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAkC,QAAO;4CAAe,MAAK;4CAAO,SAAQ;sDACzF,cAAA,8OAAC;gDAAK,GAAE;gDAAyL,aAAY;gDAAI,eAAc;gDAAQ,gBAAe;;;;;;;;;;;sDAExP,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAiB,WAAU;;sEACxC,8OAAC;4DAAK,WAAU;sEAA+C;;;;;;sEAG/D,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;8DAIrD,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,WAAU;oDACV,QAAQ;oDACR,QAAO;oDACP,UAAU;;;;;;;;;;;;;;;;;;gCAKf,aAAa,MAAM,GAAG,mBACrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;wCACjD,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAA6B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;gEAAK,WAAU;0EAAyB,KAAK,IAAI;;;;;;0EAClD,8OAAC;gEAAK,WAAU;;oEAA6B;oEAAE,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;kEAEtF,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,WAAW;wDAC1B,WAAU;kEAEV,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAC9D,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;+CAdjE;;;;;;;;;;;;;;;;;sCAwBlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;gEACvD,WAAW,YAAY,MAAM,IAAI,KAAK,OAAO,CAAC;;;;;;;;;;;;;8DAGnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;gEACvD,WAAW,YAAY,SAAS,IAAI,KAAK,OAAO,CAAC;;;;;;;;;;;;;8DAGtD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;sEACpD,8OAAC;4DAAK,WAAU;;gEACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,QAAQ,GAAG;gEACvD,uBAAuB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;sDAKtC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,QAAQ,GAAG,QAAQ;;;;;;;;;;;;8DAGlE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAoB,YAAY,UAAU,IAAI;;;;;;;;;;;;8DAEhE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;sEAAoB,YAAY,aAAa,IAAI;;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAK,WAAU;;gEAAoB,aAAa,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAID,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;6EAIR;;kEACE,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACnE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;oDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B", "debugId": null}}]}