{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/finance/generate-report/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function GenerateFinanceReport() {\n  const [reportConfig, setReportConfig] = useState({\n    reportType: 'expense-summary',\n    dateRange: 'current-month',\n    startDate: '',\n    endDate: '',\n    department: 'all',\n    category: 'all',\n    format: 'pdf',\n    includeCharts: true,\n    includeDetails: true,\n    groupBy: 'department'\n  });\n\n  const [isGenerating, setIsGenerating] = useState(false);\n\n  const reportTypes = [\n    { id: 'expense-summary', name: 'Expense Summary Report', description: 'Overview of all expenses by category and department' },\n    { id: 'budget-analysis', name: 'Budget Analysis Report', description: 'Budget vs actual spending analysis' },\n    { id: 'vendor-spending', name: 'Vendor Spending Report', description: 'Spending breakdown by vendor/supplier' },\n    { id: 'department-costs', name: 'Department Cost Report', description: 'Cost analysis by department' },\n    { id: 'project-financials', name: 'Project Financial Report', description: 'Financial performance by project' },\n    { id: 'cash-flow', name: 'Cash Flow Report', description: 'Cash flow analysis and projections' },\n    { id: 'profit-loss', name: 'Profit & Loss Statement', description: 'P&L statement for specified period' },\n    { id: 'tax-report', name: 'Tax Report', description: 'Tax-related expenses and deductions' }\n  ];\n\n  const categories = [\n    'All Categories', 'Travel & Transportation', 'Office Supplies', 'Marketing & Advertising',\n    'Professional Services', 'Utilities & Communications', 'Entertainment & Meals', 'Technology', 'Other'\n  ];\n\n  const handleGenerateReport = async () => {\n    setIsGenerating(true);\n    await new Promise(resolve => setTimeout(resolve, 3000));\n    setIsGenerating(false);\n    alert('Finance report generated successfully!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/finance\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Generate Finance Report</h1>\n                <p className=\"text-gray-600 text-sm\">Create detailed financial reports and analytics</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-6xl\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Report Configuration */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Report Type */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Report Type</h2>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {reportTypes.map((type) => (\n                  <div\n                    key={type.id}\n                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      reportConfig.reportType === type.id\n                        ? 'border-purple bg-purple/5'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setReportConfig({...reportConfig, reportType: type.id})}\n                  >\n                    <h3 className=\"font-medium text-gray-900 mb-1\">{type.name}</h3>\n                    <p className=\"text-sm text-gray-500\">{type.description}</p>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* Filters */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Filters & Options</h2>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Date Range</label>\n                  <select\n                    value={reportConfig.dateRange}\n                    onChange={(e) => setReportConfig({...reportConfig, dateRange: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"current-month\">Current Month</option>\n                    <option value=\"last-month\">Last Month</option>\n                    <option value=\"current-quarter\">Current Quarter</option>\n                    <option value=\"last-quarter\">Last Quarter</option>\n                    <option value=\"current-year\">Current Year</option>\n                    <option value=\"custom\">Custom Range</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Department</label>\n                  <select\n                    value={reportConfig.department}\n                    onChange={(e) => setReportConfig({...reportConfig, department: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"all\">All Departments</option>\n                    <option value=\"engineering\">Engineering</option>\n                    <option value=\"marketing\">Marketing</option>\n                    <option value=\"sales\">Sales</option>\n                    <option value=\"finance\">Finance</option>\n                    <option value=\"hr\">HR</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Category</label>\n                  <select\n                    value={reportConfig.category}\n                    onChange={(e) => setReportConfig({...reportConfig, category: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    {categories.map(category => (\n                      <option key={category} value={category.toLowerCase().replace(/\\s+/g, '-')}>{category}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Format</label>\n                  <select\n                    value={reportConfig.format}\n                    onChange={(e) => setReportConfig({...reportConfig, format: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"pdf\">PDF</option>\n                    <option value=\"excel\">Excel (XLSX)</option>\n                    <option value=\"csv\">CSV</option>\n                  </select>\n                </div>\n              </div>\n\n              {reportConfig.dateRange === 'custom' && (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Start Date</label>\n                    <input\n                      type=\"date\"\n                      value={reportConfig.startDate}\n                      onChange={(e) => setReportConfig({...reportConfig, startDate: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">End Date</label>\n                    <input\n                      type=\"date\"\n                      value={reportConfig.endDate}\n                      onChange={(e) => setReportConfig({...reportConfig, endDate: e.target.value})}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              <div className=\"mt-6 space-y-3\">\n                <h3 className=\"text-sm font-medium text-gray-700\">Include in Report:</h3>\n                \n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={reportConfig.includeCharts}\n                    onChange={(e) => setReportConfig({...reportConfig, includeCharts: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Charts and visualizations</span>\n                </label>\n\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={reportConfig.includeDetails}\n                    onChange={(e) => setReportConfig({...reportConfig, includeDetails: e.target.checked})}\n                    className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                  />\n                  <span className=\"text-sm text-gray-700\">Detailed transaction breakdown</span>\n                </label>\n              </div>\n            </div>\n          </div>\n\n          {/* Preview & Actions */}\n          <div className=\"space-y-6\">\n            {/* Preview */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Report Preview</h2>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Report Type</h3>\n                  <p className=\"text-lg font-medium text-gray-900\">\n                    {reportTypes.find(r => r.id === reportConfig.reportType)?.name}\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Period</h3>\n                  <p className=\"text-gray-900\">\n                    {reportConfig.dateRange === 'custom' && reportConfig.startDate && reportConfig.endDate\n                      ? `${reportConfig.startDate} to ${reportConfig.endDate}`\n                      : reportConfig.dateRange.replace('-', ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n                    }\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Scope</h3>\n                  <p className=\"text-gray-900\">\n                    {reportConfig.department === 'all' ? 'All Departments' : reportConfig.department}\n                  </p>\n                </div>\n\n                <div>\n                  <h3 className=\"text-sm font-medium text-gray-500\">Format</h3>\n                  <p className=\"text-gray-900\">{reportConfig.format.toUpperCase()}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Quick Stats */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Financial Summary</h2>\n              \n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Total Expenses</span>\n                  <span className=\"font-medium text-red-600\">$247,850</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Budget Allocated</span>\n                  <span className=\"font-medium text-blue-600\">$300,000</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span className=\"text-sm text-gray-600\">Remaining Budget</span>\n                  <span className=\"font-medium text-green-600\">$52,150</span>\n                </div>\n                <div className=\"flex justify-between items-center border-t pt-3\">\n                  <span className=\"text-sm text-gray-600\">Budget Utilization</span>\n                  <span className=\"font-medium text-purple\">82.6%</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Actions */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Actions</h2>\n              \n              <div className=\"space-y-3\">\n                <button\n                  onClick={handleGenerateReport}\n                  disabled={isGenerating}\n                  className=\"w-full bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center\"\n                >\n                  {isGenerating ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Generating Report...\n                    </>\n                  ) : (\n                    <>\n                      <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                      Generate Report\n                    </>\n                  )}\n                </button>\n\n                <button className=\"w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors\">\n                  Save Configuration\n                </button>\n\n                <button className=\"w-full border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors\">\n                  Schedule Report\n                </button>\n              </div>\n            </div>\n\n            {/* Recent Reports */}\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-lg font-semibold text-navy mb-4\">Recent Reports</h2>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Expense Summary</p>\n                    <p className=\"text-xs text-gray-500\">December 2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">Budget Analysis</p>\n                    <p className=\"text-xs text-gray-500\">Q4 2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n\n                <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium text-gray-900\">P&L Statement</p>\n                    <p className=\"text-xs text-gray-500\">2023</p>\n                  </div>\n                  <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                    Download\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY;QACZ,WAAW;QACX,WAAW;QACX,SAAS;QACT,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,eAAe;QACf,gBAAgB;QAChB,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,cAAc;QAClB;YAAE,IAAI;YAAmB,MAAM;YAA0B,aAAa;QAAsD;QAC5H;YAAE,IAAI;YAAmB,MAAM;YAA0B,aAAa;QAAqC;QAC3G;YAAE,IAAI;YAAmB,MAAM;YAA0B,aAAa;QAAwC;QAC9G;YAAE,IAAI;YAAoB,MAAM;YAA0B,aAAa;QAA8B;QACrG;YAAE,IAAI;YAAsB,MAAM;YAA4B,aAAa;QAAmC;QAC9G;YAAE,IAAI;YAAa,MAAM;YAAoB,aAAa;QAAqC;QAC/F;YAAE,IAAI;YAAe,MAAM;YAA2B,aAAa;QAAqC;QACxG;YAAE,IAAI;YAAc,MAAM;YAAc,aAAa;QAAsC;KAC5F;IAED,MAAM,aAAa;QACjB;QAAkB;QAA2B;QAAmB;QAChE;QAAyB;QAA8B;QAAyB;QAAc;KAC/F;IAED,MAAM,uBAAuB;QAC3B,gBAAgB;QAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,gBAAgB;QAChB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAqB,WAAU;8CACxC,cAAA,8OAAC;wCAAI,WAAU;wCAA2B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;oDAEC,WAAW,CAAC,yDAAyD,EACnE,aAAa,UAAU,KAAK,KAAK,EAAE,GAC/B,8BACA,yCACJ;oDACF,SAAS,IAAM,gBAAgB;4DAAC,GAAG,YAAY;4DAAE,YAAY,KAAK,EAAE;wDAAA;;sEAEpE,8OAAC;4DAAG,WAAU;sEAAkC,KAAK,IAAI;;;;;;sEACzD,8OAAC;4DAAE,WAAU;sEAAyB,KAAK,WAAW;;;;;;;mDATjD,KAAK,EAAE;;;;;;;;;;;;;;;;8CAgBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,SAAS;4DAC7B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC5E,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAgB;;;;;;8EAC9B,8OAAC;oEAAO,OAAM;8EAAa;;;;;;8EAC3B,8OAAC;oEAAO,OAAM;8EAAkB;;;;;;8EAChC,8OAAC;oEAAO,OAAM;8EAAe;;;;;;8EAC7B,8OAAC;oEAAO,OAAM;8EAAe;;;;;;8EAC7B,8OAAC;oEAAO,OAAM;8EAAS;;;;;;;;;;;;;;;;;;8DAI3B,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,UAAU;4DAC9B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC7E,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAc;;;;;;8EAC5B,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAK;;;;;;;;;;;;;;;;;;8DAIvB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,QAAQ;4DAC5B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC3E,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oEAAsB,OAAO,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;8EAAO;mEAA/D;;;;;;;;;;;;;;;;8DAKnB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,OAAO,aAAa,MAAM;4DAC1B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACzE,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAQ;;;;;;8EACtB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;;;;;;;;;;;;;;;;;;;wCAKzB,aAAa,SAAS,KAAK,0BAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,MAAK;4DACL,OAAO,aAAa,SAAS;4DAC7B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC5E,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAChE,8OAAC;4DACC,MAAK;4DACL,OAAO,aAAa,OAAO;4DAC3B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC1E,WAAU;;;;;;;;;;;;;;;;;;sDAMlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAElD,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,aAAa;4DACnC,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,eAAe,EAAE,MAAM,CAAC,OAAO;gEAAA;4DAClF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAG1C,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,cAAc;4DACpC,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,gBAAgB,EAAE,MAAM,CAAC,OAAO;gEAAA;4DACnF,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEACV,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,UAAU,GAAG;;;;;;;;;;;;8DAI9D,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEACV,aAAa,SAAS,KAAK,YAAY,aAAa,SAAS,IAAI,aAAa,OAAO,GAClF,GAAG,aAAa,SAAS,CAAC,IAAI,EAAE,aAAa,OAAO,EAAE,GACtD,aAAa,SAAS,CAAC,OAAO,CAAC,KAAK,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;;8DAKpF,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEACV,aAAa,UAAU,KAAK,QAAQ,oBAAoB,aAAa,UAAU;;;;;;;;;;;;8DAIpF,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAiB,aAAa,MAAM,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;8CAMnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;;;;;;;8DAE7C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA4B;;;;;;;;;;;;8DAE9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;8CAMhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;gEAA6C,OAAM;gEAA6B,MAAK;gEAAO,SAAQ;;kFACjH,8OAAC;wEAAO,WAAU;wEAAa,IAAG;wEAAK,IAAG;wEAAK,GAAE;wEAAK,QAAO;wEAAe,aAAY;;;;;;kFACxF,8OAAC;wEAAK,WAAU;wEAAa,MAAK;wEAAe,GAAE;;;;;;;;;;;;4DAC/C;;qFAIR;;0EACE,8OAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACnE,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;4DACjE;;;;;;;;8DAMZ,8OAAC;oDAAO,WAAU;8DAAkH;;;;;;8DAIpI,8OAAC;oDAAO,WAAU;8DAAkH;;;;;;;;;;;;;;;;;;8CAOxI,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;8DAK5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;8DAK5E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAO,WAAU;sEAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5F", "debugId": null}}]}