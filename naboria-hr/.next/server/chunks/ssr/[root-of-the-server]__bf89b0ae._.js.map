{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/it/add-device/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function AddDevice() {\n  const [deviceData, setDeviceData] = useState({\n    // Basic Information\n    deviceName: '',\n    deviceType: '',\n    brand: '',\n    model: '',\n    serialNumber: '',\n    assetTag: '',\n    \n    // Assignment\n    assignedTo: '',\n    department: '',\n    location: '',\n    \n    // Technical Details\n    operatingSystem: '',\n    processor: '',\n    memory: '',\n    storage: '',\n    networkMAC: '',\n    ipAddress: '',\n    \n    // Purchase Information\n    vendor: '',\n    purchaseDate: '',\n    purchasePrice: '',\n    warrantyExpiry: '',\n    \n    // Status\n    status: 'Available',\n    condition: 'New',\n    notes: ''\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const deviceTypes = [\n    'Laptop', 'Desktop', 'Monitor', 'Printer', 'Scanner', 'Tablet', 'Smartphone', \n    'Server', 'Network Equipment', 'Projector', 'Camera', 'Audio Equipment', 'Other'\n  ];\n\n  const brands = [\n    'Apple', 'Dell', 'HP', 'Lenovo', 'Microsoft', 'ASUS', 'Acer', 'Samsung', \n    'LG', 'Canon', 'Epson', 'Cisco', 'Netgear', 'Other'\n  ];\n\n  const employees = [\n    'Sarah Chen', 'Michael Rodriguez', 'Emily Johnson', 'David Kim', 'Lisa Wang',\n    'Alex Thompson', 'Jessica Brown', 'Ryan Miller'\n  ];\n\n  const departments = [\n    'Engineering', 'Marketing', 'Sales', 'Finance', 'HR', 'Operations', 'IT'\n  ];\n\n  const locations = [\n    'San Francisco Office', 'New York Office', 'Austin Office', 'Remote', 'Warehouse', 'Data Center'\n  ];\n\n  const operatingSystems = [\n    'Windows 11', 'Windows 10', 'macOS Sonoma', 'macOS Ventura', 'Ubuntu 22.04', \n    'CentOS 8', 'iOS 17', 'Android 14', 'Other'\n  ];\n\n  const generateAssetTag = () => {\n    const prefix = deviceData.deviceType.substring(0, 3).toUpperCase() || 'DEV';\n    const number = Math.random().toString().substring(2, 8);\n    setDeviceData({...deviceData, assetTag: `${prefix}-${number}`});\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    alert('Device added successfully!');\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/it\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Add New Device</h1>\n                <p className=\"text-gray-600 text-sm\">Register a new device in the inventory system</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {/* Basic Information */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Basic Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Device Name *</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.deviceName}\n                  onChange={(e) => setDeviceData({...deviceData, deviceName: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., Sarah's MacBook Pro\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Device Type *</label>\n                <select\n                  value={deviceData.deviceType}\n                  onChange={(e) => setDeviceData({...deviceData, deviceType: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  required\n                >\n                  <option value=\"\">Select Device Type</option>\n                  {deviceTypes.map(type => (\n                    <option key={type} value={type}>{type}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Brand</label>\n                <select\n                  value={deviceData.brand}\n                  onChange={(e) => setDeviceData({...deviceData, brand: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Brand</option>\n                  {brands.map(brand => (\n                    <option key={brand} value={brand}>{brand}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Model</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.model}\n                  onChange={(e) => setDeviceData({...deviceData, model: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., MacBook Pro 16-inch\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Serial Number</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.serialNumber}\n                  onChange={(e) => setDeviceData({...deviceData, serialNumber: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"Device serial number\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Asset Tag</label>\n                <div className=\"flex\">\n                  <input\n                    type=\"text\"\n                    value={deviceData.assetTag}\n                    onChange={(e) => setDeviceData({...deviceData, assetTag: e.target.value})}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"LAP-123456\"\n                  />\n                  <button\n                    type=\"button\"\n                    onClick={generateAssetTag}\n                    className=\"px-4 py-2 bg-purple text-white rounded-r-lg hover:bg-darkpurple transition-colors\"\n                  >\n                    Generate\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Assignment Information */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Assignment Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Assigned To</label>\n                <select\n                  value={deviceData.assignedTo}\n                  onChange={(e) => setDeviceData({...deviceData, assignedTo: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Unassigned</option>\n                  {employees.map(employee => (\n                    <option key={employee} value={employee}>{employee}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Department</label>\n                <select\n                  value={deviceData.department}\n                  onChange={(e) => setDeviceData({...deviceData, department: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Department</option>\n                  {departments.map(dept => (\n                    <option key={dept} value={dept}>{dept}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Location</label>\n                <select\n                  value={deviceData.location}\n                  onChange={(e) => setDeviceData({...deviceData, location: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select Location</option>\n                  {locations.map(location => (\n                    <option key={location} value={location}>{location}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Technical Details */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Technical Details</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Operating System</label>\n                <select\n                  value={deviceData.operatingSystem}\n                  onChange={(e) => setDeviceData({...deviceData, operatingSystem: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"\">Select OS</option>\n                  {operatingSystems.map(os => (\n                    <option key={os} value={os}>{os}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Processor</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.processor}\n                  onChange={(e) => setDeviceData({...deviceData, processor: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., Apple M3 Pro, Intel i7-13700K\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Memory (RAM)</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.memory}\n                  onChange={(e) => setDeviceData({...deviceData, memory: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., 16GB, 32GB\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Storage</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.storage}\n                  onChange={(e) => setDeviceData({...deviceData, storage: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., 512GB SSD, 1TB HDD\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">MAC Address</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.networkMAC}\n                  onChange={(e) => setDeviceData({...deviceData, networkMAC: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"00:1B:44:11:3A:B7\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">IP Address</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.ipAddress}\n                  onChange={(e) => setDeviceData({...deviceData, ipAddress: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"*************\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Purchase Information */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Purchase Information</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Vendor</label>\n                <input\n                  type=\"text\"\n                  value={deviceData.vendor}\n                  onChange={(e) => setDeviceData({...deviceData, vendor: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  placeholder=\"e.g., Apple Store, Amazon Business\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Purchase Date</label>\n                <input\n                  type=\"date\"\n                  value={deviceData.purchaseDate}\n                  onChange={(e) => setDeviceData({...deviceData, purchaseDate: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Purchase Price</label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                  <input\n                    type=\"number\"\n                    value={deviceData.purchasePrice}\n                    onChange={(e) => setDeviceData({...deviceData, purchasePrice: e.target.value})}\n                    className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"2499.00\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Warranty Expiry</label>\n                <input\n                  type=\"date\"\n                  value={deviceData.warrantyExpiry}\n                  onChange={(e) => setDeviceData({...deviceData, warrantyExpiry: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Status & Notes */}\n          <div className=\"bg-white rounded-xl shadow-sm p-6\">\n            <h2 className=\"text-xl font-semibold text-navy mb-6\">Status & Notes</h2>\n            \n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Status</label>\n                <select\n                  value={deviceData.status}\n                  onChange={(e) => setDeviceData({...deviceData, status: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"Available\">Available</option>\n                  <option value=\"Assigned\">Assigned</option>\n                  <option value=\"In Repair\">In Repair</option>\n                  <option value=\"Retired\">Retired</option>\n                  <option value=\"Lost/Stolen\">Lost/Stolen</option>\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">Condition</label>\n                <select\n                  value={deviceData.condition}\n                  onChange={(e) => setDeviceData({...deviceData, condition: e.target.value})}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                >\n                  <option value=\"New\">New</option>\n                  <option value=\"Excellent\">Excellent</option>\n                  <option value=\"Good\">Good</option>\n                  <option value=\"Fair\">Fair</option>\n                  <option value=\"Poor\">Poor</option>\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Notes</label>\n              <textarea\n                value={deviceData.notes}\n                onChange={(e) => setDeviceData({...deviceData, notes: e.target.value})}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                placeholder=\"Additional notes about the device...\"\n              />\n            </div>\n          </div>\n\n          {/* Submit Buttons */}\n          <div className=\"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n            <Link\n              href=\"/dashboard/it\"\n              className=\"border border-gray-300 text-gray-600 hover:bg-gray-50 px-6 py-2 rounded-lg font-medium transition-colors\"\n            >\n              Cancel\n            </Link>\n            \n            <div className=\"flex space-x-3\">\n              <button\n                type=\"button\"\n                className=\"border border-purple text-purple hover:bg-purple hover:text-white px-6 py-2 rounded-lg font-medium transition-colors\"\n              >\n                Save as Draft\n              </button>\n              \n              <button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"bg-purple hover:bg-darkpurple disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center\"\n              >\n                {isSubmitting ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Adding Device...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    Add Device\n                  </>\n                )}\n              </button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,oBAAoB;QACpB,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,OAAO;QACP,cAAc;QACd,UAAU;QAEV,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,UAAU;QAEV,oBAAoB;QACpB,iBAAiB;QACjB,WAAW;QACX,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,WAAW;QAEX,uBAAuB;QACvB,QAAQ;QACR,cAAc;QACd,eAAe;QACf,gBAAgB;QAEhB,SAAS;QACT,QAAQ;QACR,WAAW;QACX,OAAO;IACT;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,cAAc;QAClB;QAAU;QAAW;QAAW;QAAW;QAAW;QAAU;QAChE;QAAU;QAAqB;QAAa;QAAU;QAAmB;KAC1E;IAED,MAAM,SAAS;QACb;QAAS;QAAQ;QAAM;QAAU;QAAa;QAAQ;QAAQ;QAC9D;QAAM;QAAS;QAAS;QAAS;QAAW;KAC7C;IAED,MAAM,YAAY;QAChB;QAAc;QAAqB;QAAiB;QAAa;QACjE;QAAiB;QAAiB;KACnC;IAED,MAAM,cAAc;QAClB;QAAe;QAAa;QAAS;QAAW;QAAM;QAAc;KACrE;IAED,MAAM,YAAY;QAChB;QAAwB;QAAmB;QAAiB;QAAU;QAAa;KACpF;IAED,MAAM,mBAAmB;QACvB;QAAc;QAAc;QAAgB;QAAiB;QAC7D;QAAY;QAAU;QAAc;KACrC;IAED,MAAM,mBAAmB;QACvB,MAAM,SAAS,WAAW,UAAU,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW,MAAM;QACtE,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,GAAG,SAAS,CAAC,GAAG;QACrD,cAAc;YAAC,GAAG,UAAU;YAAE,UAAU,GAAG,OAAO,CAAC,EAAE,QAAQ;QAAA;IAC/D;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAgB,WAAU;8CACnC,cAAA,8OAAC;wCAAI,WAAU;wCAA2B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAY;4CAAI,GAAE;;;;;;;;;;;;;;;;8CAGzE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gEAAkB,OAAO;0EAAO;+DAApB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,KAAK;oDACvB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACpE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;gEAAmB,OAAO;0EAAQ;+DAAtB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,KAAK;oDACvB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACpE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,YAAY;oDAC9B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC3E,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,OAAO,WAAW,QAAQ;4DAC1B,UAAU,CAAC,IAAM,cAAc;oEAAC,GAAG,UAAU;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAA;4DACvE,WAAU;4DACV,aAAY;;;;;;sEAEd,8OAAC;4DACC,MAAK;4DACL,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAST,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;gEAAsB,OAAO;0EAAW;+DAA5B;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gEAAkB,OAAO;0EAAO;+DAApB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,QAAQ;oDAC1B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACvE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,UAAU,GAAG,CAAC,CAAA,yBACb,8OAAC;gEAAsB,OAAO;0EAAW;+DAA5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,eAAe;oDACjC,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC9E,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,iBAAiB,GAAG,CAAC,CAAA,mBACpB,8OAAC;gEAAgB,OAAO;0EAAK;+DAAhB;;;;;;;;;;;;;;;;;sDAKnB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,SAAS;oDAC3B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACxE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,MAAM;oDACxB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACrE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,OAAO;oDACzB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACtE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,UAAU;oDAC5B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACzE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,SAAS;oDAC3B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACxE,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;sCAOpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,MAAM;oDACxB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACrE,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,YAAY;oDAC9B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,cAAc,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC3E,WAAU;;;;;;;;;;;;sDAId,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAsC;;;;;;sEACtD,8OAAC;4DACC,MAAK;4DACL,OAAO,WAAW,aAAa;4DAC/B,UAAU,CAAC,IAAM,cAAc;oEAAC,GAAG,UAAU;oEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gEAAA;4DAC5E,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,MAAK;oDACL,OAAO,WAAW,cAAc;oDAChC,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAAA;oDAC7E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,MAAM;oDACxB,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACrE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,8OAAC;4DAAO,OAAM;sEAAW;;;;;;sEACzB,8OAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAChE,8OAAC;oDACC,OAAO,WAAW,SAAS;oDAC3B,UAAU,CAAC,IAAM,cAAc;4DAAC,GAAG,UAAU;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAA;oDACxE,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;sEACrB,8OAAC;4DAAO,OAAM;sEAAO;;;;;;;;;;;;;;;;;;;;;;;;8CAK3B,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAChE,8OAAC;4CACC,OAAO,WAAW,KAAK;4CACvB,UAAU,CAAC,IAAM,cAAc;oDAAC,GAAG,UAAU;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAA;4CACpE,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;sDAID,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,6BACC;;kEACE,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;6EAIR;;kEACE,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACnE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;oDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B", "debugId": null}}]}