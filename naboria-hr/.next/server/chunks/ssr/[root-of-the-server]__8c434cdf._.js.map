{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/payroll/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function PayrollManagement() {\n  const [activeTab, setActiveTab] = useState('overview');\n  const [selectedPeriod, setSelectedPeriod] = useState('current');\n\n  const payrollRuns = [\n    {\n      id: 1,\n      period: 'December 2024',\n      status: 'Completed',\n      employees: 247,\n      totalAmount: '$1,234,567',\n      runDate: '2024-12-31',\n      payDate: '2025-01-05'\n    },\n    {\n      id: 2,\n      period: 'November 2024',\n      status: 'Completed',\n      employees: 245,\n      totalAmount: '$1,198,432',\n      runDate: '2024-11-30',\n      payDate: '2024-12-05'\n    },\n    {\n      id: 3,\n      period: 'January 2025',\n      status: 'In Progress',\n      employees: 250,\n      totalAmount: '$1,267,890',\n      runDate: '2025-01-31',\n      payDate: '2025-02-05'\n    }\n  ];\n\n  const upcomingPayments = [\n    { employee: '<PERSON>', amount: '$5,200', date: '2025-01-15', type: 'Salary' },\n    { employee: '<PERSON>', amount: '$3,800', date: '2025-01-15', type: 'Sal<PERSON>' },\n    { employee: '<PERSON>', amount: '$4,100', date: '2025-01-15', type: 'Salary' },\n    { employee: 'David Kim', amount: '$1,200', date: '2025-01-20', type: 'Bonus' }\n  ];\n\n  const taxSummary = {\n    federalTax: '$156,789',\n    stateTax: '$89,234',\n    socialSecurity: '$76,543',\n    medicare: '$17,890',\n    unemployment: '$12,345'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Payroll Management</h1>\n                <p className=\"text-gray-600 text-sm\">Process payroll, manage taxes, and track payments</p>\n              </div>\n            </div>\n            <div className=\"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2\">\n              <Link\n                href=\"/dashboard/payroll/add-payroll\"\n                className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors text-center\"\n              >\n                Run Payroll\n              </Link>\n              <Link\n                href=\"/dashboard/payroll/generate-report\"\n                className=\"border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors text-center\"\n              >\n                Generate Report\n              </Link>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Overview\n              </button>\n              <button\n                onClick={() => setActiveTab('runs')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'runs' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Payroll Runs\n              </button>\n              <button\n                onClick={() => setActiveTab('taxes')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'taxes' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                </svg>\n                Tax Management\n              </button>\n              <button\n                onClick={() => setActiveTab('reports')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'reports' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Reports\n              </button>\n              <button\n                onClick={() => setActiveTab('compliance')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'compliance' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\" />\n                </svg>\n                Compliance\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Stats Cards */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6\">\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Monthly Payroll</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">$1.2M</p>\n                      <p className=\"text-sm text-green-600\">+5.2% from last month</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Employees Paid</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">247</p>\n                      <p className=\"text-sm text-blue-600\">100% processed</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Tax Withholdings</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">$352K</p>\n                      <p className=\"text-sm text-purple\">29.3% of gross</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center justify-between\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-600\">Next Payroll</p>\n                      <p className=\"text-2xl lg:text-3xl font-bold text-navy\">Jan 31</p>\n                      <p className=\"text-sm text-orange-600\">12 days remaining</p>\n                    </div>\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center\">\n                      <svg className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Upcoming Payments */}\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Upcoming Payments</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {upcomingPayments.map((payment, index) => (\n                    <div key={index} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col sm:flex-row sm:items-center justify-between space-y-2 sm:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{payment.employee}</h4>\n                          <p className=\"text-sm text-gray-500\">{payment.type} • Due {payment.date}</p>\n                        </div>\n                        <div className=\"text-right\">\n                          <p className=\"text-lg font-semibold text-navy\">{payment.amount}</p>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'runs' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Payroll Runs</h3>\n                </div>\n                \n                {/* Mobile Cards */}\n                <div className=\"lg:hidden divide-y divide-gray-200\">\n                  {payrollRuns.map((run) => (\n                    <div key={run.id} className=\"p-4\">\n                      <div className=\"flex items-center justify-between mb-3\">\n                        <h4 className=\"font-medium text-gray-900\">{run.period}</h4>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          run.status === 'Completed' \n                            ? 'bg-green-100 text-green-800' \n                            : 'bg-orange-100 text-orange-800'\n                        }`}>\n                          {run.status}\n                        </span>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div>\n                          <span className=\"text-gray-500\">Employees:</span>\n                          <p className=\"font-medium\">{run.employees}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Total:</span>\n                          <p className=\"font-medium\">{run.totalAmount}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Run Date:</span>\n                          <p className=\"font-medium\">{run.runDate}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Pay Date:</span>\n                          <p className=\"font-medium\">{run.payDate}</p>\n                        </div>\n                      </div>\n                      <div className=\"mt-3 flex space-x-2\">\n                        <button className=\"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium\">\n                          View Details\n                        </button>\n                        <button className=\"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium\">\n                          Download\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Period\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Employees\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Total Amount\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Pay Date\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {payrollRuns.map((run) => (\n                        <tr key={run.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {run.period}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              run.status === 'Completed' \n                                ? 'bg-green-100 text-green-800' \n                                : 'bg-orange-100 text-orange-800'\n                            }`}>\n                              {run.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {run.employees}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {run.totalAmount}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {run.payDate}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <button className=\"text-purple hover:text-darkpurple mr-3\">View</button>\n                            <button className=\"text-gray-400 hover:text-gray-600\">Download</button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'taxes' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Tax Summary (YTD)</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-sm text-gray-600\">Federal Tax</p>\n                    <p className=\"text-xl font-bold text-navy\">{taxSummary.federalTax}</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-sm text-gray-600\">State Tax</p>\n                    <p className=\"text-xl font-bold text-navy\">{taxSummary.stateTax}</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-sm text-gray-600\">Social Security</p>\n                    <p className=\"text-xl font-bold text-navy\">{taxSummary.socialSecurity}</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-sm text-gray-600\">Medicare</p>\n                    <p className=\"text-xl font-bold text-navy\">{taxSummary.medicare}</p>\n                  </div>\n                  <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n                    <p className=\"text-sm text-gray-600\">Unemployment</p>\n                    <p className=\"text-xl font-bold text-navy\">{taxSummary.unemployment}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'reports' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Payroll Reports</h3>\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Payroll Summary</h4>\n                    <p className=\"text-sm text-gray-500\">Detailed payroll breakdown by period</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Tax Reports</h4>\n                    <p className=\"text-sm text-gray-500\">Federal and state tax filings</p>\n                  </button>\n                  <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n                    <h4 className=\"font-medium text-gray-900 mb-2\">Employee Earnings</h4>\n                    <p className=\"text-sm text-gray-500\">Individual employee pay statements</p>\n                  </button>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'compliance' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Compliance Status</h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <svg className=\"h-5 w-5 text-green-500 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"font-medium\">Federal Tax Filing</span>\n                    </div>\n                    <span className=\"text-green-600 text-sm\">Up to date</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-green-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <svg className=\"h-5 w-5 text-green-500 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"font-medium\">State Compliance</span>\n                    </div>\n                    <span className=\"text-green-600 text-sm\">Compliant</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-orange-50 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <svg className=\"h-5 w-5 text-orange-500 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <span className=\"font-medium\">Quarterly Filing</span>\n                    </div>\n                    <span className=\"text-orange-600 text-sm\">Due in 15 days</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,cAAc;QAClB;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;QACX;KACD;IAED,MAAM,mBAAmB;QACvB;YAAE,UAAU;YAAc,QAAQ;YAAU,MAAM;YAAc,MAAM;QAAS;QAC/E;YAAE,UAAU;YAAqB,QAAQ;YAAU,MAAM;YAAc,MAAM;QAAS;QACtF;YAAE,UAAU;YAAiB,QAAQ;YAAU,MAAM;YAAc,MAAM;QAAS;QAClF;YAAE,UAAU;YAAa,QAAQ;YAAU,MAAM;YAAc,MAAM;QAAQ;KAC9E;IAED,MAAM,aAAa;QACjB,YAAY;QACZ,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,cAAc;IAChB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQT,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,SACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,UACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,YACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,eACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,8OAAC;wBAAK,WAAU;;4BACb,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAyB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC5E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAsB;;;;;;;;;;;;sEAErC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC1E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM7E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAoC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAA2C;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAA0B;;;;;;;;;;;;sEAEzC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC9E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DAAkC;;;;;;;;;;;0DAElD,8OAAC;gDAAI,WAAU;0DACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wDAAgB,WAAU;kEACzB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAA6B,QAAQ,QAAQ;;;;;;sFAC3D,8OAAC;4EAAE,WAAU;;gFAAyB,QAAQ,IAAI;gFAAC;gFAAQ,QAAQ,IAAI;;;;;;;;;;;;;8EAEzE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAE,WAAU;kFAAmC,QAAQ,MAAM;;;;;;;;;;;;;;;;;uDAP1D;;;;;;;;;;;;;;;;;;;;;;4BAiBnB,cAAc,wBACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAIlD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC;oDAAiB,WAAU;;sEAC1B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA6B,IAAI,MAAM;;;;;;8EACrD,8OAAC;oEAAK,WAAW,CAAC,+BAA+B,EAC/C,IAAI,MAAM,KAAK,cACX,gCACA,iCACJ;8EACC,IAAI,MAAM;;;;;;;;;;;;sEAGf,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,IAAI,SAAS;;;;;;;;;;;;8EAE3C,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,IAAI,WAAW;;;;;;;;;;;;8EAE7C,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,IAAI,OAAO;;;;;;;;;;;;8EAEzC,8OAAC;;sFACC,8OAAC;4EAAK,WAAU;sFAAgB;;;;;;sFAChC,8OAAC;4EAAE,WAAU;sFAAe,IAAI,OAAO;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAO,WAAU;8EAAwE;;;;;;8EAG1F,8OAAC;oEAAO,WAAU;8EAAyE;;;;;;;;;;;;;mDAjCrF,IAAI,EAAE;;;;;;;;;;sDA0CpB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAM,WAAU;kEACf,cAAA,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;8EAG/F,8OAAC;oEAAG,WAAU;8EAAiF;;;;;;;;;;;;;;;;;kEAKnG,8OAAC;wDAAM,WAAU;kEACd,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC;gEAAgB,WAAU;;kFACzB,8OAAC;wEAAG,WAAU;kFACX,IAAI,MAAM;;;;;;kFAEb,8OAAC;wEAAG,WAAU;kFACZ,cAAA,8OAAC;4EAAK,WAAW,CAAC,+BAA+B,EAC/C,IAAI,MAAM,KAAK,cACX,gCACA,iCACJ;sFACC,IAAI,MAAM;;;;;;;;;;;kFAGf,8OAAC;wEAAG,WAAU;kFACX,IAAI,SAAS;;;;;;kFAEhB,8OAAC;wEAAG,WAAU;kFACX,IAAI,WAAW;;;;;;kFAElB,8OAAC;wEAAG,WAAU;kFACX,IAAI,OAAO;;;;;;kFAEd,8OAAC;wEAAG,WAAU;;0FACZ,8OAAC;gFAAO,WAAU;0FAAyC;;;;;;0FAC3D,8OAAC;gFAAO,WAAU;0FAAoC;;;;;;;;;;;;;+DAxBjD,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAmC5B,cAAc,yBACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA+B,WAAW,UAAU;;;;;;;;;;;;8DAEnE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA+B,WAAW,QAAQ;;;;;;;;;;;;8DAEjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA+B,WAAW,cAAc;;;;;;;;;;;;8DAEvE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA+B,WAAW,QAAQ;;;;;;;;;;;;8DAEjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,8OAAC;4DAAE,WAAU;sEAA+B,WAAW,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAO5E,cAAc,2BACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAEvC,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC;4DAAG,WAAU;sEAAiC;;;;;;sEAC/C,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAO9C,cAAc,8BACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAClF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;8EAEvE,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;8DAE3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA8B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EAClF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;8EAEvE,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAK,WAAU;sEAAyB;;;;;;;;;;;;8DAE3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,SAAQ;oEAAY,QAAO;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;8EAEvE,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAK,WAAU;sEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9D", "debugId": null}}]}