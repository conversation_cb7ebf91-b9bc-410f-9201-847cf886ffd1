{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/hr/employee/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useParams } from 'next/navigation';\n\nexport default function EmployeeDetail() {\n  const params = useParams();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  // Mock employee data - in real app, this would be fetched based on ID\n  const employee = {\n    id: params.id,\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    phone: '+****************',\n    employeeId: 'EMP-001',\n    department: 'Engineering',\n    position: 'Senior Software Engineer',\n    manager: '<PERSON>',\n    startDate: '2023-01-15',\n    status: 'Active',\n    location: 'San Francisco, CA',\n    salary: '$120,000',\n    avatar: '/api/placeholder/120/120',\n    \n    // Detailed Information\n    personalInfo: {\n      dateOfBirth: '1990-05-15',\n      address: '123 Main Street, San Francisco, CA 94105',\n      emergencyContact: {\n        name: '<PERSON>',\n        relationship: 'Spouse',\n        phone: '+****************'\n      }\n    },\n    \n    employment: {\n      employmentType: 'Full-time',\n      workSchedule: 'Monday - Friday, 9:00 AM - 5:00 PM',\n      timezone: 'Pacific Time (PT)',\n      probationEndDate: '2023-04-15',\n      nextReviewDate: '2024-01-15'\n    },\n    \n    compensation: {\n      baseSalary: 120000,\n      bonus: 15000,\n      equity: '0.25%',\n      benefits: ['Health Insurance', 'Dental', 'Vision', '401k', 'Life Insurance']\n    },\n    \n    performance: {\n      currentRating: 'Exceeds Expectations',\n      lastReviewDate: '2023-12-15',\n      goals: [\n        { title: 'Lead React Migration Project', status: 'In Progress', dueDate: '2024-03-31' },\n        { title: 'Mentor 2 Junior Developers', status: 'Completed', dueDate: '2023-12-31' },\n        { title: 'Complete AWS Certification', status: 'Not Started', dueDate: '2024-06-30' }\n      ]\n    },\n    \n    timeOff: {\n      ptoBalance: 15.5,\n      sickBalance: 8,\n      personalBalance: 3,\n      recentRequests: [\n        { type: 'PTO', dates: 'Feb 15-22, 2024', days: 6, status: 'Approved' },\n        { type: 'Sick', dates: 'Jan 10, 2024', days: 1, status: 'Approved' }\n      ]\n    },\n    \n    devices: [\n      { type: 'Laptop', model: 'MacBook Pro 16\"', serialNumber: 'ABC123456', assignedDate: '2023-01-15' },\n      { type: 'Monitor', model: 'Dell 27\" 4K', serialNumber: 'DEF789012', assignedDate: '2023-01-15' },\n      { type: 'Phone', model: 'iPhone 15 Pro', serialNumber: 'GHI345678', assignedDate: '2023-09-20' }\n    ],\n    \n    documents: [\n      { name: 'Employment Contract', type: 'Contract', uploadDate: '2023-01-10', status: 'Signed' },\n      { name: 'I-9 Form', type: 'Compliance', uploadDate: '2023-01-12', status: 'Complete' },\n      { name: 'W-4 Form', type: 'Tax', uploadDate: '2023-01-12', status: 'Complete' },\n      { name: 'Performance Review 2023', type: 'Review', uploadDate: '2023-12-15', status: 'Complete' }\n    ]\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/hr\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div className=\"flex items-center\">\n                <div className=\"w-12 h-12 bg-purple/10 rounded-full flex items-center justify-center mr-4\">\n                  <span className=\"text-lg font-bold text-purple\">SC</span>\n                </div>\n                <div>\n                  <h1 className=\"text-2xl font-bold text-navy\">{employee.name}</h1>\n                  <p className=\"text-gray-600 text-sm\">{employee.position} • {employee.department}</p>\n                </div>\n              </div>\n            </div>\n            <div className=\"flex space-x-2\">\n              <button className=\"border border-purple text-purple hover:bg-purple hover:text-white px-4 py-2 rounded-lg font-medium transition-colors\">\n                Edit Employee\n              </button>\n              <button className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors\">\n                Actions\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('overview')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'overview' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                </svg>\n                Overview\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('employment')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'employment' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6\" />\n                </svg>\n                Employment\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('performance')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'performance' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                </svg>\n                Performance\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('timeoff')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'timeoff' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                Time Off\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('devices')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'devices' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n                Devices\n              </button>\n              \n              <button\n                onClick={() => setActiveTab('documents')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'documents' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n                Documents\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Quick Info Cards */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center mr-4\">\n                      <svg className=\"h-6 w-6 text-blue-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Tenure</p>\n                      <p className=\"text-xl font-bold text-navy\">1.2 years</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center mr-4\">\n                      <svg className=\"h-6 w-6 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Performance</p>\n                      <p className=\"text-xl font-bold text-navy\">Exceeds</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center mr-4\">\n                      <svg className=\"h-6 w-6 text-orange-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">PTO Balance</p>\n                      <p className=\"text-xl font-bold text-navy\">15.5 days</p>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <div className=\"flex items-center\">\n                    <div className=\"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center mr-4\">\n                      <svg className=\"h-6 w-6 text-purple\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-600\">Devices</p>\n                      <p className=\"text-xl font-bold text-navy\">{employee.devices.length}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Personal Information */}\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Personal Information</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Contact Information</h3>\n                    <div className=\"space-y-2\">\n                      <p className=\"text-gray-900\">{employee.email}</p>\n                      <p className=\"text-gray-900\">{employee.phone}</p>\n                      <p className=\"text-gray-900\">{employee.personalInfo.address}</p>\n                    </div>\n                  </div>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Emergency Contact</h3>\n                    <div className=\"space-y-2\">\n                      <p className=\"text-gray-900\">{employee.personalInfo.emergencyContact.name}</p>\n                      <p className=\"text-gray-600\">{employee.personalInfo.emergencyContact.relationship}</p>\n                      <p className=\"text-gray-900\">{employee.personalInfo.emergencyContact.phone}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Employment Summary */}\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Employment Summary</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Position Details</h3>\n                    <div className=\"space-y-2\">\n                      <p className=\"text-gray-900\">{employee.position}</p>\n                      <p className=\"text-gray-600\">{employee.department}</p>\n                      <p className=\"text-gray-600\">Reports to: {employee.manager}</p>\n                    </div>\n                  </div>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Employment</h3>\n                    <div className=\"space-y-2\">\n                      <p className=\"text-gray-900\">Start Date: {employee.startDate}</p>\n                      <p className=\"text-gray-600\">{employee.employment.employmentType}</p>\n                      <p className=\"text-gray-600\">{employee.location}</p>\n                    </div>\n                  </div>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Compensation</h3>\n                    <div className=\"space-y-2\">\n                      <p className=\"text-gray-900\">{employee.salary} annually</p>\n                      <p className=\"text-gray-600\">Bonus: ${employee.compensation.bonus.toLocaleString()}</p>\n                      <p className=\"text-gray-600\">Equity: {employee.compensation.equity}</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'employment' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Employment Details</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-4\">Basic Information</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Employee ID:</span>\n                        <span className=\"font-medium\">{employee.employeeId}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Employment Type:</span>\n                        <span className=\"font-medium\">{employee.employment.employmentType}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Status:</span>\n                        <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium\">\n                          {employee.status}\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Start Date:</span>\n                        <span className=\"font-medium\">{employee.startDate}</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-4\">Work Schedule</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Schedule:</span>\n                        <span className=\"font-medium\">{employee.employment.workSchedule}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Timezone:</span>\n                        <span className=\"font-medium\">{employee.employment.timezone}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Location:</span>\n                        <span className=\"font-medium\">{employee.location}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Compensation & Benefits</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-4\">Compensation</h3>\n                    <div className=\"space-y-3\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Base Salary:</span>\n                        <span className=\"font-medium\">${employee.compensation.baseSalary.toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Annual Bonus:</span>\n                        <span className=\"font-medium\">${employee.compensation.bonus.toLocaleString()}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Equity:</span>\n                        <span className=\"font-medium\">{employee.compensation.equity}</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h3 className=\"text-sm font-medium text-gray-500 mb-4\">Benefits</h3>\n                    <div className=\"space-y-2\">\n                      {employee.compensation.benefits.map((benefit, index) => (\n                        <div key={index} className=\"flex items-center\">\n                          <svg className=\"h-4 w-4 text-green-500 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                          </svg>\n                          <span className=\"text-gray-900\">{benefit}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'performance' && (\n            <div className=\"space-y-6\">\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Performance Overview</h2>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple mb-2\">{employee.performance.currentRating}</div>\n                    <p className=\"text-gray-600\">Current Rating</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-blue-600 mb-2\">{employee.performance.lastReviewDate}</div>\n                    <p className=\"text-gray-600\">Last Review</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-green-600 mb-2\">{employee.employment.nextReviewDate}</div>\n                    <p className=\"text-gray-600\">Next Review</p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Goals & Objectives</h2>\n                <div className=\"space-y-4\">\n                  {employee.performance.goals.map((goal, index) => (\n                    <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <h3 className=\"font-medium text-gray-900\">{goal.title}</h3>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          goal.status === 'Completed' ? 'bg-green-100 text-green-800' :\n                          goal.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :\n                          'bg-gray-100 text-gray-800'\n                        }`}>\n                          {goal.status}\n                        </span>\n                      </div>\n                      <p className=\"text-sm text-gray-600\">Due: {goal.dueDate}</p>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'timeoff' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">PTO Balance</h3>\n                  <p className=\"text-3xl font-bold text-blue-600\">{employee.timeOff.ptoBalance}</p>\n                  <p className=\"text-sm text-gray-500\">days available</p>\n                </div>\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Sick Leave</h3>\n                  <p className=\"text-3xl font-bold text-green-600\">{employee.timeOff.sickBalance}</p>\n                  <p className=\"text-sm text-gray-500\">days available</p>\n                </div>\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Personal Days</h3>\n                  <p className=\"text-3xl font-bold text-orange-600\">{employee.timeOff.personalBalance}</p>\n                  <p className=\"text-sm text-gray-500\">days available</p>\n                </div>\n              </div>\n\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h2 className=\"text-xl font-semibold text-navy mb-6\">Recent Requests</h2>\n                <div className=\"space-y-4\">\n                  {employee.timeOff.recentRequests.map((request, index) => (\n                    <div key={index} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">{request.type}</h3>\n                        <p className=\"text-sm text-gray-600\">{request.dates} ({request.days} days)</p>\n                      </div>\n                      <span className=\"px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium\">\n                        {request.status}\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'devices' && (\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Assigned Devices</h2>\n              <div className=\"space-y-4\">\n                {employee.devices.map((device, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                    <div>\n                      <h3 className=\"font-medium text-gray-900\">{device.type}</h3>\n                      <p className=\"text-sm text-gray-600\">{device.model}</p>\n                      <p className=\"text-xs text-gray-500\">Serial: {device.serialNumber}</p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-sm text-gray-600\">Assigned: {device.assignedDate}</p>\n                      <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                        View Details\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'documents' && (\n            <div className=\"bg-white rounded-xl shadow-sm p-6\">\n              <h2 className=\"text-xl font-semibold text-navy mb-6\">Employee Documents</h2>\n              <div className=\"space-y-4\">\n                {employee.documents.map((doc, index) => (\n                  <div key={index} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg\">\n                    <div className=\"flex items-center\">\n                      <svg className=\"h-8 w-8 text-gray-400 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                      </svg>\n                      <div>\n                        <h3 className=\"font-medium text-gray-900\">{doc.name}</h3>\n                        <p className=\"text-sm text-gray-600\">{doc.type} • Uploaded: {doc.uploadDate}</p>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <span className={`px-2 py-1 text-xs rounded-full ${\n                        doc.status === 'Complete' || doc.status === 'Signed' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'\n                      }`}>\n                        {doc.status}\n                      </span>\n                      <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                        Download\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sEAAsE;IACtE,MAAM,WAAW;QACf,IAAI,OAAO,EAAE;QACb,MAAM;QACN,OAAO;QACP,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW;QACX,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,QAAQ;QAER,uBAAuB;QACvB,cAAc;YACZ,aAAa;YACb,SAAS;YACT,kBAAkB;gBAChB,MAAM;gBACN,cAAc;gBACd,OAAO;YACT;QACF;QAEA,YAAY;YACV,gBAAgB;YAChB,cAAc;YACd,UAAU;YACV,kBAAkB;YAClB,gBAAgB;QAClB;QAEA,cAAc;YACZ,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,UAAU;gBAAC;gBAAoB;gBAAU;gBAAU;gBAAQ;aAAiB;QAC9E;QAEA,aAAa;YACX,eAAe;YACf,gBAAgB;YAChB,OAAO;gBACL;oBAAE,OAAO;oBAAgC,QAAQ;oBAAe,SAAS;gBAAa;gBACtF;oBAAE,OAAO;oBAA8B,QAAQ;oBAAa,SAAS;gBAAa;gBAClF;oBAAE,OAAO;oBAA8B,QAAQ;oBAAe,SAAS;gBAAa;aACrF;QACH;QAEA,SAAS;YACP,YAAY;YACZ,aAAa;YACb,iBAAiB;YACjB,gBAAgB;gBACd;oBAAE,MAAM;oBAAO,OAAO;oBAAmB,MAAM;oBAAG,QAAQ;gBAAW;gBACrE;oBAAE,MAAM;oBAAQ,OAAO;oBAAgB,MAAM;oBAAG,QAAQ;gBAAW;aACpE;QACH;QAEA,SAAS;YACP;gBAAE,MAAM;gBAAU,OAAO;gBAAmB,cAAc;gBAAa,cAAc;YAAa;YAClG;gBAAE,MAAM;gBAAW,OAAO;gBAAe,cAAc;gBAAa,cAAc;YAAa;YAC/F;gBAAE,MAAM;gBAAS,OAAO;gBAAiB,cAAc;gBAAa,cAAc;YAAa;SAChG;QAED,WAAW;YACT;gBAAE,MAAM;gBAAuB,MAAM;gBAAY,YAAY;gBAAc,QAAQ;YAAS;YAC5F;gBAAE,MAAM;gBAAY,MAAM;gBAAc,YAAY;gBAAc,QAAQ;YAAW;YACrF;gBAAE,MAAM;gBAAY,MAAM;gBAAO,YAAY;gBAAc,QAAQ;YAAW;YAC9E;gBAAE,MAAM;gBAA2B,MAAM;gBAAU,YAAY;gBAAc,QAAQ;YAAW;SACjG;IACH;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDACnC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAgC;;;;;;;;;;;0DAElD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAgC,SAAS,IAAI;;;;;;kEAC3D,8OAAC;wDAAE,WAAU;;4DAAyB,SAAS,QAAQ;4DAAC;4DAAI,SAAS,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;0CAIrF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAuH;;;;;;kDAGzI,8OAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,eACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,gBACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,YACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,YACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,cACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,8OAAC;wBAAK,WAAU;;4BACb,cAAc,4BACb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC5E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EAA8B;;;;;;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC7E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EAA8B;;;;;;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC9E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EAA8B;;;;;;;;;;;;;;;;;;;;;;;0DAKjD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAsB,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EAC1E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;8EACrC,8OAAC;oEAAE,WAAU;8EAA+B,SAAS,OAAO,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,KAAK;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,KAAK;;;;;;kFAC5C,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;kEAG/D,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,YAAY,CAAC,gBAAgB,CAAC,IAAI;;;;;;kFACzE,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,YAAY,CAAC,gBAAgB,CAAC,YAAY;;;;;;kFACjF,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,YAAY,CAAC,gBAAgB,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOlF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,QAAQ;;;;;;kFAC/C,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,UAAU;;;;;;kFACjD,8OAAC;wEAAE,WAAU;;4EAAgB;4EAAa,SAAS,OAAO;;;;;;;;;;;;;;;;;;;kEAG9D,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAAgB;4EAAa,SAAS,SAAS;;;;;;;kFAC5D,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,UAAU,CAAC,cAAc;;;;;;kFAChE,8OAAC;wEAAE,WAAU;kFAAiB,SAAS,QAAQ;;;;;;;;;;;;;;;;;;kEAGnD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAE,WAAU;;4EAAiB,SAAS,MAAM;4EAAC;;;;;;;kFAC9C,8OAAC;wEAAE,WAAU;;4EAAgB;4EAAS,SAAS,YAAY,CAAC,KAAK,CAAC,cAAc;;;;;;;kFAChF,8OAAC;wEAAE,WAAU;;4EAAgB;4EAAS,SAAS,YAAY,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ7E,cAAc,8BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,UAAU;;;;;;;;;;;;kFAEpD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,UAAU,CAAC,cAAc;;;;;;;;;;;;kFAEnE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FACb,SAAS,MAAM;;;;;;;;;;;;kFAGpB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kEAKvD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,UAAU,CAAC,YAAY;;;;;;;;;;;;kFAEjE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,UAAU,CAAC,QAAQ;;;;;;;;;;;;kFAE7D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAAc;oFAAE,SAAS,YAAY,CAAC,UAAU,CAAC,cAAc;;;;;;;;;;;;;kFAEjF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;;oFAAc;oFAAE,SAAS,YAAY,CAAC,KAAK,CAAC,cAAc;;;;;;;;;;;;;kFAE5E,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAK,WAAU;0FAAe,SAAS,YAAY,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;kEAKjE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;0EACvD,8OAAC;gEAAI,WAAU;0EACZ,SAAS,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5C,8OAAC;wEAAgB,WAAU;;0FACzB,8OAAC;gFAAI,WAAU;gFAA8B,MAAK;gFAAO,SAAQ;gFAAY,QAAO;0FAClF,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAY;oFAAI,GAAE;;;;;;;;;;;0FAEvE,8OAAC;gFAAK,WAAU;0FAAiB;;;;;;;uEAJzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAcvB,cAAc,+BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAuC,SAAS,WAAW,CAAC,aAAa;;;;;;0EACxF,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAyC,SAAS,WAAW,CAAC,cAAc;;;;;;0EAC3F,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA0C,SAAS,UAAU,CAAC,cAAc;;;;;;0EAC3F,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ,SAAS,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrC,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA6B,KAAK,KAAK;;;;;;kFACrD,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,MAAM,KAAK,cAAc,gCAC9B,KAAK,MAAM,KAAK,gBAAgB,8BAChC,6BACA;kFACC,KAAK,MAAM;;;;;;;;;;;;0EAGhB,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAM,KAAK,OAAO;;;;;;;;uDAX/C;;;;;;;;;;;;;;;;;;;;;;4BAmBnB,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAoC,SAAS,OAAO,CAAC,UAAU;;;;;;kEAC5E,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAqC,SAAS,OAAO,CAAC,WAAW;;;;;;kEAC9E,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;0DAEvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAyC;;;;;;kEACvD,8OAAC;wDAAE,WAAU;kEAAsC,SAAS,OAAO,CAAC,eAAe;;;;;;kEACnF,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAIzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DACZ,SAAS,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC7C,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,IAAI;;;;;;kFACvD,8OAAC;wEAAE,WAAU;;4EAAyB,QAAQ,KAAK;4EAAC;4EAAG,QAAQ,IAAI;4EAAC;;;;;;;;;;;;;0EAEtE,8OAAC;gEAAK,WAAU;0EACb,QAAQ,MAAM;;;;;;;uDANT;;;;;;;;;;;;;;;;;;;;;;4BAenB,cAAc,2BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA6B,OAAO,IAAI;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EAAyB,OAAO,KAAK;;;;;;0EAClD,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAS,OAAO,YAAY;;;;;;;;;;;;;kEAEnE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;;oEAAwB;oEAAW,OAAO,YAAY;;;;;;;0EACnE,8OAAC;gEAAO,WAAU;0EAAwD;;;;;;;;;;;;;+CARpE;;;;;;;;;;;;;;;;4BAkBjB,cAAc,6BACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;gEAA6B,MAAK;gEAAO,SAAQ;gEAAY,QAAO;0EACjF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,IAAI,IAAI;;;;;;kFACnD,8OAAC;wEAAE,WAAU;;4EAAyB,IAAI,IAAI;4EAAC;4EAAc,IAAI,UAAU;;;;;;;;;;;;;;;;;;;kEAG/E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,+BAA+B,EAC/C,IAAI,MAAM,KAAK,cAAc,IAAI,MAAM,KAAK,WAAW,gCAAgC,iCACvF;0EACC,IAAI,MAAM;;;;;;0EAEb,8OAAC;gEAAO,WAAU;0EAAwD;;;;;;;;;;;;;+CAhBpE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6B5B", "debugId": null}}]}