{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function Home() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [activeTab, setActiveTab] = useState('startups');\n\n  return (\n    <div className=\"bg-white\">\n      {/* Navigation */}\n      <nav className=\"fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-md z-50 shadow-sm\">\n        <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n          <div className=\"flex items-center\">\n            <svg className=\"h-10 w-10 text-purple\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n              <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n            </svg>\n            <span className=\"ml-2 text-2xl font-bold text-navy font-poppins\">Naboria</span>\n          </div>\n\n          <div className=\"hidden md:flex space-x-8\">\n            <a href=\"#home\" className=\"nav-link active-nav text-navy font-medium hover:text-purple transition-colors\">Home</a>\n            <a href=\"#features\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Features</a>\n            <a href=\"#solutions\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Solutions</a>\n            <a href=\"#integrations\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Integrations</a>\n            <a href=\"#pricing\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Pricing</a>\n            <a href=\"#about\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">About</a>\n            <a href=\"#contact\" className=\"nav-link text-navy font-medium hover:text-purple transition-colors\">Contact</a>\n          </div>\n\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <a href=\"#\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</a>\n            <a href=\"#\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors shadow-md hover:shadow-lg\">Get Started</a>\n          </div>\n\n          <button\n            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            className=\"md:hidden text-navy\"\n          >\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {mobileMenuOpen && (\n          <div className=\"md:hidden bg-white shadow-lg absolute w-full\">\n            <div className=\"container mx-auto px-4 py-3 flex flex-col space-y-3\">\n              <a href=\"#home\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Home</a>\n              <a href=\"#features\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Features</a>\n              <a href=\"#solutions\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Solutions</a>\n              <a href=\"#integrations\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Integrations</a>\n              <a href=\"#pricing\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Pricing</a>\n              <a href=\"#about\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">About</a>\n              <a href=\"#contact\" className=\"text-navy font-medium py-2 hover:text-purple transition-colors\">Contact</a>\n              <div className=\"flex flex-col space-y-3 pt-3 border-t border-gray-200\">\n                <a href=\"#\" className=\"text-navy font-medium hover:text-purple transition-colors\">Login</a>\n                <a href=\"#\" className=\"bg-purple hover:bg-darkpurple text-white px-5 py-2 rounded-full font-medium transition-colors text-center shadow-md\">Get Started</a>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Home Section */}\n      <section id=\"home\" className=\"pt-28 pb-20 md:pt-32 md:pb-24\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row items-center\">\n            <div className=\"md:w-1/2 mb-10 md:mb-0\">\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy leading-tight mb-6 font-poppins\">\n                The Future of Work <span className=\"gradient-text\">Starts Here</span>\n              </h1>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-lg\">\n                Streamline your HR, payroll, and IT operations with our all-in-one platform. Automate workflows, reduce errors, and empower your team.\n              </p>\n              <div className=\"flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4\">\n                <a href=\"#\" className=\"gradient-bg text-white px-8 py-3 rounded-full font-medium text-center shadow-lg hover:shadow-xl transition-all transform hover:-translate-y-1\">\n                  Request Demo\n                </a>\n                <a href=\"#\" className=\"border-2 border-purple text-purple px-8 py-3 rounded-full font-medium text-center hover:bg-purple hover:text-white transition-colors\">\n                  Learn More\n                </a>\n              </div>\n              <div className=\"mt-10 flex items-center\">\n                <div className=\"flex -space-x-2\">\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23A78BFA'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%232DD4BF'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <img src=\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%230A2342'/%3E%3Cpath d='M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12ZM14 26C14 22.6863 16.6863 20 20 20C23.3137 20 26 22.6863 26 26V28H14V26Z' fill='white'/%3E%3C/svg%3E\" alt=\"User\" className=\"w-10 h-10 rounded-full border-2 border-white\" />\n                  <div className=\"w-10 h-10 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-navy font-medium\">+5</div>\n                </div>\n                <p className=\"ml-4 text-gray-600\">Trusted by 1000+ companies worldwide</p>\n              </div>\n            </div>\n            <div className=\"md:w-1/2\">\n              <div className=\"relative\">\n                <div className=\"absolute -top-10 -left-10 w-40 h-40 bg-purple/10 rounded-full\"></div>\n                <div className=\"absolute -bottom-10 -right-10 w-60 h-60 bg-teal/10 rounded-full\"></div>\n                <div className=\"relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-gray-100\">\n                  <div className=\"bg-navy p-4 flex justify-between items-center\">\n                    <div className=\"flex space-x-2\">\n                      <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n                      <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n                    </div>\n                    <div className=\"text-white text-sm\">Naboria Dashboard</div>\n                    <div></div>\n                  </div>\n                  <div className=\"p-6\">\n                    <div className=\"flex justify-between items-center mb-6\">\n                      <div>\n                        <h3 className=\"text-navy font-semibold\">Welcome back, Sarah</h3>\n                        <p className=\"text-gray-500 text-sm\">Monday, June 12</p>\n                      </div>\n                      <div className=\"bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium\">\n                        Admin\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-purple text-xl font-bold\">24</div>\n                        <div className=\"text-gray-500 text-sm\">New Hires</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-teal text-xl font-bold\">98%</div>\n                        <div className=\"text-gray-500 text-sm\">Onboarding</div>\n                      </div>\n                      <div className=\"bg-gray-50 p-4 rounded-lg\">\n                        <div className=\"text-navy text-xl font-bold\">12</div>\n                        <div className=\"text-gray-500 text-sm\">Approvals</div>\n                      </div>\n                    </div>\n                    <div className=\"mb-6\">\n                      <div className=\"flex justify-between items-center mb-2\">\n                        <h4 className=\"font-medium text-navy\">Onboarding Progress</h4>\n                        <span className=\"text-sm text-purple\">View All</span>\n                      </div>\n                      <div className=\"space-y-3\">\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Emily Johnson</span>\n                            <span>75%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-purple h-2 rounded-full\" style={{width: '75%'}}></div>\n                          </div>\n                        </div>\n                        <div>\n                          <div className=\"flex justify-between text-sm mb-1\">\n                            <span>Michael Chen</span>\n                            <span>90%</span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div className=\"bg-teal h-2 rounded-full\" style={{width: '90%'}}></div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <button className=\"text-sm text-navy bg-gray-100 px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                        </svg>\n                        New Task\n                      </button>\n                      <button className=\"text-sm text-white bg-purple px-4 py-2 rounded-lg flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-1\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                        Run Payroll\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAAwB,SAAQ;wCAAY,MAAK;wCAAO,OAAM;;0DAC3E,8OAAC;gDAAK,GAAE;gDAAsC,MAAK;gDAAe,aAAY;;;;;;0DAC9E,8OAAC;gDAAK,GAAE;gDAA8C,MAAK;;;;;;;;;;;;kDAE7D,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAQ,WAAU;kDAAgF;;;;;;kDAC1G,8OAAC;wCAAE,MAAK;wCAAY,WAAU;kDAAqE;;;;;;kDACnG,8OAAC;wCAAE,MAAK;wCAAa,WAAU;kDAAqE;;;;;;kDACpG,8OAAC;wCAAE,MAAK;wCAAgB,WAAU;kDAAqE;;;;;;kDACvG,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;kDAClG,8OAAC;wCAAE,MAAK;wCAAS,WAAU;kDAAqE;;;;;;kDAChG,8OAAC;wCAAE,MAAK;wCAAW,WAAU;kDAAqE;;;;;;;;;;;;0CAGpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA4D;;;;;;kDAClF,8OAAC;wCAAE,MAAK;wCAAI,WAAU;kDAA0H;;;;;;;;;;;;0CAGlJ,8OAAC;gCACC,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACjG,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAY;wCAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;oBAM1E,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAQ,WAAU;8CAAiE;;;;;;8CAC3F,8OAAC;oCAAE,MAAK;oCAAY,WAAU;8CAAiE;;;;;;8CAC/F,8OAAC;oCAAE,MAAK;oCAAa,WAAU;8CAAiE;;;;;;8CAChG,8OAAC;oCAAE,MAAK;oCAAgB,WAAU;8CAAiE;;;;;;8CACnG,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,8OAAC;oCAAE,MAAK;oCAAS,WAAU;8CAAiE;;;;;;8CAC5F,8OAAC;oCAAE,MAAK;oCAAW,WAAU;8CAAiE;;;;;;8CAC9F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA4D;;;;;;sDAClF,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAsH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQtJ,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAuF;0DAChF,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAErD,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAgJ;;;;;;0DAGtK,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAuI;;;;;;;;;;;;kDAI/J,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,8OAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,8OAAC;wDAAI,KAAI;wDAAwY,KAAI;wDAAO,WAAU;;;;;;kEACta,8OAAC;wDAAI,WAAU;kEAAkH;;;;;;;;;;;;0DAEnI,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;0CAGtC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;sEAEjB,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;;;;;;;;;;;8DAEH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAA0B;;;;;;sFACxC,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,8OAAC;oEAAI,WAAU;8EAAsE;;;;;;;;;;;;sEAIvF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAAgC;;;;;;sFAC/C,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;8EAEzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFAA8B;;;;;;sFAC7C,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;sEAG3C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAwB;;;;;;sFACtC,8OAAC;4EAAK,WAAU;sFAAsB;;;;;;;;;;;;8EAExC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;8FACC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;sGAAK;;;;;;sGACN,8OAAC;sGAAK;;;;;;;;;;;;8FAER,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFAAI,WAAU;wFAA6B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;sFAGpE,8OAAC;;8FACC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;sGAAK;;;;;;sGACN,8OAAC;sGAAK;;;;;;;;;;;;8FAER,8OAAC;oFAAI,WAAU;8FACb,cAAA,8OAAC;wFAAI,WAAU;wFAA2B,OAAO;4FAAC,OAAO;wFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sEAKtE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAO,WAAU;;sFAChB,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;8EAGR,8OAAC;oEAAO,WAAU;;sFAChB,8OAAC;4EAAI,OAAM;4EAA6B,WAAU;4EAAe,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACtG,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;wEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa9B", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 941, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 948, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}]}