{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function Login() {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    rememberMe: false\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Handle login logic here\n    console.log('Login attempt:', formData);\n    // For demo purposes, redirect to dashboard\n    window.location.href = '/dashboard';\n  };\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <svg className=\"h-12 w-12 text-purple\" viewBox=\"0 0 40 40\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 5L30 10V30L20 35L10 30V10L20 5Z\" fill=\"currentColor\" fillOpacity=\"0.8\"/>\n            <path d=\"M20 15L25 17.5V25L20 27.5L15 25V17.5L20 15Z\" fill=\"white\"/>\n          </svg>\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-bold text-navy\">\n          Sign in to Naboria\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          Or{' '}\n          <Link href=\"/signup\" className=\"font-medium text-purple hover:text-darkpurple\">\n            create a new account\n          </Link>\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10\">\n          <form className=\"space-y-6\" onSubmit={handleSubmit}>\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"email\"\n                  name=\"email\"\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  required\n                  value={formData.email}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm\"\n                  placeholder=\"Enter your email\"\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <div className=\"mt-1\">\n                <input\n                  id=\"password\"\n                  name=\"password\"\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  required\n                  value={formData.password}\n                  onChange={handleChange}\n                  className=\"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-purple focus:border-purple sm:text-sm\"\n                  placeholder=\"Enter your password\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center\">\n                <input\n                  id=\"rememberMe\"\n                  name=\"rememberMe\"\n                  type=\"checkbox\"\n                  checked={formData.rememberMe}\n                  onChange={handleChange}\n                  className=\"h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                />\n                <label htmlFor=\"rememberMe\" className=\"ml-2 block text-sm text-gray-900\">\n                  Remember me\n                </label>\n              </div>\n\n              <div className=\"text-sm\">\n                <a href=\"#\" className=\"font-medium text-purple hover:text-darkpurple\">\n                  Forgot your password?\n                </a>\n              </div>\n            </div>\n\n            <div>\n              <button\n                type=\"submit\"\n                className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple transition-all\"\n              >\n                Sign in\n              </button>\n            </div>\n          </form>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-2 gap-3\">\n              <button className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors\">\n                <svg className=\"h-5 w-5\" viewBox=\"0 0 24 24\">\n                  <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                  <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                  <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                  <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                </svg>\n                <span className=\"ml-2\">Google</span>\n              </button>\n\n              <button className=\"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                </svg>\n                <span className=\"ml-2\">LinkedIn</span>\n              </button>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <div className=\"bg-gray-50 p-4 rounded-lg\">\n              <h3 className=\"text-sm font-medium text-gray-900 mb-2\">Demo Credentials</h3>\n              <div className=\"text-xs text-gray-600 space-y-1\">\n                <p><strong>Email:</strong> <EMAIL></p>\n                <p><strong>Password:</strong> demo123</p>\n                <p className=\"text-purple\">Use these credentials to explore the dashboard</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 text-center\">\n          <p className=\"text-sm text-gray-600\">\n            Need help?{' '}\n            <Link href=\"/contact\" className=\"font-medium text-purple hover:text-darkpurple\">\n              Contact support\n            </Link>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,0BAA0B;QAC1B,QAAQ,GAAG,CAAC,kBAAkB;QAC9B,2CAA2C;QAC3C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;QAC/C,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,SAAQ;4BAAY,MAAK;4BAAO,OAAM;;8CAC3E,8OAAC;oCAAK,GAAE;oCAAsC,MAAK;oCAAe,aAAY;;;;;;8CAC9E,8OAAC;oCAAK,GAAE;oCAA8C,MAAK;;;;;;;;;;;;;;;;;kCAG/D,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAG9D,8OAAC;wBAAE,WAAU;;4BAAyC;4BACjD;0CACH,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAAgD;;;;;;;;;;;;;;;;;;0BAMnF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;gCAAY,UAAU;;kDACpC,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA0C;;;;;;0DAG3E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO,SAAS,KAAK;oDACrB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA0C;;;;;;0DAG9E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAK;oDACL,cAAa;oDACb,QAAQ;oDACR,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,MAAK;wDACL,SAAS,SAAS,UAAU;wDAC5B,UAAU;wDACV,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAa,WAAU;kEAAmC;;;;;;;;;;;;0DAK3E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,MAAK;oDAAI,WAAU;8DAAgD;;;;;;;;;;;;;;;;;kDAM1E,8OAAC;kDACC,cAAA,8OAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;;;;;;;;;;;;kDAIlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAI,WAAU;wDAAU,SAAQ;;0EAC/B,8OAAC;gEAAK,MAAK;gEAAe,GAAE;;;;;;0EAC5B,8OAAC;gEAAK,MAAK;gEAAe,GAAE;;;;;;0EAC5B,8OAAC;gEAAK,MAAK;gEAAe,GAAE;;;;;;0EAC5B,8OAAC;gEAAK,MAAK;gEAAe,GAAE;;;;;;;;;;;;kEAE9B,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;0DAGzB,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;kEACnD,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;kEAEV,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;;;;;;;8DAC1B,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAkB;;;;;;;8DAC7B,8OAAC;oDAAE,WAAU;8DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACxB;8CACX,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5F", "debugId": null}}]}