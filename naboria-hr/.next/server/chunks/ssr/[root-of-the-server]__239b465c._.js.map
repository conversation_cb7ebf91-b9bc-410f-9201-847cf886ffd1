{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/hr/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function HRManagement() {\n  const [activeTab, setActiveTab] = useState('employees');\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedDepartment, setSelectedDepartment] = useState('all');\n\n  const employees = [\n    {\n      id: 1,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Engineering',\n      position: 'Senior Software Engineer',\n      status: 'Active',\n      startDate: '2023-01-15',\n      salary: '$120,000',\n      avatar: '/api/placeholder/40/40'\n    },\n    {\n      id: 2,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Marketing',\n      position: 'Marketing Manager',\n      status: 'Active',\n      startDate: '2023-03-20',\n      salary: '$85,000',\n      avatar: '/api/placeholder/40/40'\n    },\n    {\n      id: 3,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Engineering',\n      position: 'Frontend Developer',\n      status: 'Active',\n      startDate: '2023-06-10',\n      salary: '$95,000',\n      avatar: '/api/placeholder/40/40'\n    },\n    {\n      id: 4,\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      department: 'Sales',\n      position: 'Sales Representative',\n      status: 'On Leave',\n      startDate: '2022-11-05',\n      salary: '$70,000',\n      avatar: '/api/placeholder/40/40'\n    },\n    {\n      id: 5,\n      name: 'Lisa Wang',\n      email: '<EMAIL>',\n      department: 'Finance',\n      position: 'Financial Analyst',\n      status: 'Active',\n      startDate: '2023-02-28',\n      salary: '$78,000',\n      avatar: '/api/placeholder/40/40'\n    }\n  ];\n\n  const departments = ['all', 'Engineering', 'Marketing', 'Sales', 'Finance', 'HR'];\n\n  const filteredEmployees = employees.filter(employee => {\n    const matchesSearch = employee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         employee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         employee.position.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesDepartment = selectedDepartment === 'all' || employee.department === selectedDepartment;\n    return matchesSearch && matchesDepartment;\n  });\n\n  const pendingRequests = [\n    { id: 1, employee: 'Sarah Chen', type: 'Time Off', date: '2024-01-20', status: 'Pending' },\n    { id: 2, employee: 'Michael Rodriguez', type: 'Expense', date: '2024-01-19', status: 'Pending' },\n    { id: 3, employee: 'Emily Johnson', type: 'Equipment', date: '2024-01-18', status: 'Pending' }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">HR Management</h1>\n                <p className=\"text-gray-600 text-sm\">Manage employees, requests, and HR operations</p>\n              </div>\n            </div>\n            <button className=\"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto\">\n              Add Employee\n            </button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex flex-col lg:flex-row\">\n        {/* Sidebar */}\n        <aside className=\"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0\">\n          <nav className=\"p-4 lg:p-6\">\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => setActiveTab('employees')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'employees' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\" />\n                </svg>\n                Employees\n              </button>\n              <button\n                onClick={() => setActiveTab('requests')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'requests' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n                </svg>\n                Requests\n                <span className=\"ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full\">\n                  {pendingRequests.length}\n                </span>\n              </button>\n              <button\n                onClick={() => setActiveTab('onboarding')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'onboarding' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\" />\n                </svg>\n                Onboarding\n              </button>\n              <button\n                onClick={() => setActiveTab('performance')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'performance' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n                Performance\n              </button>\n              <button\n                onClick={() => setActiveTab('analytics')}\n                className={`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${\n                  activeTab === 'analytics' \n                    ? 'bg-purple/10 text-purple border-r-2 border-purple' \n                    : 'text-gray-600 hover:bg-gray-50'\n                }`}\n              >\n                <svg className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                Analytics\n              </button>\n            </div>\n          </nav>\n        </aside>\n\n        {/* Main Content */}\n        <main className=\"flex-1 p-4 lg:p-8\">\n          {activeTab === 'employees' && (\n            <div>\n              {/* Filters */}\n              <div className=\"bg-white p-4 lg:p-6 rounded-xl shadow-sm mb-6\">\n                <div className=\"flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4\">\n                  <div className=\"flex-1\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Search employees...\"\n                      value={searchTerm}\n                      onChange={(e) => setSearchTerm(e.target.value)}\n                      className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    />\n                  </div>\n                  <select\n                    value={selectedDepartment}\n                    onChange={(e) => setSelectedDepartment(e.target.value)}\n                    className=\"px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    {departments.map(dept => (\n                      <option key={dept} value={dept}>\n                        {dept === 'all' ? 'All Departments' : dept}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Employee List */}\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">\n                    Employees ({filteredEmployees.length})\n                  </h3>\n                </div>\n                \n                {/* Mobile Cards */}\n                <div className=\"lg:hidden\">\n                  {filteredEmployees.map((employee) => (\n                    <div key={employee.id} className=\"p-4 border-b border-gray-200 last:border-b-0\">\n                      <div className=\"flex items-center space-x-3 mb-3\">\n                        <div className=\"w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center\">\n                          <span className=\"text-purple font-medium\">\n                            {employee.name.split(' ').map(n => n[0]).join('')}\n                          </span>\n                        </div>\n                        <div className=\"flex-1\">\n                          <h4 className=\"font-medium text-gray-900\">{employee.name}</h4>\n                          <p className=\"text-sm text-gray-500\">{employee.position}</p>\n                        </div>\n                        <span className={`px-2 py-1 text-xs rounded-full ${\n                          employee.status === 'Active'\n                            ? 'bg-green-100 text-green-800'\n                            : 'bg-orange-100 text-orange-800'\n                        }`}>\n                          {employee.status}\n                        </span>\n                      </div>\n                      <div className=\"grid grid-cols-2 gap-2 text-sm\">\n                        <div>\n                          <span className=\"text-gray-500\">Department:</span>\n                          <p className=\"font-medium\">{employee.department}</p>\n                        </div>\n                        <div>\n                          <span className=\"text-gray-500\">Start Date:</span>\n                          <p className=\"font-medium\">{employee.startDate}</p>\n                        </div>\n                      </div>\n                      <div className=\"mt-3 flex space-x-2\">\n                        <button className=\"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium\">\n                          Edit\n                        </button>\n                        <button className=\"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium\">\n                          View\n                        </button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Desktop Table */}\n                <div className=\"hidden lg:block overflow-x-auto\">\n                  <table className=\"w-full\">\n                    <thead className=\"bg-gray-50\">\n                      <tr>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Employee\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Department\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Position\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Status\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Start Date\n                        </th>\n                        <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                          Actions\n                        </th>\n                      </tr>\n                    </thead>\n                    <tbody className=\"bg-white divide-y divide-gray-200\">\n                      {filteredEmployees.map((employee) => (\n                        <tr key={employee.id} className=\"hover:bg-gray-50\">\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <div className=\"flex items-center\">\n                              <div className=\"w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3\">\n                                <span className=\"text-purple font-medium\">\n                                  {employee.name.split(' ').map(n => n[0]).join('')}\n                                </span>\n                              </div>\n                              <div>\n                                <div className=\"text-sm font-medium text-gray-900\">{employee.name}</div>\n                                <div className=\"text-sm text-gray-500\">{employee.email}</div>\n                              </div>\n                            </div>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {employee.department}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {employee.position}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap\">\n                            <span className={`px-2 py-1 text-xs rounded-full ${\n                              employee.status === 'Active' \n                                ? 'bg-green-100 text-green-800' \n                                : 'bg-orange-100 text-orange-800'\n                            }`}>\n                              {employee.status}\n                            </span>\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                            {employee.startDate}\n                          </td>\n                          <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                            <button className=\"text-purple hover:text-darkpurple mr-3\">Edit</button>\n                            <button className=\"text-gray-400 hover:text-gray-600\">View</button>\n                          </td>\n                        </tr>\n                      ))}\n                    </tbody>\n                  </table>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'requests' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm overflow-hidden\">\n                <div className=\"px-4 lg:px-6 py-4 border-b border-gray-200\">\n                  <h3 className=\"text-lg font-semibold text-navy\">Pending Requests</h3>\n                </div>\n                <div className=\"divide-y divide-gray-200\">\n                  {pendingRequests.map((request) => (\n                    <div key={request.id} className=\"p-4 lg:p-6\">\n                      <div className=\"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0\">\n                        <div>\n                          <h4 className=\"font-medium text-gray-900\">{request.employee}</h4>\n                          <p className=\"text-sm text-gray-500\">{request.type} Request • {request.date}</p>\n                        </div>\n                        <div className=\"flex space-x-2\">\n                          <button className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\">\n                            Approve\n                          </button>\n                          <button className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors\">\n                            Reject\n                          </button>\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'onboarding' && (\n            <div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">New Hires This Month</h3>\n                  <div className=\"space-y-4\">\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium\">Emily Johnson</p>\n                        <p className=\"text-sm text-gray-500\">Frontend Developer</p>\n                      </div>\n                      <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs\">\n                        Completed\n                      </span>\n                    </div>\n                    <div className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                      <div>\n                        <p className=\"font-medium\">Alex Thompson</p>\n                        <p className=\"text-sm text-gray-500\">Product Manager</p>\n                      </div>\n                      <span className=\"bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs\">\n                        In Progress\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Onboarding Checklist</h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center\">\n                      <input type=\"checkbox\" checked className=\"mr-3\" readOnly />\n                      <span className=\"text-sm\">Send welcome email</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <input type=\"checkbox\" checked className=\"mr-3\" readOnly />\n                      <span className=\"text-sm\">Create accounts</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-3\" readOnly />\n                      <span className=\"text-sm\">Schedule orientation</span>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <input type=\"checkbox\" className=\"mr-3\" readOnly />\n                      <span className=\"text-sm\">Assign equipment</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'performance' && (\n            <div>\n              <div className=\"bg-white rounded-xl shadow-sm p-6\">\n                <h3 className=\"text-lg font-semibold text-navy mb-6\">Performance Reviews</h3>\n                <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-green-600\">94%</div>\n                    <p className=\"text-gray-500\">Reviews Completed</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-blue-600\">4.2</div>\n                    <p className=\"text-gray-500\">Average Rating</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-3xl font-bold text-purple\">12</div>\n                    <p className=\"text-gray-500\">Pending Reviews</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'analytics' && (\n            <div>\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Department Distribution</h3>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex justify-between items-center\">\n                      <span>Engineering</span>\n                      <span className=\"font-medium\">45%</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span>Sales</span>\n                      <span className=\"font-medium\">25%</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span>Marketing</span>\n                      <span className=\"font-medium\">20%</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span>Finance</span>\n                      <span className=\"font-medium\">10%</span>\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"bg-white p-6 rounded-xl shadow-sm\">\n                  <h3 className=\"text-lg font-semibold text-navy mb-4\">Turnover Rate</h3>\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl font-bold text-green-600 mb-2\">2.1%</div>\n                    <p className=\"text-gray-500\">Annual turnover rate</p>\n                    <p className=\"text-sm text-green-600 mt-2\">↓ 0.5% from last year</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,YAAY;YACZ,UAAU;YACV,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,QAAQ;QACV;KACD;IAED,MAAM,cAAc;QAAC;QAAO;QAAe;QAAa;QAAS;QAAW;KAAK;IAEjF,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,gBAAgB,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QACpF,MAAM,oBAAoB,uBAAuB,SAAS,SAAS,UAAU,KAAK;QAClF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAG,UAAU;YAAc,MAAM;YAAY,MAAM;YAAc,QAAQ;QAAU;QACzF;YAAE,IAAI;YAAG,UAAU;YAAqB,MAAM;YAAW,MAAM;YAAc,QAAQ;QAAU;QAC/F;YAAE,IAAI;YAAG,UAAU;YAAiB,MAAM;YAAa,MAAM;YAAc,QAAQ;QAAU;KAC9F;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAO,WAAU;0CAA+G;;;;;;;;;;;;;;;;;;;;;;0BAOvI,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAM,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,cACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,aACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;0DAEN,8OAAC;gDAAK,WAAU;0DACb,gBAAgB,MAAM;;;;;;;;;;;;kDAG3B,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,eACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,gBACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAGR,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,0EAA0E,EACpF,cAAc,cACV,sDACA,kCACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;;;;;kCAQd,8OAAC;wBAAK,WAAU;;4BACb,cAAc,6BACb,8OAAC;;kDAEC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;8DAGd,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;oDACrD,WAAU;8DAET,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;4DAAkB,OAAO;sEACvB,SAAS,QAAQ,oBAAoB;2DAD3B;;;;;;;;;;;;;;;;;;;;;kDASrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;;wDAAkC;wDAClC,kBAAkB,MAAM;wDAAC;;;;;;;;;;;;0DAKzC,8OAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;wDAAsB,WAAU;;0EAC/B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFACb,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;kFAGlD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAA6B,SAAS,IAAI;;;;;;0FACxD,8OAAC;gFAAE,WAAU;0FAAyB,SAAS,QAAQ;;;;;;;;;;;;kFAEzD,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,SAAS,MAAM,KAAK,WAChB,gCACA,iCACJ;kFACC,SAAS,MAAM;;;;;;;;;;;;0EAGpB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAE,WAAU;0FAAe,SAAS,UAAU;;;;;;;;;;;;kFAEjD,8OAAC;;0FACC,8OAAC;gFAAK,WAAU;0FAAgB;;;;;;0FAChC,8OAAC;gFAAE,WAAU;0FAAe,SAAS,SAAS;;;;;;;;;;;;;;;;;;0EAGlD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAO,WAAU;kFAAwE;;;;;;kFAG1F,8OAAC;wEAAO,WAAU;kFAAyE;;;;;;;;;;;;;uDAjCrF,SAAS,EAAE;;;;;;;;;;0DA0CzB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DAAM,WAAU;sEACf,cAAA,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAG/F,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAG/F,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAG/F,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAG/F,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;kFAG/F,8OAAC;wEAAG,WAAU;kFAAiF;;;;;;;;;;;;;;;;;sEAKnG,8OAAC;4DAAM,WAAU;sEACd,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC;oEAAqB,WAAU;;sFAC9B,8OAAC;4EAAG,WAAU;sFACZ,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAK,WAAU;sGACb,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;kGAGlD,8OAAC;;0GACC,8OAAC;gGAAI,WAAU;0GAAqC,SAAS,IAAI;;;;;;0GACjE,8OAAC;gGAAI,WAAU;0GAAyB,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;;sFAI5D,8OAAC;4EAAG,WAAU;sFACX,SAAS,UAAU;;;;;;sFAEtB,8OAAC;4EAAG,WAAU;sFACX,SAAS,QAAQ;;;;;;sFAEpB,8OAAC;4EAAG,WAAU;sFACZ,cAAA,8OAAC;gFAAK,WAAW,CAAC,+BAA+B,EAC/C,SAAS,MAAM,KAAK,WAChB,gCACA,iCACJ;0FACC,SAAS,MAAM;;;;;;;;;;;sFAGpB,8OAAC;4EAAG,WAAU;sFACX,SAAS,SAAS;;;;;;sFAErB,8OAAC;4EAAG,WAAU;;8FACZ,8OAAC;oFAAO,WAAU;8FAAyC;;;;;;8FAC3D,8OAAC;oFAAO,WAAU;8FAAoC;;;;;;;;;;;;;mEAlCjD,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BA6CjC,cAAc,4BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,8OAAC;oDAAqB,WAAU;8DAC9B,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAA6B,QAAQ,QAAQ;;;;;;kFAC3D,8OAAC;wEAAE,WAAU;;4EAAyB,QAAQ,IAAI;4EAAC;4EAAY,QAAQ,IAAI;;;;;;;;;;;;;0EAE7E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAO,WAAU;kFAAwG;;;;;;kFAG1H,8OAAC;wEAAO,WAAU;kFAAoG;;;;;;;;;;;;;;;;;;mDAVlH,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;4BAsB7B,cAAc,8BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAc;;;;;;sFAC3B,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,8OAAC;oEAAK,WAAU;8EAA6D;;;;;;;;;;;;sEAI/E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAc;;;;;;sFAC3B,8OAAC;4EAAE,WAAU;sFAAwB;;;;;;;;;;;;8EAEvC,8OAAC;oEAAK,WAAU;8EAA+D;;;;;;;;;;;;;;;;;;;;;;;;sDAOrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,MAAK;oEAAW,OAAO;oEAAC,WAAU;oEAAO,QAAQ;;;;;;8EACxD,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,MAAK;oEAAW,OAAO;oEAAC,WAAU;oEAAO,QAAQ;;;;;;8EACxD,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,MAAK;oEAAW,WAAU;oEAAO,QAAQ;;;;;;8EAChD,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,MAAK;oEAAW,WAAU;oEAAO,QAAQ;;;;;;8EAChD,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQrC,cAAc,+BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAE/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAE/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAiC;;;;;;sEAChD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAOtC,cAAc,6BACb,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;sEAEhC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;8EAAK;;;;;;8EACN,8OAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;sDAKpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAyC;;;;;;sEACxD,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/D", "debugId": null}}]}