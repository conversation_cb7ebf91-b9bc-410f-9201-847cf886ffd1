{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/automation/add-workflow/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function AddWorkflow() {\n  const [workflowData, setWorkflowData] = useState({\n    name: '',\n    description: '',\n    trigger: '',\n    triggerConditions: {},\n    actions: [],\n    isActive: true,\n    category: '',\n    priority: 'medium'\n  });\n\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isCreating, setIsCreating] = useState(false);\n\n  const triggers = [\n    { id: 'employee-onboard', name: 'New Employee Onboarding', description: 'Triggered when a new employee is added' },\n    { id: 'expense-submit', name: 'Expense Submission', description: 'Triggered when an expense is submitted' },\n    { id: 'time-off-request', name: 'Time Off Request', description: 'Triggered when time off is requested' },\n    { id: 'device-assignment', name: 'Device Assignment', description: 'Triggered when a device is assigned' },\n    { id: 'payroll-run', name: 'Payroll Processing', description: 'Triggered during payroll runs' },\n    { id: 'compliance-due', name: 'Compliance Due Date', description: 'Triggered when compliance training is due' },\n    { id: 'performance-review', name: 'Performance Review', description: 'Triggered for performance review cycles' },\n    { id: 'schedule', name: 'Scheduled Trigger', description: 'Triggered on a schedule (daily, weekly, monthly)' }\n  ];\n\n  const actionTypes = [\n    { id: 'send-email', name: 'Send Email', description: 'Send automated email notifications', icon: '📧' },\n    { id: 'create-task', name: 'Create Task', description: 'Create tasks for team members', icon: '✅' },\n    { id: 'update-record', name: 'Update Record', description: 'Update employee or system records', icon: '📝' },\n    { id: 'send-slack', name: 'Send Slack Message', description: 'Send notifications to Slack channels', icon: '💬' },\n    { id: 'generate-report', name: 'Generate Report', description: 'Automatically generate reports', icon: '📊' },\n    { id: 'approve-request', name: 'Auto-Approve', description: 'Automatically approve certain requests', icon: '✅' },\n    { id: 'assign-device', name: 'Assign Device', description: 'Automatically assign devices to employees', icon: '💻' },\n    { id: 'schedule-meeting', name: 'Schedule Meeting', description: 'Schedule meetings or interviews', icon: '📅' }\n  ];\n\n  const categories = [\n    'HR & Onboarding', 'Finance & Expenses', 'IT & Equipment', 'Compliance & Training', \n    'Payroll & Benefits', 'Performance Management', 'General Operations'\n  ];\n\n  const addAction = (actionType: string) => {\n    const newAction = {\n      id: Date.now(),\n      type: actionType,\n      config: {},\n      order: workflowData.actions.length + 1\n    };\n    setWorkflowData({\n      ...workflowData,\n      actions: [...workflowData.actions, newAction]\n    });\n  };\n\n  const removeAction = (actionId: number) => {\n    setWorkflowData({\n      ...workflowData,\n      actions: workflowData.actions.filter(action => action.id !== actionId)\n    });\n  };\n\n  const handleCreateWorkflow = async () => {\n    setIsCreating(true);\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    setIsCreating(false);\n    alert('Workflow created successfully!');\n  };\n\n  const selectedTrigger = triggers.find(t => t.id === workflowData.trigger);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/automation\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Create New Workflow</h1>\n                <p className=\"text-gray-600 text-sm\">Build automated workflows to streamline your processes</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-sm text-gray-600\">Step {currentStep} of 4</p>\n              <p className=\"text-lg font-semibold text-purple\">{Math.round((currentStep / 4) * 100)}%</p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between mb-4\">\n            {[1, 2, 3, 4].map((step) => (\n              <div key={step} className=\"flex items-center\">\n                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${\n                  currentStep > step ? 'bg-green-500 text-white' :\n                  currentStep === step ? 'bg-purple text-white' :\n                  'bg-gray-200 text-gray-600'\n                }`}>\n                  {currentStep > step ? (\n                    <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  ) : (\n                    step\n                  )}\n                </div>\n                {step < 4 && (\n                  <div className={`w-16 h-1 mx-2 ${\n                    currentStep > step ? 'bg-green-500' : 'bg-gray-200'\n                  }`} />\n                )}\n              </div>\n            ))}\n          </div>\n          <div className=\"text-center\">\n            <h2 className=\"text-xl font-semibold text-navy\">\n              {currentStep === 1 && 'Basic Information'}\n              {currentStep === 2 && 'Choose Trigger'}\n              {currentStep === 3 && 'Configure Actions'}\n              {currentStep === 4 && 'Review & Create'}\n            </h2>\n          </div>\n        </div>\n\n        {/* Step Content */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6\">\n          {currentStep === 1 && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Workflow Details</h3>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Workflow Name *</label>\n                  <input\n                    type=\"text\"\n                    value={workflowData.name}\n                    onChange={(e) => setWorkflowData({...workflowData, name: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"e.g., New Employee Onboarding Automation\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Category</label>\n                  <select\n                    value={workflowData.category}\n                    onChange={(e) => setWorkflowData({...workflowData, category: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"\">Select Category</option>\n                    {categories.map(category => (\n                      <option key={category} value={category}>{category}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Priority</label>\n                  <select\n                    value={workflowData.priority}\n                    onChange={(e) => setWorkflowData({...workflowData, priority: e.target.value})}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                  >\n                    <option value=\"low\">Low</option>\n                    <option value=\"medium\">Medium</option>\n                    <option value=\"high\">High</option>\n                  </select>\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">Description</label>\n                  <textarea\n                    value={workflowData.description}\n                    onChange={(e) => setWorkflowData({...workflowData, description: e.target.value})}\n                    rows={4}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                    placeholder=\"Describe what this workflow does and when it should run...\"\n                  />\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      checked={workflowData.isActive}\n                      onChange={(e) => setWorkflowData({...workflowData, isActive: e.target.checked})}\n                      className=\"mr-3 h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                    />\n                    <span className=\"text-sm font-medium text-gray-700\">Activate workflow immediately after creation</span>\n                  </label>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {currentStep === 2 && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Choose Trigger Event</h3>\n              <p className=\"text-gray-600\">Select what event will start this workflow</p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {triggers.map((trigger) => (\n                  <div\n                    key={trigger.id}\n                    className={`p-4 border-2 rounded-lg cursor-pointer transition-colors ${\n                      workflowData.trigger === trigger.id\n                        ? 'border-purple bg-purple/5'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                    onClick={() => setWorkflowData({...workflowData, trigger: trigger.id})}\n                  >\n                    <h4 className=\"font-medium text-gray-900 mb-1\">{trigger.name}</h4>\n                    <p className=\"text-sm text-gray-500\">{trigger.description}</p>\n                  </div>\n                ))}\n              </div>\n\n              {selectedTrigger && (\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"font-medium text-blue-800 mb-2\">Selected Trigger: {selectedTrigger.name}</h4>\n                  <p className=\"text-blue-700 text-sm\">{selectedTrigger.description}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {currentStep === 3 && (\n            <div className=\"space-y-6\">\n              <div className=\"flex justify-between items-center\">\n                <div>\n                  <h3 className=\"text-lg font-medium text-gray-900\">Configure Actions</h3>\n                  <p className=\"text-gray-600\">Add actions that will be executed when the trigger occurs</p>\n                </div>\n                <span className=\"bg-purple/10 text-purple px-3 py-1 rounded-full text-sm font-medium\">\n                  {workflowData.actions.length} action(s)\n                </span>\n              </div>\n\n              {/* Available Actions */}\n              <div>\n                <h4 className=\"font-medium text-gray-900 mb-3\">Available Actions</h4>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\">\n                  {actionTypes.map((actionType) => (\n                    <button\n                      key={actionType.id}\n                      onClick={() => addAction(actionType.id)}\n                      className=\"p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\"\n                    >\n                      <div className=\"text-2xl mb-1\">{actionType.icon}</div>\n                      <h5 className=\"font-medium text-gray-900 text-sm\">{actionType.name}</h5>\n                      <p className=\"text-xs text-gray-500\">{actionType.description}</p>\n                    </button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Selected Actions */}\n              {workflowData.actions.length > 0 && (\n                <div>\n                  <h4 className=\"font-medium text-gray-900 mb-3\">Workflow Actions</h4>\n                  <div className=\"space-y-3\">\n                    {workflowData.actions.map((action, index) => {\n                      const actionType = actionTypes.find(at => at.id === action.type);\n                      return (\n                        <div key={action.id} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                          <div className=\"flex items-center\">\n                            <span className=\"text-2xl mr-3\">{actionType?.icon}</span>\n                            <div>\n                              <h5 className=\"font-medium text-gray-900\">{actionType?.name}</h5>\n                              <p className=\"text-sm text-gray-500\">Step {index + 1}: {actionType?.description}</p>\n                            </div>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <button className=\"text-purple hover:text-darkpurple text-sm font-medium\">\n                              Configure\n                            </button>\n                            <button\n                              onClick={() => removeAction(action.id)}\n                              className=\"text-red-500 hover:text-red-700\"\n                            >\n                              <svg className=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                              </svg>\n                            </button>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          {currentStep === 4 && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Review Workflow</h3>\n              \n              <div className=\"bg-gray-50 rounded-lg p-6 space-y-4\">\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Workflow Name</h4>\n                  <p className=\"text-gray-600\">{workflowData.name || 'Untitled Workflow'}</p>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Description</h4>\n                  <p className=\"text-gray-600\">{workflowData.description || 'No description provided'}</p>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Category</h4>\n                    <p className=\"text-gray-600\">{workflowData.category || 'Not specified'}</p>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Priority</h4>\n                    <p className=\"text-gray-600 capitalize\">{workflowData.priority}</p>\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium text-gray-900\">Status</h4>\n                    <p className=\"text-gray-600\">{workflowData.isActive ? 'Active' : 'Inactive'}</p>\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Trigger</h4>\n                  <p className=\"text-gray-600\">{selectedTrigger?.name || 'No trigger selected'}</p>\n                </div>\n\n                <div>\n                  <h4 className=\"font-medium text-gray-900\">Actions ({workflowData.actions.length})</h4>\n                  <div className=\"space-y-2 mt-2\">\n                    {workflowData.actions.map((action, index) => {\n                      const actionType = actionTypes.find(at => at.id === action.type);\n                      return (\n                        <div key={action.id} className=\"flex items-center\">\n                          <span className=\"text-lg mr-2\">{actionType?.icon}</span>\n                          <span className=\"text-gray-600\">{index + 1}. {actionType?.name}</span>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <div className=\"flex items-start\">\n                  <svg className=\"h-5 w-5 text-green-500 mr-2 mt-0.5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                  </svg>\n                  <div>\n                    <p className=\"text-green-800 font-medium\">Ready to Create</p>\n                    <p className=\"text-green-700 text-sm\">Your workflow is configured and ready to be created.</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Navigation */}\n          <div className=\"flex justify-between mt-8 pt-6 border-t border-gray-200\">\n            <button\n              onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}\n              disabled={currentStep === 1}\n              className=\"flex items-center px-4 py-2 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n              </svg>\n              Previous\n            </button>\n            \n            {currentStep < 4 ? (\n              <button\n                onClick={() => setCurrentStep(Math.min(4, currentStep + 1))}\n                className=\"flex items-center px-4 py-2 bg-purple hover:bg-darkpurple text-white rounded-lg font-medium\"\n              >\n                Next\n                <svg className=\"h-4 w-4 ml-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </button>\n            ) : (\n              <button\n                onClick={handleCreateWorkflow}\n                disabled={isCreating}\n                className=\"flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium\"\n              >\n                {isCreating ? (\n                  <>\n                    <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                      <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                      <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                    Creating...\n                  </>\n                ) : (\n                  <>\n                    <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                    Create Workflow\n                  </>\n                )}\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,MAAM;QACN,aAAa;QACb,SAAS;QACT,mBAAmB,CAAC;QACpB,SAAS,EAAE;QACX,UAAU;QACV,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,IAAI;YAAoB,MAAM;YAA2B,aAAa;QAAyC;QACjH;YAAE,IAAI;YAAkB,MAAM;YAAsB,aAAa;QAAyC;QAC1G;YAAE,IAAI;YAAoB,MAAM;YAAoB,aAAa;QAAuC;QACxG;YAAE,IAAI;YAAqB,MAAM;YAAqB,aAAa;QAAsC;QACzG;YAAE,IAAI;YAAe,MAAM;YAAsB,aAAa;QAAgC;QAC9F;YAAE,IAAI;YAAkB,MAAM;YAAuB,aAAa;QAA4C;QAC9G;YAAE,IAAI;YAAsB,MAAM;YAAsB,aAAa;QAA0C;QAC/G;YAAE,IAAI;YAAY,MAAM;YAAqB,aAAa;QAAmD;KAC9G;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAc,MAAM;YAAc,aAAa;YAAsC,MAAM;QAAK;QACtG;YAAE,IAAI;YAAe,MAAM;YAAe,aAAa;YAAiC,MAAM;QAAI;QAClG;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAAqC,MAAM;QAAK;QAC3G;YAAE,IAAI;YAAc,MAAM;YAAsB,aAAa;YAAwC,MAAM;QAAK;QAChH;YAAE,IAAI;YAAmB,MAAM;YAAmB,aAAa;YAAkC,MAAM;QAAK;QAC5G;YAAE,IAAI;YAAmB,MAAM;YAAgB,aAAa;YAA0C,MAAM;QAAI;QAChH;YAAE,IAAI;YAAiB,MAAM;YAAiB,aAAa;YAA6C,MAAM;QAAK;QACnH;YAAE,IAAI;YAAoB,MAAM;YAAoB,aAAa;YAAmC,MAAM;QAAK;KAChH;IAED,MAAM,aAAa;QACjB;QAAmB;QAAsB;QAAkB;QAC3D;QAAsB;QAA0B;KACjD;IAED,MAAM,YAAY,CAAC;QACjB,MAAM,YAAY;YAChB,IAAI,KAAK,GAAG;YACZ,MAAM;YACN,QAAQ,CAAC;YACT,OAAO,aAAa,OAAO,CAAC,MAAM,GAAG;QACvC;QACA,gBAAgB;YACd,GAAG,YAAY;YACf,SAAS;mBAAI,aAAa,OAAO;gBAAE;aAAU;QAC/C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,gBAAgB;YACd,GAAG,YAAY;YACf,SAAS,aAAa,OAAO,CAAC,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;QAC/D;IACF;IAEA,MAAM,uBAAuB;QAC3B,cAAc;QACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,cAAc;QACd,MAAM;IACR;IAEA,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,OAAO;IAExE,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAwB,WAAU;kDAC3C,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAM;4CAAY;;;;;;;kDACvD,8OAAC;wCAAE,WAAU;;4CAAqC,KAAK,KAAK,CAAC,AAAC,cAAc,IAAK;4CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM9F,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,qBACjB,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAI,WAAW,CAAC,0EAA0E,EACzF,cAAc,OAAO,4BACrB,gBAAgB,OAAO,yBACvB,6BACA;0DACC,cAAc,qBACb,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9D,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;2DAGvE;;;;;;4CAGH,OAAO,mBACN,8OAAC;gDAAI,WAAW,CAAC,cAAc,EAC7B,cAAc,OAAO,iBAAiB,eACtC;;;;;;;uCAjBI;;;;;;;;;;0CAsBd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCACX,gBAAgB,KAAK;wCACrB,gBAAgB,KAAK;wCACrB,gBAAgB,KAAK;wCACrB,gBAAgB,KAAK;;;;;;;;;;;;;;;;;;kCAM5B,8OAAC;wBAAI,WAAU;;4BACZ,gBAAgB,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,MAAK;wDACL,OAAO,aAAa,IAAI;wDACxB,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAA;wDACvE,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,aAAa,QAAQ;wDAC5B,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAC3E,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,yBACd,8OAAC;oEAAsB,OAAO;8EAAW;mEAA5B;;;;;;;;;;;;;;;;;0DAKnB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,aAAa,QAAQ;wDAC5B,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAC3E,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAM;;;;;;0EACpB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAO;;;;;;;;;;;;;;;;;;0DAIzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAChE,8OAAC;wDACC,OAAO,aAAa,WAAW;wDAC/B,UAAU,CAAC,IAAM,gBAAgB;gEAAC,GAAG,YAAY;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAA;wDAC9E,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAM,WAAU;;sEACf,8OAAC;4DACC,MAAK;4DACL,SAAS,aAAa,QAAQ;4DAC9B,UAAU,CAAC,IAAM,gBAAgB;oEAAC,GAAG,YAAY;oEAAE,UAAU,EAAE,MAAM,CAAC,OAAO;gEAAA;4DAC7E,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAO7D,gBAAgB,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAEC,WAAW,CAAC,yDAAyD,EACnE,aAAa,OAAO,KAAK,QAAQ,EAAE,GAC/B,8BACA,yCACJ;gDACF,SAAS,IAAM,gBAAgB;wDAAC,GAAG,YAAY;wDAAE,SAAS,QAAQ,EAAE;oDAAA;;kEAEpE,8OAAC;wDAAG,WAAU;kEAAkC,QAAQ,IAAI;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;+CATpD,QAAQ,EAAE;;;;;;;;;;oCAcpB,iCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;oDAAiC;oDAAmB,gBAAgB,IAAI;;;;;;;0DACtF,8OAAC;gDAAE,WAAU;0DAAyB,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;4BAMxE,gBAAgB,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAE/B,8OAAC;gDAAK,WAAU;;oDACb,aAAa,OAAO,CAAC,MAAM;oDAAC;;;;;;;;;;;;;kDAKjC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;wDAEC,SAAS,IAAM,UAAU,WAAW,EAAE;wDACtC,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;0EAAiB,WAAW,IAAI;;;;;;0EAC/C,8OAAC;gEAAG,WAAU;0EAAqC,WAAW,IAAI;;;;;;0EAClE,8OAAC;gEAAE,WAAU;0EAAyB,WAAW,WAAW;;;;;;;uDANvD,WAAW,EAAE;;;;;;;;;;;;;;;;oCAazB,aAAa,OAAO,CAAC,MAAM,GAAG,mBAC7B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAI,WAAU;0DACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;oDACjC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,OAAO,IAAI;oDAC/D,qBACE,8OAAC;wDAAoB,WAAU;;0EAC7B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiB,YAAY;;;;;;kFAC7C,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAA6B,YAAY;;;;;;0FACvD,8OAAC;gFAAE,WAAU;;oFAAwB;oFAAM,QAAQ;oFAAE;oFAAG,YAAY;;;;;;;;;;;;;;;;;;;0EAGxE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAO,WAAU;kFAAwD;;;;;;kFAG1E,8OAAC;wEACC,SAAS,IAAM,aAAa,OAAO,EAAE;wEACrC,WAAU;kFAEV,cAAA,8OAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFAC9D,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAY;gFAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;uDAjBnE,OAAO,EAAE;;;;;gDAuBvB;;;;;;;;;;;;;;;;;;4BAOT,gBAAgB,mBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAElD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAiB,aAAa,IAAI,IAAI;;;;;;;;;;;;0DAGrD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAiB,aAAa,WAAW,IAAI;;;;;;;;;;;;0DAG5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAiB,aAAa,QAAQ,IAAI;;;;;;;;;;;;kEAEzD,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAA4B,aAAa,QAAQ;;;;;;;;;;;;kEAEhE,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAiB,aAAa,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;0DAIrE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;kEAAiB,iBAAiB,QAAQ;;;;;;;;;;;;0DAGzD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAA4B;4DAAU,aAAa,OAAO,CAAC,MAAM;4DAAC;;;;;;;kEAChF,8OAAC;wDAAI,WAAU;kEACZ,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ;4DACjC,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK,OAAO,IAAI;4DAC/D,qBACE,8OAAC;gEAAoB,WAAU;;kFAC7B,8OAAC;wEAAK,WAAU;kFAAgB,YAAY;;;;;;kFAC5C,8OAAC;wEAAK,WAAU;;4EAAiB,QAAQ;4EAAE;4EAAG,YAAY;;;;;;;;+DAFlD,OAAO,EAAE;;;;;wDAKvB;;;;;;;;;;;;;;;;;;kDAKN,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;oDAAqC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACzF,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;8DAEvE,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wCACxD,UAAU,gBAAgB;wCAC1B,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;4CACjE;;;;;;;oCAIP,cAAc,kBACb,8OAAC;wCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wCACxD,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,WAAU;gDAAe,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACnE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAY;oDAAI,GAAE;;;;;;;;;;;;;;;;6DAIzE,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,2BACC;;8DACE,8OAAC;oDAAI,WAAU;oDAA6C,OAAM;oDAA6B,MAAK;oDAAO,SAAQ;;sEACjH,8OAAC;4DAAO,WAAU;4DAAa,IAAG;4DAAK,IAAG;4DAAK,GAAE;4DAAK,QAAO;4DAAe,aAAY;;;;;;sEACxF,8OAAC;4DAAK,WAAU;4DAAa,MAAK;4DAAe,GAAE;;;;;;;;;;;;gDAC/C;;yEAIR;;8DACE,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DACnE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B", "debugId": null}}]}