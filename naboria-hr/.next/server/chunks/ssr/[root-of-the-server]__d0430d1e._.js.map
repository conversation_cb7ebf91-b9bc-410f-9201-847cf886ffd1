{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Apps/Websites/Naboria%20HR/naboria-hr/src/app/dashboard/payroll/add-payroll/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nexport default function AddPayroll() {\n  const [payrollData, setPayrollData] = useState({\n    payPeriodStart: '',\n    payPeriodEnd: '',\n    payDate: '',\n    payrollType: 'Regular',\n    department: 'all',\n    employees: [],\n    notes: ''\n  });\n\n  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  const employees = [\n    { id: 1, name: '<PERSON>', department: 'Engineering', salary: 120000, hourlyRate: 57.69, hoursWorked: 80, overtime: 0 },\n    { id: 2, name: '<PERSON>', department: 'Marketing', salary: 85000, hourlyRate: 40.87, hoursWorked: 80, overtime: 2 },\n    { id: 3, name: '<PERSON>', department: 'Engineering', salary: 95000, hourlyRate: 45.67, hoursWorked: 80, overtime: 5 },\n    { id: 4, name: '<PERSON>', department: 'Sales', salary: 70000, hourlyRate: 33.65, hoursWorked: 75, overtime: 0 },\n    { id: 5, name: '<PERSON>', department: 'Finance', salary: 78000, hourlyRate: 37.50, hoursWorked: 80, overtime: 3 },\n    { id: 6, name: '<PERSON>', department: 'Engineering', salary: 110000, hourlyRate: 52.88, hoursWorked: 80, overtime: 8 },\n    { id: 7, name: '<PERSON> <PERSON>', department: 'HR', salary: 65000, hourlyRate: 31.25, hoursWorked: 80, overtime: 0 },\n    { id: 8, name: 'Ryan Miller', department: 'Sales', salary: 95000, hourlyRate: 45.67, hoursWorked: 80, overtime: 4 }\n  ];\n\n  const calculateGrossPay = (employee: any) => {\n    const regularPay = employee.hoursWorked * employee.hourlyRate;\n    const overtimePay = employee.overtime * employee.hourlyRate * 1.5;\n    return regularPay + overtimePay;\n  };\n\n  const calculateTaxes = (grossPay: number) => {\n    const federalTax = grossPay * 0.22; // 22% federal tax\n    const stateTax = grossPay * 0.08; // 8% state tax\n    const socialSecurity = grossPay * 0.062; // 6.2% Social Security\n    const medicare = grossPay * 0.0145; // 1.45% Medicare\n    return {\n      federal: federalTax,\n      state: stateTax,\n      socialSecurity,\n      medicare,\n      total: federalTax + stateTax + socialSecurity + medicare\n    };\n  };\n\n  const calculateNetPay = (employee: any) => {\n    const grossPay = calculateGrossPay(employee);\n    const taxes = calculateTaxes(grossPay);\n    return grossPay - taxes.total;\n  };\n\n  const handleEmployeeSelection = (employeeId: number) => {\n    setSelectedEmployees(prev => \n      prev.includes(employeeId) \n        ? prev.filter(id => id !== employeeId)\n        : [...prev, employeeId]\n    );\n  };\n\n  const selectAllEmployees = () => {\n    const filteredEmployees = payrollData.department === 'all' \n      ? employees \n      : employees.filter(emp => emp.department === payrollData.department);\n    \n    setSelectedEmployees(filteredEmployees.map(emp => emp.id));\n  };\n\n  const deselectAllEmployees = () => {\n    setSelectedEmployees([]);\n  };\n\n  const handleProcessPayroll = async () => {\n    setIsProcessing(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 3000));\n    \n    setIsProcessing(false);\n    alert('Payroll processed successfully!');\n  };\n\n  const filteredEmployees = payrollData.department === 'all' \n    ? employees \n    : employees.filter(emp => emp.department === payrollData.department);\n\n  const totalGrossPay = selectedEmployees.reduce((total, empId) => {\n    const employee = employees.find(emp => emp.id === empId);\n    return total + (employee ? calculateGrossPay(employee) : 0);\n  }, 0);\n\n  const totalNetPay = selectedEmployees.reduce((total, empId) => {\n    const employee = employees.find(emp => emp.id === empId);\n    return total + (employee ? calculateNetPay(employee) : 0);\n  }, 0);\n\n  const totalTaxes = totalGrossPay - totalNetPay;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"px-4 sm:px-6 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard/payroll\" className=\"flex items-center mr-4\">\n                <svg className=\"h-6 w-6 text-purple mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 19l-7-7 7-7\" />\n                </svg>\n              </Link>\n              <div>\n                <h1 className=\"text-2xl font-bold text-navy\">Process Payroll</h1>\n                <p className=\"text-gray-600 text-sm\">Create and process a new payroll run</p>\n              </div>\n            </div>\n            <div className=\"text-right\">\n              <p className=\"text-sm text-gray-600\">Selected Employees</p>\n              <p className=\"text-2xl font-bold text-purple\">{selectedEmployees.length}</p>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"container mx-auto px-4 py-8 max-w-6xl\">\n        {/* Payroll Configuration */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-navy mb-6\">Payroll Configuration</h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Pay Period Start *</label>\n              <input\n                type=\"date\"\n                value={payrollData.payPeriodStart}\n                onChange={(e) => setPayrollData({...payrollData, payPeriodStart: e.target.value})}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Pay Period End *</label>\n              <input\n                type=\"date\"\n                value={payrollData.payPeriodEnd}\n                onChange={(e) => setPayrollData({...payrollData, payPeriodEnd: e.target.value})}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Pay Date *</label>\n              <input\n                type=\"date\"\n                value={payrollData.payDate}\n                onChange={(e) => setPayrollData({...payrollData, payDate: e.target.value})}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n                required\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">Payroll Type</label>\n              <select\n                value={payrollData.payrollType}\n                onChange={(e) => setPayrollData({...payrollData, payrollType: e.target.value})}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n              >\n                <option value=\"Regular\">Regular Payroll</option>\n                <option value=\"Bonus\">Bonus Payroll</option>\n                <option value=\"Correction\">Correction Payroll</option>\n                <option value=\"Final\">Final Payroll</option>\n              </select>\n            </div>\n          </div>\n\n          <div className=\"mt-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Department Filter</label>\n            <select\n              value={payrollData.department}\n              onChange={(e) => setPayrollData({...payrollData, department: e.target.value})}\n              className=\"w-full md:w-64 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n            >\n              <option value=\"all\">All Departments</option>\n              <option value=\"Engineering\">Engineering</option>\n              <option value=\"Marketing\">Marketing</option>\n              <option value=\"Sales\">Sales</option>\n              <option value=\"Finance\">Finance</option>\n              <option value=\"HR\">HR</option>\n            </select>\n          </div>\n\n          <div className=\"mt-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Notes</label>\n            <textarea\n              value={payrollData.notes}\n              onChange={(e) => setPayrollData({...payrollData, notes: e.target.value})}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent\"\n              placeholder=\"Add any notes about this payroll run...\"\n            />\n          </div>\n        </div>\n\n        {/* Employee Selection */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6 mb-8\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0\">\n            <h2 className=\"text-xl font-semibold text-navy\">Employee Selection</h2>\n            <div className=\"flex space-x-2\">\n              <button\n                onClick={selectAllEmployees}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors\"\n              >\n                Select All\n              </button>\n              <button\n                onClick={deselectAllEmployees}\n                className=\"border border-gray-300 text-gray-600 hover:bg-gray-50 px-4 py-2 rounded-lg font-medium transition-colors\"\n              >\n                Deselect All\n              </button>\n            </div>\n          </div>\n\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-4 py-3 text-left\">\n                    <input\n                      type=\"checkbox\"\n                      checked={selectedEmployees.length === filteredEmployees.length && filteredEmployees.length > 0}\n                      onChange={() => {\n                        if (selectedEmployees.length === filteredEmployees.length) {\n                          deselectAllEmployees();\n                        } else {\n                          selectAllEmployees();\n                        }\n                      }}\n                      className=\"h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                    />\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Employee</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Department</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Hours</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Overtime</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Gross Pay</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Taxes</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Net Pay</th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredEmployees.map((employee) => {\n                  const grossPay = calculateGrossPay(employee);\n                  const taxes = calculateTaxes(grossPay);\n                  const netPay = calculateNetPay(employee);\n                  \n                  return (\n                    <tr key={employee.id} className={`hover:bg-gray-50 ${selectedEmployees.includes(employee.id) ? 'bg-purple/5' : ''}`}>\n                      <td className=\"px-4 py-4\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedEmployees.includes(employee.id)}\n                          onChange={() => handleEmployeeSelection(employee.id)}\n                          className=\"h-4 w-4 text-purple focus:ring-purple border-gray-300 rounded\"\n                        />\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        <div className=\"text-sm font-medium text-gray-900\">{employee.name}</div>\n                        <div className=\"text-sm text-gray-500\">${employee.hourlyRate.toFixed(2)}/hr</div>\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{employee.department}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{employee.hoursWorked}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{employee.overtime}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">${grossPay.toFixed(2)}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-red-600\">${taxes.total.toFixed(2)}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600\">${netPay.toFixed(2)}</td>\n                    </tr>\n                  );\n                })}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Payroll Summary */}\n        <div className=\"bg-white rounded-xl shadow-sm p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-navy mb-6\">Payroll Summary</h2>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"bg-blue-50 p-6 rounded-lg\">\n              <h3 className=\"text-lg font-medium text-blue-800 mb-2\">Total Gross Pay</h3>\n              <p className=\"text-3xl font-bold text-blue-900\">${totalGrossPay.toFixed(2)}</p>\n              <p className=\"text-sm text-blue-600 mt-1\">{selectedEmployees.length} employees</p>\n            </div>\n            \n            <div className=\"bg-red-50 p-6 rounded-lg\">\n              <h3 className=\"text-lg font-medium text-red-800 mb-2\">Total Taxes</h3>\n              <p className=\"text-3xl font-bold text-red-900\">${totalTaxes.toFixed(2)}</p>\n              <p className=\"text-sm text-red-600 mt-1\">{((totalTaxes / totalGrossPay) * 100 || 0).toFixed(1)}% of gross</p>\n            </div>\n            \n            <div className=\"bg-green-50 p-6 rounded-lg\">\n              <h3 className=\"text-lg font-medium text-green-800 mb-2\">Total Net Pay</h3>\n              <p className=\"text-3xl font-bold text-green-900\">${totalNetPay.toFixed(2)}</p>\n              <p className=\"text-sm text-green-600 mt-1\">Amount to be paid</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"flex space-x-3\">\n            <button className=\"border border-gray-300 text-gray-600 hover:bg-gray-50 px-6 py-2 rounded-lg font-medium transition-colors\">\n              Save as Draft\n            </button>\n            <button className=\"bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg font-medium transition-colors\">\n              Preview Payroll\n            </button>\n          </div>\n          \n          <button\n            onClick={handleProcessPayroll}\n            disabled={selectedEmployees.length === 0 || isProcessing}\n            className=\"bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-8 py-2 rounded-lg font-medium transition-colors flex items-center\"\n          >\n            {isProcessing ? (\n              <>\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Processing...\n              </>\n            ) : (\n              <>\n                <svg className=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\" />\n                </svg>\n                Process Payroll\n              </>\n            )}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,gBAAgB;QAChB,cAAc;QACd,SAAS;QACT,aAAa;QACb,YAAY;QACZ,WAAW,EAAE;QACb,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,YAAY;QAChB;YAAE,IAAI;YAAG,MAAM;YAAc,YAAY;YAAe,QAAQ;YAAQ,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QACxH;YAAE,IAAI;YAAG,MAAM;YAAqB,YAAY;YAAa,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QAC5H;YAAE,IAAI;YAAG,MAAM;YAAiB,YAAY;YAAe,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QAC1H;YAAE,IAAI;YAAG,MAAM;YAAa,YAAY;YAAS,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QAChH;YAAE,IAAI;YAAG,MAAM;YAAa,YAAY;YAAW,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QAClH;YAAE,IAAI;YAAG,MAAM;YAAiB,YAAY;YAAe,QAAQ;YAAQ,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QAC3H;YAAE,IAAI;YAAG,MAAM;YAAiB,YAAY;YAAM,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;QACjH;YAAE,IAAI;YAAG,MAAM;YAAe,YAAY;YAAS,QAAQ;YAAO,YAAY;YAAO,aAAa;YAAI,UAAU;QAAE;KACnH;IAED,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,SAAS,WAAW,GAAG,SAAS,UAAU;QAC7D,MAAM,cAAc,SAAS,QAAQ,GAAG,SAAS,UAAU,GAAG;QAC9D,OAAO,aAAa;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,aAAa,WAAW,MAAM,kBAAkB;QACtD,MAAM,WAAW,WAAW,MAAM,eAAe;QACjD,MAAM,iBAAiB,WAAW,OAAO,uBAAuB;QAChE,MAAM,WAAW,WAAW,QAAQ,iBAAiB;QACrD,OAAO;YACL,SAAS;YACT,OAAO;YACP;YACA;YACA,OAAO,aAAa,WAAW,iBAAiB;QAClD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,WAAW,kBAAkB;QACnC,MAAM,QAAQ,eAAe;QAC7B,OAAO,WAAW,MAAM,KAAK;IAC/B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,qBAAqB,CAAA,OACnB,KAAK,QAAQ,CAAC,cACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,cACzB;mBAAI;gBAAM;aAAW;IAE7B;IAEA,MAAM,qBAAqB;QACzB,MAAM,oBAAoB,YAAY,UAAU,KAAK,QACjD,YACA,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK,YAAY,UAAU;QAErE,qBAAqB,kBAAkB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;IAC1D;IAEA,MAAM,uBAAuB;QAC3B,qBAAqB,EAAE;IACzB;IAEA,MAAM,uBAAuB;QAC3B,gBAAgB;QAEhB,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,MAAM;IACR;IAEA,MAAM,oBAAoB,YAAY,UAAU,KAAK,QACjD,YACA,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK,YAAY,UAAU;IAErE,MAAM,gBAAgB,kBAAkB,MAAM,CAAC,CAAC,OAAO;QACrD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAClD,OAAO,QAAQ,CAAC,WAAW,kBAAkB,YAAY,CAAC;IAC5D,GAAG;IAEH,MAAM,cAAc,kBAAkB,MAAM,CAAC,CAAC,OAAO;QACnD,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAClD,OAAO,QAAQ,CAAC,WAAW,gBAAgB,YAAY,CAAC;IAC1D,GAAG;IAEH,MAAM,aAAa,gBAAgB;IAEnC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAqB,WAAU;kDACxC,cAAA,8OAAC;4CAAI,WAAU;4CAA2B,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAC/E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;;;;;;kDAGzE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;kDACrC,8OAAC;wCAAE,WAAU;kDAAkC,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM/E,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,MAAK;gDACL,OAAO,YAAY,cAAc;gDACjC,UAAU,CAAC,IAAM,eAAe;wDAAC,GAAG,WAAW;wDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC/E,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,MAAK;gDACL,OAAO,YAAY,YAAY;gDAC/B,UAAU,CAAC,IAAM,eAAe;wDAAC,GAAG,WAAW;wDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC7E,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,MAAK;gDACL,OAAO,YAAY,OAAO;gDAC1B,UAAU,CAAC,IAAM,eAAe;wDAAC,GAAG,WAAW;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAA;gDACxE,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO,YAAY,WAAW;gDAC9B,UAAU,CAAC,IAAM,eAAe;wDAAC,GAAG,WAAW;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAA;gDAC5E,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,YAAY,UAAU;wCAC7B,UAAU,CAAC,IAAM,eAAe;gDAAC,GAAG,WAAW;gDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;4CAAA;wCAC3E,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAc;;;;;;0DAC5B,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;0CAIvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,YAAY,KAAK;wCACxB,UAAU,CAAC,IAAM,eAAe;gDAAC,GAAG,WAAW;gDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAA;wCACtE,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;;;;;;;;;;;;;kCAMlB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAML,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DACC,MAAK;4DACL,SAAS,kBAAkB,MAAM,KAAK,kBAAkB,MAAM,IAAI,kBAAkB,MAAM,GAAG;4DAC7F,UAAU;gEACR,IAAI,kBAAkB,MAAM,KAAK,kBAAkB,MAAM,EAAE;oEACzD;gEACF,OAAO;oEACL;gEACF;4DACF;4DACA,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAC/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAGnG,8OAAC;4CAAM,WAAU;sDACd,kBAAkB,GAAG,CAAC,CAAC;gDACtB,MAAM,WAAW,kBAAkB;gDACnC,MAAM,QAAQ,eAAe;gDAC7B,MAAM,SAAS,gBAAgB;gDAE/B,qBACE,8OAAC;oDAAqB,WAAW,CAAC,iBAAiB,EAAE,kBAAkB,QAAQ,CAAC,SAAS,EAAE,IAAI,gBAAgB,IAAI;;sEACjH,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEACC,MAAK;gEACL,SAAS,kBAAkB,QAAQ,CAAC,SAAS,EAAE;gEAC/C,UAAU,IAAM,wBAAwB,SAAS,EAAE;gEACnD,WAAU;;;;;;;;;;;sEAGd,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAI,WAAU;8EAAqC,SAAS,IAAI;;;;;;8EACjE,8OAAC;oEAAI,WAAU;;wEAAwB;wEAAE,SAAS,UAAU,CAAC,OAAO,CAAC;wEAAG;;;;;;;;;;;;;sEAE1E,8OAAC;4DAAG,WAAU;sEAAqD,SAAS,UAAU;;;;;;sEACtF,8OAAC;4DAAG,WAAU;sEAAqD,SAAS,WAAW;;;;;;sEACvF,8OAAC;4DAAG,WAAU;sEAAqD,SAAS,QAAQ;;;;;;sEACpF,8OAAC;4DAAG,WAAU;;gEAAgE;gEAAE,SAAS,OAAO,CAAC;;;;;;;sEACjG,8OAAC;4DAAG,WAAU;;gEAAmD;gEAAE,MAAM,KAAK,CAAC,OAAO,CAAC;;;;;;;sEACvF,8OAAC;4DAAG,WAAU;;gEAAiE;gEAAE,OAAO,OAAO,CAAC;;;;;;;;mDAlBzF,SAAS,EAAE;;;;;4CAqBxB;;;;;;;;;;;;;;;;;;;;;;;kCAOR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAE,WAAU;;oDAAmC;oDAAE,cAAc,OAAO,CAAC;;;;;;;0DACxE,8OAAC;gDAAE,WAAU;;oDAA8B,kBAAkB,MAAM;oDAAC;;;;;;;;;;;;;kDAGtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;;oDAAkC;oDAAE,WAAW,OAAO,CAAC;;;;;;;0DACpE,8OAAC;gDAAE,WAAU;;oDAA6B,CAAC,AAAC,aAAa,gBAAiB,OAAO,CAAC,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;kDAGjG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;;oDAAoC;oDAAE,YAAY,OAAO,CAAC;;;;;;;0DACvE,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;kCAMjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAA2G;;;;;;kDAG7H,8OAAC;wCAAO,WAAU;kDAA8F;;;;;;;;;;;;0CAKlH,8OAAC;gCACC,SAAS;gCACT,UAAU,kBAAkB,MAAM,KAAK,KAAK;gCAC5C,WAAU;0CAET,6BACC;;sDACE,8OAAC;4CAAI,WAAU;4CAA6C,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DACjH,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;iEAIR;;sDACE,8OAAC;4CAAI,WAAU;4CAAe,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACnE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAY;gDAAI,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}]}