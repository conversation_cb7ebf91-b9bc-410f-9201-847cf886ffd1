<!DOCTYPE html><!--qe3xd0_nb7jKRW4Z1fqWe--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/50e15e5e523436b8.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-8c94b35adf29e9b1.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-540481dc452dbf61.js" async=""></script><script src="/_next/static/chunks/main-app-1cd19b73a61d65ce.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/app/dashboard/hr/page-1823e005caa246dc.js" async=""></script><meta name="next-size-adjust" content=""/><title>Naboria | HR, Payroll &amp; IT Automation Platform</title><meta name="description" content="Next-gen HR/Payroll/IT/Finance automation system that outperforms Workday and Rippling. More modular, more automated, more developer-friendly."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-200"><div class="px-4 sm:px-6 py-4"><div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"><div class="flex items-center"><a class="flex items-center mr-4" href="/dashboard"><svg class="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg></a><div><h1 class="text-2xl font-bold text-navy">HR Management</h1><p class="text-gray-600 text-sm">Manage employees, requests, and HR operations</p></div></div><button class="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">Add Employee</button></div></div></header><div class="flex flex-col lg:flex-row"><aside class="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0"><nav class="p-4 lg:p-6"><div class="space-y-2"><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors bg-purple/10 text-purple border-r-2 border-purple"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path></svg>Employees</button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path></svg>Requests<span class="ml-auto bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">3</span></button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path></svg>Onboarding</button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>Performance</button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>Analytics</button></div></nav></aside><main class="flex-1 p-4 lg:p-8"><div><div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm mb-6"><div class="flex flex-col lg:flex-row space-y-4 lg:space-y-0 lg:space-x-4"><div class="flex-1"><input type="text" placeholder="Search employees..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent" value=""/></div><select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple focus:border-transparent"><option value="all" selected="">All Departments</option><option value="Engineering">Engineering</option><option value="Marketing">Marketing</option><option value="Sales">Sales</option><option value="Finance">Finance</option><option value="HR">HR</option></select></div></div><div class="bg-white rounded-xl shadow-sm overflow-hidden"><div class="px-4 lg:px-6 py-4 border-b border-gray-200"><h3 class="text-lg font-semibold text-navy">Employees (<!-- -->5<!-- -->)</h3></div><div class="lg:hidden"><div class="p-4 border-b border-gray-200 last:border-b-0"><div class="flex items-center space-x-3 mb-3"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center"><span class="text-purple font-medium">SC</span></div><div class="flex-1"><h4 class="font-medium text-gray-900">Sarah Chen</h4><p class="text-sm text-gray-500">Senior Software Engineer</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></div><div class="grid grid-cols-2 gap-2 text-sm"><div><span class="text-gray-500">Department:</span><p class="font-medium">Engineering</p></div><div><span class="text-gray-500">Start Date:</span><p class="font-medium">2023-01-15</p></div></div><div class="mt-3 flex space-x-2"><button class="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">Edit</button><button class="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">View</button></div></div><div class="p-4 border-b border-gray-200 last:border-b-0"><div class="flex items-center space-x-3 mb-3"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center"><span class="text-purple font-medium">MR</span></div><div class="flex-1"><h4 class="font-medium text-gray-900">Michael Rodriguez</h4><p class="text-sm text-gray-500">Marketing Manager</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></div><div class="grid grid-cols-2 gap-2 text-sm"><div><span class="text-gray-500">Department:</span><p class="font-medium">Marketing</p></div><div><span class="text-gray-500">Start Date:</span><p class="font-medium">2023-03-20</p></div></div><div class="mt-3 flex space-x-2"><button class="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">Edit</button><button class="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">View</button></div></div><div class="p-4 border-b border-gray-200 last:border-b-0"><div class="flex items-center space-x-3 mb-3"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center"><span class="text-purple font-medium">EJ</span></div><div class="flex-1"><h4 class="font-medium text-gray-900">Emily Johnson</h4><p class="text-sm text-gray-500">Frontend Developer</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></div><div class="grid grid-cols-2 gap-2 text-sm"><div><span class="text-gray-500">Department:</span><p class="font-medium">Engineering</p></div><div><span class="text-gray-500">Start Date:</span><p class="font-medium">2023-06-10</p></div></div><div class="mt-3 flex space-x-2"><button class="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">Edit</button><button class="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">View</button></div></div><div class="p-4 border-b border-gray-200 last:border-b-0"><div class="flex items-center space-x-3 mb-3"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center"><span class="text-purple font-medium">DK</span></div><div class="flex-1"><h4 class="font-medium text-gray-900">David Kim</h4><p class="text-sm text-gray-500">Sales Representative</p></div><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">On Leave</span></div><div class="grid grid-cols-2 gap-2 text-sm"><div><span class="text-gray-500">Department:</span><p class="font-medium">Sales</p></div><div><span class="text-gray-500">Start Date:</span><p class="font-medium">2022-11-05</p></div></div><div class="mt-3 flex space-x-2"><button class="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">Edit</button><button class="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">View</button></div></div><div class="p-4 border-b border-gray-200 last:border-b-0"><div class="flex items-center space-x-3 mb-3"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center"><span class="text-purple font-medium">LW</span></div><div class="flex-1"><h4 class="font-medium text-gray-900">Lisa Wang</h4><p class="text-sm text-gray-500">Financial Analyst</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></div><div class="grid grid-cols-2 gap-2 text-sm"><div><span class="text-gray-500">Department:</span><p class="font-medium">Finance</p></div><div><span class="text-gray-500">Start Date:</span><p class="font-medium">2023-02-28</p></div></div><div class="mt-3 flex space-x-2"><button class="flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium">Edit</button><button class="flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium">View</button></div></div></div><div class="hidden lg:block overflow-x-auto"><table class="w-full"><thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th></tr></thead><tbody class="bg-white divide-y divide-gray-200"><tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3"><span class="text-purple font-medium">SC</span></div><div><div class="text-sm font-medium text-gray-900">Sarah Chen</div><div class="text-sm text-gray-500"><EMAIL></div></div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Engineering</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Senior Software Engineer</td><td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-01-15</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-purple hover:text-darkpurple mr-3">Edit</button><button class="text-gray-400 hover:text-gray-600">View</button></td></tr><tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3"><span class="text-purple font-medium">MR</span></div><div><div class="text-sm font-medium text-gray-900">Michael Rodriguez</div><div class="text-sm text-gray-500"><EMAIL></div></div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Marketing</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Marketing Manager</td><td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-03-20</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-purple hover:text-darkpurple mr-3">Edit</button><button class="text-gray-400 hover:text-gray-600">View</button></td></tr><tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3"><span class="text-purple font-medium">EJ</span></div><div><div class="text-sm font-medium text-gray-900">Emily Johnson</div><div class="text-sm text-gray-500"><EMAIL></div></div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Engineering</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Frontend Developer</td><td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-06-10</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-purple hover:text-darkpurple mr-3">Edit</button><button class="text-gray-400 hover:text-gray-600">View</button></td></tr><tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3"><span class="text-purple font-medium">DK</span></div><div><div class="text-sm font-medium text-gray-900">David Kim</div><div class="text-sm text-gray-500"><EMAIL></div></div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Sales</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Sales Representative</td><td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">On Leave</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022-11-05</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-purple hover:text-darkpurple mr-3">Edit</button><button class="text-gray-400 hover:text-gray-600">View</button></td></tr><tr class="hover:bg-gray-50"><td class="px-6 py-4 whitespace-nowrap"><div class="flex items-center"><div class="w-10 h-10 bg-purple/10 rounded-full flex items-center justify-center mr-3"><span class="text-purple font-medium">LW</span></div><div><div class="text-sm font-medium text-gray-900">Lisa Wang</div><div class="text-sm text-gray-500"><EMAIL></div></div></div></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Finance</td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Financial Analyst</td><td class="px-6 py-4 whitespace-nowrap"><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Active</span></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-02-28</td><td class="px-6 py-4 whitespace-nowrap text-sm font-medium"><button class="text-purple hover:text-darkpurple mr-3">Edit</button><button class="text-gray-400 hover:text-gray-600">View</button></td></tr></tbody></table></div></div></div></main></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-8c94b35adf29e9b1.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[8530,[\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"160\",\"static/chunks/app/dashboard/hr/page-1823e005caa246dc.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\na:I[4911,[],\"AsyncMetadataOutlet\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[8393,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/50e15e5e523436b8.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"qe3xd0_nb7jKRW4Z1fqWe\",\"p\":\"\",\"c\":[\"\",\"dashboard\",\"hr\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"dashboard\",{\"children\":[\"hr\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/50e15e5e523436b8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"dashboard\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"hr\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:I[8175,[],\"IconMark\"]\nb:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Naboria | HR, Payroll \u0026 IT Automation Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Next-gen HR/Payroll/IT/Finance automation system that outperforms Workday and Rippling. More modular, more automated, more developer-friendly.\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L12\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>