<!DOCTYPE html><!--qe3xd0_nb7jKRW4Z1fqWe--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/569ce4b8f30dc480-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" href="/_next/static/media/93f479601ee12b01-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/50e15e5e523436b8.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-8c94b35adf29e9b1.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-540481dc452dbf61.js" async=""></script><script src="/_next/static/chunks/main-app-1cd19b73a61d65ce.js" async=""></script><script src="/_next/static/chunks/874-437a265a67d6cfee.js" async=""></script><script src="/_next/static/chunks/app/dashboard/automation/page-b54e178109f15367.js" async=""></script><meta name="next-size-adjust" content=""/><title>Naboria | HR, Payroll &amp; IT Automation Platform</title><meta name="description" content="Next-gen HR/Payroll/IT/Finance automation system that outperforms Workday and Rippling. More modular, more automated, more developer-friendly."/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-gray-50"><header class="bg-white shadow-sm border-b border-gray-200"><div class="px-4 sm:px-6 py-4"><div class="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0"><div class="flex items-center"><a class="flex items-center mr-4" href="/dashboard"><svg class="h-6 w-6 text-purple mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg></a><div><h1 class="text-2xl font-bold text-navy">Automation Center</h1><p class="text-gray-600 text-sm">Manage workflows, integrations, and automated processes</p></div></div><button class="bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto">Create Workflow</button></div></div></header><div class="flex flex-col lg:flex-row"><aside class="w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0"><nav class="p-4 lg:p-6"><div class="space-y-2"><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors bg-purple/10 text-purple border-r-2 border-purple"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg>Overview</button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg>Workflows<span class="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">3</span></button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>Integrations<span class="ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">1</span></button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path></svg>Rules</button><button class="w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50"><svg class="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>Activity Log</button></div></nav></aside><main class="flex-1 p-4 lg:p-8"><div class="space-y-6"><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"><div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Active Workflows</p><p class="text-2xl lg:text-3xl font-bold text-navy">3</p><p class="text-sm text-green-600">All running smoothly</p></div><div class="w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center"><svg class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path></svg></div></div></div><div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Tasks Automated</p><p class="text-2xl lg:text-3xl font-bold text-navy">1,247</p><p class="text-sm text-blue-600">This month</p></div><div class="w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center"><svg class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div></div></div><div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Time Saved</p><p class="text-2xl lg:text-3xl font-bold text-navy">156h</p><p class="text-sm text-purple">This month</p></div><div class="w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center"><svg class="h-6 w-6 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div></div></div><div class="bg-white p-4 lg:p-6 rounded-xl shadow-sm"><div class="flex items-center justify-between"><div><p class="text-sm font-medium text-gray-600">Success Rate</p><p class="text-2xl lg:text-3xl font-bold text-navy">98.4%</p><p class="text-sm text-green-600">Excellent</p></div><div class="w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center"><svg class="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path></svg></div></div></div></div><div class="bg-white rounded-xl shadow-sm p-6"><h3 class="text-lg font-semibold text-navy mb-4">Quick Actions</h3><div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"><button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"><div class="w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3"><svg class="h-4 w-4 text-purple" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg></div><h4 class="font-medium text-gray-900 mb-1">Create Workflow</h4><p class="text-sm text-gray-500">Build new automation</p></button><button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"><div class="w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3"><svg class="h-4 w-4 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"></path></svg></div><h4 class="font-medium text-gray-900 mb-1">Add Integration</h4><p class="text-sm text-gray-500">Connect new service</p></button><button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"><div class="w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3"><svg class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path></svg></div><h4 class="font-medium text-gray-900 mb-1">Create Rule</h4><p class="text-sm text-gray-500">Set up automation rule</p></button><button class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"><div class="w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3"><svg class="h-4 w-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h4 class="font-medium text-gray-900 mb-1">View Reports</h4><p class="text-sm text-gray-500">Automation analytics</p></button></div></div><div class="bg-white rounded-xl shadow-sm overflow-hidden"><div class="px-4 lg:px-6 py-4 border-b border-gray-200"><h3 class="text-lg font-semibold text-navy">Recent Activity</h3></div><div class="divide-y divide-gray-200"><div class="p-4 lg:p-6"><div class="flex items-center space-x-3"><div class="w-8 h-8 rounded-full flex items-center justify-center bg-green/10"><svg class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><div class="flex-1"><p class="text-sm font-medium text-gray-900">Created accounts for Emily Johnson</p><p class="text-xs text-gray-500">Employee Onboarding<!-- --> • <!-- -->2024-01-15 09:30</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Success</span></div></div><div class="p-4 lg:p-6"><div class="flex items-center space-x-3"><div class="w-8 h-8 rounded-full flex items-center justify-center bg-green/10"><svg class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><div class="flex-1"><p class="text-sm font-medium text-gray-900">Auto-approved $245 office supplies expense</p><p class="text-xs text-gray-500">Expense Approval<!-- --> • <!-- -->2024-01-15 14:22</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Success</span></div></div><div class="p-4 lg:p-6"><div class="flex items-center space-x-3"><div class="w-8 h-8 rounded-full flex items-center justify-center bg-green/10"><svg class="h-4 w-4 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg></div><div class="flex-1"><p class="text-sm font-medium text-gray-900">Provisioned MacBook for new hire</p><p class="text-xs text-gray-500">IT Equipment Request<!-- --> • <!-- -->2024-01-14 16:45</p></div><span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">Success</span></div></div><div class="p-4 lg:p-6"><div class="flex items-center space-x-3"><div class="w-8 h-8 rounded-full flex items-center justify-center bg-orange/10"><svg class="h-4 w-4 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg></div><div class="flex-1"><p class="text-sm font-medium text-gray-900">Detected suspicious login attempt</p><p class="text-xs text-gray-500">Security Alert<!-- --> • <!-- -->2024-01-14 11:20</p></div><span class="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800">Warning</span></div></div></div></div></div></main></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-8c94b35adf29e9b1.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[7111,[\"874\",\"static/chunks/874-437a265a67d6cfee.js\",\"673\",\"static/chunks/app/dashboard/automation/page-b54e178109f15367.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\na:I[4911,[],\"AsyncMetadataOutlet\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[8393,[],\"\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/50e15e5e523436b8.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"qe3xd0_nb7jKRW4Z1fqWe\",\"p\":\"\",\"c\":[\"\",\"dashboard\",\"automation\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"dashboard\",{\"children\":[\"automation\",{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/50e15e5e523436b8.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"dashboard\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"automation\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:2:children:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:I[8175,[],\"IconMark\"]\nb:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Naboria | HR, Payroll \u0026 IT Automation Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Next-gen HR/Payroll/IT/Finance automation system that outperforms Workday and Rippling. More modular, more automated, more developer-friendly.\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L12\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>