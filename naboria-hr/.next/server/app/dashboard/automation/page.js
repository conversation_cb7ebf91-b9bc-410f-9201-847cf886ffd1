(()=>{var a={};a.id=673,a.ids=[673],a.modules={162:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["automation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,961)),"/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx"]}]},{}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/automation/page",pathname:"/dashboard/automation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/automation/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},961:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx","default")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1135:()=>{},1947:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},2563:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3353:(a,b,c)=>{Promise.resolve().then(c.bind(c,961))},3725:()=>{},3873:a=>{"use strict";a.exports=require("path")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(2202),f=c.n(e),g=c(4988),h=c.n(g);c(1135);let i={title:"Naboria | HR, Payroll & IT Automation Platform",description:"Next-gen HR/Payroll/IT/Finance automation system that outperforms Workday and Rippling. More modular, more automated, more developer-friendly."};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},5528:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>h});var d=c(687),e=c(3210),f=c(5814),g=c.n(f);function h(){let[a,b]=(0,e.useState)("overview"),c=[{id:1,name:"Employee Onboarding",description:"Automated new hire setup and account creation",status:"Active",triggers:12,lastRun:"2024-01-15 09:30",success:98.5},{id:2,name:"Expense Approval",description:"Auto-approve expenses under $500",status:"Active",triggers:45,lastRun:"2024-01-15 14:22",success:100},{id:3,name:"IT Equipment Request",description:"Automated equipment provisioning workflow",status:"Active",triggers:8,lastRun:"2024-01-14 16:45",success:95.2},{id:4,name:"Payroll Processing",description:"Monthly payroll calculation and distribution",status:"Scheduled",triggers:1,lastRun:"2023-12-31 23:59",success:100}],f=[{name:"Slack",status:"Connected",type:"Communication",lastSync:"2024-01-15 15:30"},{name:"Google Workspace",status:"Connected",type:"Productivity",lastSync:"2024-01-15 15:25"},{name:"Microsoft 365",status:"Connected",type:"Productivity",lastSync:"2024-01-15 15:20"},{name:"Salesforce",status:"Connected",type:"CRM",lastSync:"2024-01-15 15:15"},{name:"QuickBooks",status:"Connected",type:"Finance",lastSync:"2024-01-15 15:10"},{name:"Jira",status:"Error",type:"Project Management",lastSync:"2024-01-15 12:00"}],h=[{id:1,workflow:"Employee Onboarding",action:"Created accounts for Emily Johnson",timestamp:"2024-01-15 09:30",status:"Success"},{id:2,workflow:"Expense Approval",action:"Auto-approved $245 office supplies expense",timestamp:"2024-01-15 14:22",status:"Success"},{id:3,workflow:"IT Equipment Request",action:"Provisioned MacBook for new hire",timestamp:"2024-01-14 16:45",status:"Success"},{id:4,workflow:"Security Alert",action:"Detected suspicious login attempt",timestamp:"2024-01-14 11:20",status:"Warning"}];return(0,d.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,d.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,d.jsx)("div",{className:"px-4 sm:px-6 py-4",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0",children:[(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)(g(),{href:"/dashboard",className:"flex items-center mr-4",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-purple mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15 19l-7-7 7-7"})})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-navy",children:"Automation Center"}),(0,d.jsx)("p",{className:"text-gray-600 text-sm",children:"Manage workflows, integrations, and automated processes"})]})]}),(0,d.jsx)("button",{className:"bg-purple hover:bg-darkpurple text-white px-4 py-2 rounded-lg font-medium transition-colors w-full sm:w-auto",children:"Create Workflow"})]})})}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,d.jsx)("aside",{className:"w-full lg:w-64 bg-white shadow-sm lg:h-screen lg:sticky lg:top-0",children:(0,d.jsx)("nav",{className:"p-4 lg:p-6",children:(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("button",{onClick:()=>b("overview"),className:`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${"overview"===a?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"}`,children:[(0,d.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Overview"]}),(0,d.jsxs)("button",{onClick:()=>b("workflows"),className:`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${"workflows"===a?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"}`,children:[(0,d.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})}),"Workflows",(0,d.jsx)("span",{className:"ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:c.filter(a=>"Active"===a.status).length})]}),(0,d.jsxs)("button",{onClick:()=>b("integrations"),className:`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${"integrations"===a?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"}`,children:[(0,d.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"})}),"Integrations",(0,d.jsx)("span",{className:"ml-auto bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full",children:f.filter(a=>"Error"===a.status).length})]}),(0,d.jsxs)("button",{onClick:()=>b("rules"),className:`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${"rules"===a?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"}`,children:[(0,d.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})}),"Rules"]}),(0,d.jsxs)("button",{onClick:()=>b("activity"),className:`w-full flex items-center px-4 py-3 text-left rounded-lg transition-colors ${"activity"===a?"bg-purple/10 text-purple border-r-2 border-purple":"text-gray-600 hover:bg-gray-50"}`,children:[(0,d.jsx)("svg",{className:"h-5 w-5 mr-3",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"Activity Log"]})]})})}),(0,d.jsxs)("main",{className:"flex-1 p-4 lg:p-8",children:["overview"===a&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6",children:[(0,d.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Workflows"}),(0,d.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:c.filter(a=>"Active"===a.status).length}),(0,d.jsx)("p",{className:"text-sm text-green-600",children:"All running smoothly"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-green/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})})})]})}),(0,d.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Tasks Automated"}),(0,d.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"1,247"}),(0,d.jsx)("p",{className:"text-sm text-blue-600",children:"This month"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-blue/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,d.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Time Saved"}),(0,d.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"156h"}),(0,d.jsx)("p",{className:"text-sm text-purple",children:"This month"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-purple/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})]})}),(0,d.jsx)("div",{className:"bg-white p-4 lg:p-6 rounded-xl shadow-sm",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Success Rate"}),(0,d.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-navy",children:"98.4%"}),(0,d.jsx)("p",{className:"text-sm text-green-600",children:"Excellent"})]}),(0,d.jsx)("div",{className:"w-12 h-12 bg-orange/10 rounded-lg flex items-center justify-center",children:(0,d.jsx)("svg",{className:"h-6 w-6 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})})]})})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"Quick Actions"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,d.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-purple/10 rounded-lg flex items-center justify-center mb-3",children:(0,d.jsx)("svg",{className:"h-4 w-4 text-purple",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Create Workflow"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Build new automation"})]}),(0,d.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-blue/10 rounded-lg flex items-center justify-center mb-3",children:(0,d.jsx)("svg",{className:"h-4 w-4 text-blue-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 9l3 3-3 3m5 0h3"})})}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Add Integration"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Connect new service"})]}),(0,d.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-green/10 rounded-lg flex items-center justify-center mb-3",children:(0,d.jsx)("svg",{className:"h-4 w-4 text-green-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"})})}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"Create Rule"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Set up automation rule"})]}),(0,d.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-orange/10 rounded-lg flex items-center justify-center mb-3",children:(0,d.jsx)("svg",{className:"h-4 w-4 text-orange-500",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,d.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:"View Reports"}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Automation analytics"})]})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,d.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Recent Activity"})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:h.slice(0,5).map(a=>(0,d.jsx)("div",{className:"p-4 lg:p-6",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"Success"===a.status?"bg-green/10":"Warning"===a.status?"bg-orange/10":"bg-red/10"}`,children:(0,d.jsx)("svg",{className:`h-4 w-4 ${"Success"===a.status?"text-green-500":"Warning"===a.status?"text-orange-500":"text-red-500"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:"Success"===a.status?(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"}):(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.action}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[a.workflow," • ",a.timestamp]})]}),(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Success"===a.status?"bg-green-100 text-green-800":"Warning"===a.status?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:a.status})]})},a.id))})]})]}),"workflows"===a&&(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,d.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Automation Workflows"})}),(0,d.jsx)("div",{className:"lg:hidden divide-y divide-gray-200",children:c.map(a=>(0,d.jsxs)("div",{className:"p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:a.name}),(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Active"===a.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:a.status})]}),(0,d.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:a.description}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Triggers:"}),(0,d.jsx)("p",{className:"font-medium",children:a.triggers})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Success Rate:"}),(0,d.jsxs)("p",{className:"font-medium",children:[a.success,"%"]})]}),(0,d.jsxs)("div",{className:"col-span-2",children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Last Run:"}),(0,d.jsx)("p",{className:"font-medium",children:a.lastRun})]})]}),(0,d.jsxs)("div",{className:"mt-3 flex space-x-2",children:[(0,d.jsx)("button",{className:"flex-1 bg-purple/10 text-purple px-3 py-1 rounded text-sm font-medium",children:"Edit"}),(0,d.jsx)("button",{className:"flex-1 bg-gray-100 text-gray-600 px-3 py-1 rounded text-sm font-medium",children:"View Logs"})]})]},a.id))}),(0,d.jsx)("div",{className:"hidden lg:block overflow-x-auto",children:(0,d.jsxs)("table",{className:"w-full",children:[(0,d.jsx)("thead",{className:"bg-gray-50",children:(0,d.jsxs)("tr",{children:[(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Workflow"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Triggers"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Success Rate"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Run"}),(0,d.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,d.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:c.map(a=>(0,d.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-900",children:a.name}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:a.description})]})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Active"===a.status?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:a.status})}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.triggers}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:[a.success,"%"]}),(0,d.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:a.lastRun}),(0,d.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,d.jsx)("button",{className:"text-purple hover:text-darkpurple mr-3",children:"Edit"}),(0,d.jsx)("button",{className:"text-gray-400 hover:text-gray-600",children:"View Logs"})]})]},a.id))})]})})]})}),"integrations"===a&&(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,d.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"System Integrations"})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:f.map((a,b)=>(0,d.jsx)("div",{className:"p-4 lg:p-6",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center justify-between space-y-3 lg:space-y-0",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-500",children:[a.type," • Last sync: ",a.lastSync]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Connected"===a.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:a.status}),(0,d.jsx)("button",{className:"text-purple hover:text-darkpurple text-sm font-medium",children:"Connected"===a.status?"Configure":"Reconnect"})]})]})},b))})]})}),"rules"===a&&(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,d.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Automation Rules"})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:[{id:1,name:"Auto-assign IT equipment",condition:"New employee in Engineering department",action:"Create equipment request for laptop and monitor",enabled:!0},{id:2,name:"Expense auto-approval",condition:"Expense amount < $500 AND category = Office Supplies",action:"Automatically approve and process payment",enabled:!0},{id:3,name:"Time-off notification",condition:"Time-off request submitted",action:"Notify manager and update calendar",enabled:!0},{id:4,name:"Security alert",condition:"Failed login attempts > 5",action:"Lock account and notify IT security",enabled:!1}].map(a=>(0,d.jsx)("div",{className:"p-4 lg:p-6",children:(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-start justify-between space-y-3 lg:space-y-0",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,d.jsx)("h4",{className:"font-medium text-gray-900",children:a.name}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:a.enabled,className:"sr-only peer",readOnly:!0}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple"})]})]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 mb-1",children:[(0,d.jsx)("strong",{children:"When:"})," ",a.condition]}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[(0,d.jsx)("strong",{children:"Then:"})," ",a.action]})]}),(0,d.jsx)("button",{className:"text-purple hover:text-darkpurple text-sm font-medium",children:"Edit Rule"})]})},a.id))})]})}),"activity"===a&&(0,d.jsx)("div",{children:(0,d.jsxs)("div",{className:"bg-white rounded-xl shadow-sm overflow-hidden",children:[(0,d.jsx)("div",{className:"px-4 lg:px-6 py-4 border-b border-gray-200",children:(0,d.jsx)("h3",{className:"text-lg font-semibold text-navy",children:"Activity Log"})}),(0,d.jsx)("div",{className:"divide-y divide-gray-200",children:h.map(a=>(0,d.jsx)("div",{className:"p-4 lg:p-6",children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"Success"===a.status?"bg-green/10":"Warning"===a.status?"bg-orange/10":"bg-red/10"}`,children:(0,d.jsx)("svg",{className:`h-4 w-4 ${"Success"===a.status?"text-green-500":"Warning"===a.status?"text-orange-500":"text-red-500"}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:"Success"===a.status?(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 13l4 4L19 7"}):(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.action}),(0,d.jsxs)("p",{className:"text-xs text-gray-500",children:[a.workflow," • ",a.timestamp]})]}),(0,d.jsx)("span",{className:`px-2 py-1 text-xs rounded-full ${"Success"===a.status?"bg-green-100 text-green-800":"Warning"===a.status?"bg-orange-100 text-orange-800":"bg-red-100 text-red-800"}`,children:a.status})]})},a.id))})]})})]})]})]})}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6505:(a,b,c)=>{Promise.resolve().then(c.bind(c,5528))},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9757:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,400,814],()=>b(b.s=162));module.exports=c})();