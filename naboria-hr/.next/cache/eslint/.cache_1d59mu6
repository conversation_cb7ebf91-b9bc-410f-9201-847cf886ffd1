[{"/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/api-docs/page.tsx": "1", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/cookies/page.tsx": "2", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/page.tsx": "3", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/gdpr/page.tsx": "4", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/layout.tsx": "5", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/login/page.tsx": "6", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/page.tsx": "7", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/privacy/page.tsx": "8", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/signup/page.tsx": "9", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/terms/page.tsx": "10", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/AboutSection.tsx": "11", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/ContactSection.tsx": "12", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/Footer.tsx": "13", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/IntegrationsSection.tsx": "14", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/PricingSection.tsx": "15", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/lib/utils.ts": "16", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx": "17", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/finance/page.tsx": "18", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/hr/page.tsx": "19", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/it/page.tsx": "20", "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/payroll/page.tsx": "21"}, {"size": 16907, "mtime": 1752951181763, "results": "22", "hashOfConfig": "23"}, {"size": 9602, "mtime": 1752951580629, "results": "24", "hashOfConfig": "23"}, {"size": 22188, "mtime": 1752952835846, "results": "25", "hashOfConfig": "23"}, {"size": 11223, "mtime": 1752951637122, "results": "26", "hashOfConfig": "23"}, {"size": 834, "mtime": 1752949768101, "results": "27", "hashOfConfig": "23"}, {"size": 7975, "mtime": 1752951011439, "results": "28", "hashOfConfig": "23"}, {"size": 45876, "mtime": 1752951422617, "results": "29", "hashOfConfig": "23"}, {"size": 7615, "mtime": 1752951477426, "results": "30", "hashOfConfig": "23"}, {"size": 13153, "mtime": 1752951069503, "results": "31", "hashOfConfig": "23"}, {"size": 9036, "mtime": 1752951525355, "results": "32", "hashOfConfig": "23"}, {"size": 17784, "mtime": 1752950702987, "results": "33", "hashOfConfig": "23"}, {"size": 14469, "mtime": 1752950764919, "results": "34", "hashOfConfig": "23"}, {"size": 10358, "mtime": 1752951662058, "results": "35", "hashOfConfig": "23"}, {"size": 13065, "mtime": 1752950556019, "results": "36", "hashOfConfig": "23"}, {"size": 16006, "mtime": 1752950618786, "results": "37", "hashOfConfig": "23"}, {"size": 166, "mtime": 1752949630659, "results": "38", "hashOfConfig": "23"}, {"size": 31844, "mtime": 1752952554361, "results": "39", "hashOfConfig": "23"}, {"size": 28964, "mtime": 1752952435750, "results": "40", "hashOfConfig": "23"}, {"size": 23553, "mtime": 1752952116811, "results": "41", "hashOfConfig": "23"}, {"size": 28653, "mtime": 1752952324126, "results": "42", "hashOfConfig": "23"}, {"size": 24017, "mtime": 1752952212372, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vpf8oy", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/api-docs/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/cookies/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/gdpr/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/layout.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/login/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/privacy/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/signup/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/terms/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/AboutSection.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/ContactSection.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/Footer.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/IntegrationsSection.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/components/PricingSection.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/lib/utils.ts", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/automation/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/finance/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/hr/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/it/page.tsx", [], [], "/Volumes/Apps/Websites/Naboria HR/naboria-hr/src/app/dashboard/payroll/page.tsx", ["107", "108"], [], {"ruleId": "109", "severity": 1, "message": "110", "line": 8, "column": 10, "nodeType": null, "messageId": "111", "endLine": 8, "endColumn": 24}, {"ruleId": "109", "severity": 1, "message": "112", "line": 8, "column": 26, "nodeType": null, "messageId": "111", "endLine": 8, "endColumn": 43}, "@typescript-eslint/no-unused-vars", "'selectedPeriod' is assigned a value but never used.", "unusedVar", "'setSelectedPeriod' is assigned a value but never used."]