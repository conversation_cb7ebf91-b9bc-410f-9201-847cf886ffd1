/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10.5 17h1.227a2 2 0 0 0 1.345-.52L18 12", key: "16muxl" }],
  ["path", { d: "m12 13.5 3.75.5", key: "1i9qhk" }],
  ["path", { d: "m4.5 8 10.58-5.06a1 1 0 0 1 1.342.488L18.5 8", key: "12lg5p" }],
  ["path", { d: "M6 10V8", key: "1y41hn" }],
  ["path", { d: "M6 14v1", key: "cao2tf" }],
  ["path", { d: "M6 19v2", key: "1loha6" }],
  ["rect", { x: "2", y: "8", width: "20", height: "13", rx: "2", key: "p3bz5l" }]
];
const TicketsPlane = createLucideIcon("tickets-plane", __iconNode);

export { __iconNode, TicketsPlane as default };
//# sourceMappingURL=tickets-plane.js.map
